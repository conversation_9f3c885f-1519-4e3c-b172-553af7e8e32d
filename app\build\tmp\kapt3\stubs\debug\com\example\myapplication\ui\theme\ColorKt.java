package com.example.myapplication.ui.theme;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000\n\n\u0000\n\u0002\u0018\u0002\n\u0002\b.\"\u0013\u0010\u0000\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0002\u0010\u0003\"\u0013\u0010\u0005\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0006\u0010\u0003\"\u0013\u0010\u0007\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\b\u0010\u0003\"\u0013\u0010\t\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\n\u0010\u0003\"\u0013\u0010\u000b\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\f\u0010\u0003\"\u0013\u0010\r\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u000e\u0010\u0003\"\u0013\u0010\u000f\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0010\u0010\u0003\"\u0013\u0010\u0011\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0012\u0010\u0003\"\u0013\u0010\u0013\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0014\u0010\u0003\"\u0013\u0010\u0015\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0016\u0010\u0003\"\u0013\u0010\u0017\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0018\u0010\u0003\"\u0013\u0010\u0019\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u001a\u0010\u0003\"\u0013\u0010\u001b\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u001c\u0010\u0003\"\u0013\u0010\u001d\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u001e\u0010\u0003\"\u0013\u0010\u001f\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b \u0010\u0003\"\u0013\u0010!\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\"\u0010\u0003\"\u0013\u0010#\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b$\u0010\u0003\"\u0013\u0010%\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b&\u0010\u0003\"\u0013\u0010\'\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b(\u0010\u0003\"\u0013\u0010)\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b*\u0010\u0003\"\u0013\u0010+\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b,\u0010\u0003\"\u0013\u0010-\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b.\u0010\u0003\u00a8\u0006/"}, d2 = {"Background", "Landroidx/compose/ui/graphics/Color;", "getBackground", "()J", "J", "Error", "getError", "Info", "getInfo", "OnBackground", "getOnBackground", "OnPrimary", "getOnPrimary", "OnSecondary", "getOnSecondary", "OnSurface", "getOnSurface", "OnSurfaceVariant", "getOnSurfaceVariant", "Pink40", "getPink40", "Pink80", "getPink80", "Primary", "getPrimary", "PrimaryVariant", "getPrimaryVariant", "Purple40", "getPurple40", "Purple80", "getPurple80", "PurpleGrey40", "getPurpleGrey40", "PurpleGrey80", "getPurpleGrey80", "Secondary", "getSecondary", "SecondaryVariant", "getSecondaryVariant", "Success", "getSuccess", "Surface", "getSurface", "SurfaceVariant", "getSurfaceVariant", "Warning", "getWarning", "app_debug"})
public final class ColorKt {
    private static final long Primary = 0L;
    private static final long PrimaryVariant = 0L;
    private static final long Secondary = 0L;
    private static final long SecondaryVariant = 0L;
    private static final long Background = 0L;
    private static final long Surface = 0L;
    private static final long SurfaceVariant = 0L;
    private static final long OnPrimary = 0L;
    private static final long OnSecondary = 0L;
    private static final long OnBackground = 0L;
    private static final long OnSurface = 0L;
    private static final long OnSurfaceVariant = 0L;
    private static final long Success = 0L;
    private static final long Warning = 0L;
    private static final long Error = 0L;
    private static final long Info = 0L;
    private static final long Purple80 = 0L;
    private static final long PurpleGrey80 = 0L;
    private static final long Pink80 = 0L;
    private static final long Purple40 = 0L;
    private static final long PurpleGrey40 = 0L;
    private static final long Pink40 = 0L;
    
    public static final long getPrimary() {
        return 0L;
    }
    
    public static final long getPrimaryVariant() {
        return 0L;
    }
    
    public static final long getSecondary() {
        return 0L;
    }
    
    public static final long getSecondaryVariant() {
        return 0L;
    }
    
    public static final long getBackground() {
        return 0L;
    }
    
    public static final long getSurface() {
        return 0L;
    }
    
    public static final long getSurfaceVariant() {
        return 0L;
    }
    
    public static final long getOnPrimary() {
        return 0L;
    }
    
    public static final long getOnSecondary() {
        return 0L;
    }
    
    public static final long getOnBackground() {
        return 0L;
    }
    
    public static final long getOnSurface() {
        return 0L;
    }
    
    public static final long getOnSurfaceVariant() {
        return 0L;
    }
    
    public static final long getSuccess() {
        return 0L;
    }
    
    public static final long getWarning() {
        return 0L;
    }
    
    public static final long getError() {
        return 0L;
    }
    
    public static final long getInfo() {
        return 0L;
    }
    
    public static final long getPurple80() {
        return 0L;
    }
    
    public static final long getPurpleGrey80() {
        return 0L;
    }
    
    public static final long getPink80() {
        return 0L;
    }
    
    public static final long getPurple40() {
        return 0L;
    }
    
    public static final long getPurpleGrey40() {
        return 0L;
    }
    
    public static final long getPink40() {
        return 0L;
    }
}