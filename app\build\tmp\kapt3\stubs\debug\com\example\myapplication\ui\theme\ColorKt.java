package com.example.myapplication.ui.theme;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000\n\n\u0000\n\u0002\u0018\u0002\n\u0002\b^\"\u0013\u0010\u0000\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0002\u0010\u0003\"\u0013\u0010\u0005\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0006\u0010\u0003\"\u0013\u0010\u0007\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\b\u0010\u0003\"\u0013\u0010\t\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\n\u0010\u0003\"\u0013\u0010\u000b\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\f\u0010\u0003\"\u0013\u0010\r\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u000e\u0010\u0003\"\u0013\u0010\u000f\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0010\u0010\u0003\"\u0013\u0010\u0011\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0012\u0010\u0003\"\u0013\u0010\u0013\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0014\u0010\u0003\"\u0013\u0010\u0015\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0016\u0010\u0003\"\u0013\u0010\u0017\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0018\u0010\u0003\"\u0013\u0010\u0019\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u001a\u0010\u0003\"\u0013\u0010\u001b\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u001c\u0010\u0003\"\u0013\u0010\u001d\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u001e\u0010\u0003\"\u0013\u0010\u001f\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b \u0010\u0003\"\u0013\u0010!\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\"\u0010\u0003\"\u0013\u0010#\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b$\u0010\u0003\"\u0013\u0010%\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b&\u0010\u0003\"\u0013\u0010\'\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b(\u0010\u0003\"\u0013\u0010)\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b*\u0010\u0003\"\u0013\u0010+\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b,\u0010\u0003\"\u0013\u0010-\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b.\u0010\u0003\"\u0013\u0010/\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b0\u0010\u0003\"\u0013\u00101\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b2\u0010\u0003\"\u0013\u00103\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b4\u0010\u0003\"\u0013\u00105\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b6\u0010\u0003\"\u0013\u00107\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b8\u0010\u0003\"\u0013\u00109\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b:\u0010\u0003\"\u0013\u0010;\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b<\u0010\u0003\"\u0013\u0010=\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b>\u0010\u0003\"\u0013\u0010?\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b@\u0010\u0003\"\u0013\u0010A\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bB\u0010\u0003\"\u0013\u0010C\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bD\u0010\u0003\"\u0013\u0010E\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bF\u0010\u0003\"\u0013\u0010G\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bH\u0010\u0003\"\u0013\u0010I\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bJ\u0010\u0003\"\u0013\u0010K\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bL\u0010\u0003\"\u0013\u0010M\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bN\u0010\u0003\"\u0013\u0010O\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bP\u0010\u0003\"\u0013\u0010Q\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bR\u0010\u0003\"\u0013\u0010S\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bT\u0010\u0003\"\u0013\u0010U\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bV\u0010\u0003\"\u0013\u0010W\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bX\u0010\u0003\"\u0013\u0010Y\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bZ\u0010\u0003\"\u0013\u0010[\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\\\u0010\u0003\"\u0013\u0010]\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b^\u0010\u0003\u00a8\u0006_"}, d2 = {"Background", "Landroidx/compose/ui/graphics/Color;", "getBackground", "()J", "J", "DarkBackground", "getDarkBackground", "DarkOnBackground", "getDarkOnBackground", "DarkOnPrimary", "getDarkOnPrimary", "DarkOnPrimaryContainer", "getDarkOnPrimaryContainer", "DarkOnSecondary", "getDarkOnSecondary", "DarkOnSecondaryContainer", "getDarkOnSecondaryContainer", "DarkOnSurface", "getDarkOnSurface", "DarkOnSurfaceVariant", "getDarkOnSurfaceVariant", "DarkOutline", "getDarkOutline", "DarkOutlineVariant", "getDarkOutlineVariant", "DarkPrimary", "getDarkPrimary", "DarkPrimaryContainer", "getDarkPrimaryContainer", "DarkSecondary", "getDarkSecondary", "DarkSecondaryContainer", "getDarkSecondaryContainer", "DarkSurface", "getDarkSurface", "DarkSurfaceVariant", "getDarkSurfaceVariant", "Error", "getError", "ErrorContainer", "getErrorContainer", "ExpenseColor", "getExpenseColor", "IncomeColor", "getIncomeColor", "Info", "getInfo", "OnBackground", "getOnBackground", "OnErrorContainer", "getOnErrorContainer", "OnPrimary", "getOnPrimary", "OnPrimaryContainer", "getOnPrimaryContainer", "OnSecondary", "getOnSecondary", "OnSecondaryContainer", "getOnSecondaryContainer", "OnSurface", "getOnSurface", "OnSurfaceVariant", "getOnSurfaceVariant", "OnTertiaryContainer", "getOnTertiaryContainer", "Outline", "getOutline", "OutlineVariant", "getOutlineVariant", "Primary", "getPrimary", "PrimaryContainer", "getPrimaryContainer", "PrimaryVariant", "getPrimaryVariant", "SavingsColor", "getSavingsColor", "Secondary", "getSecondary", "SecondaryContainer", "getSecondaryContainer", "SecondaryVariant", "getSecondaryVariant", "Success", "getSuccess", "Surface", "getSurface", "SurfaceVariant", "getSurfaceVariant", "TertiaryContainer", "getTertiaryContainer", "TransferColor", "getTransferColor", "Warning", "getWarning", "app_debug"})
public final class ColorKt {
    private static final long Primary = 0L;
    private static final long PrimaryVariant = 0L;
    private static final long Secondary = 0L;
    private static final long SecondaryVariant = 0L;
    private static final long Background = 0L;
    private static final long Surface = 0L;
    private static final long SurfaceVariant = 0L;
    private static final long OnPrimary = 0L;
    private static final long OnSecondary = 0L;
    private static final long OnBackground = 0L;
    private static final long OnSurface = 0L;
    private static final long OnSurfaceVariant = 0L;
    private static final long Success = 0L;
    private static final long Warning = 0L;
    private static final long Error = 0L;
    private static final long Info = 0L;
    private static final long PrimaryContainer = 0L;
    private static final long OnPrimaryContainer = 0L;
    private static final long SecondaryContainer = 0L;
    private static final long OnSecondaryContainer = 0L;
    private static final long TertiaryContainer = 0L;
    private static final long OnTertiaryContainer = 0L;
    private static final long ErrorContainer = 0L;
    private static final long OnErrorContainer = 0L;
    private static final long Outline = 0L;
    private static final long OutlineVariant = 0L;
    private static final long DarkPrimary = 0L;
    private static final long DarkOnPrimary = 0L;
    private static final long DarkPrimaryContainer = 0L;
    private static final long DarkOnPrimaryContainer = 0L;
    private static final long DarkSecondary = 0L;
    private static final long DarkOnSecondary = 0L;
    private static final long DarkSecondaryContainer = 0L;
    private static final long DarkOnSecondaryContainer = 0L;
    private static final long DarkSurface = 0L;
    private static final long DarkOnSurface = 0L;
    private static final long DarkSurfaceVariant = 0L;
    private static final long DarkOnSurfaceVariant = 0L;
    private static final long DarkBackground = 0L;
    private static final long DarkOnBackground = 0L;
    private static final long DarkOutline = 0L;
    private static final long DarkOutlineVariant = 0L;
    private static final long IncomeColor = 0L;
    private static final long ExpenseColor = 0L;
    private static final long TransferColor = 0L;
    private static final long SavingsColor = 0L;
    
    public static final long getPrimary() {
        return 0L;
    }
    
    public static final long getPrimaryVariant() {
        return 0L;
    }
    
    public static final long getSecondary() {
        return 0L;
    }
    
    public static final long getSecondaryVariant() {
        return 0L;
    }
    
    public static final long getBackground() {
        return 0L;
    }
    
    public static final long getSurface() {
        return 0L;
    }
    
    public static final long getSurfaceVariant() {
        return 0L;
    }
    
    public static final long getOnPrimary() {
        return 0L;
    }
    
    public static final long getOnSecondary() {
        return 0L;
    }
    
    public static final long getOnBackground() {
        return 0L;
    }
    
    public static final long getOnSurface() {
        return 0L;
    }
    
    public static final long getOnSurfaceVariant() {
        return 0L;
    }
    
    public static final long getSuccess() {
        return 0L;
    }
    
    public static final long getWarning() {
        return 0L;
    }
    
    public static final long getError() {
        return 0L;
    }
    
    public static final long getInfo() {
        return 0L;
    }
    
    public static final long getPrimaryContainer() {
        return 0L;
    }
    
    public static final long getOnPrimaryContainer() {
        return 0L;
    }
    
    public static final long getSecondaryContainer() {
        return 0L;
    }
    
    public static final long getOnSecondaryContainer() {
        return 0L;
    }
    
    public static final long getTertiaryContainer() {
        return 0L;
    }
    
    public static final long getOnTertiaryContainer() {
        return 0L;
    }
    
    public static final long getErrorContainer() {
        return 0L;
    }
    
    public static final long getOnErrorContainer() {
        return 0L;
    }
    
    public static final long getOutline() {
        return 0L;
    }
    
    public static final long getOutlineVariant() {
        return 0L;
    }
    
    public static final long getDarkPrimary() {
        return 0L;
    }
    
    public static final long getDarkOnPrimary() {
        return 0L;
    }
    
    public static final long getDarkPrimaryContainer() {
        return 0L;
    }
    
    public static final long getDarkOnPrimaryContainer() {
        return 0L;
    }
    
    public static final long getDarkSecondary() {
        return 0L;
    }
    
    public static final long getDarkOnSecondary() {
        return 0L;
    }
    
    public static final long getDarkSecondaryContainer() {
        return 0L;
    }
    
    public static final long getDarkOnSecondaryContainer() {
        return 0L;
    }
    
    public static final long getDarkSurface() {
        return 0L;
    }
    
    public static final long getDarkOnSurface() {
        return 0L;
    }
    
    public static final long getDarkSurfaceVariant() {
        return 0L;
    }
    
    public static final long getDarkOnSurfaceVariant() {
        return 0L;
    }
    
    public static final long getDarkBackground() {
        return 0L;
    }
    
    public static final long getDarkOnBackground() {
        return 0L;
    }
    
    public static final long getDarkOutline() {
        return 0L;
    }
    
    public static final long getDarkOutlineVariant() {
        return 0L;
    }
    
    public static final long getIncomeColor() {
        return 0L;
    }
    
    public static final long getExpenseColor() {
        return 0L;
    }
    
    public static final long getTransferColor() {
        return 0L;
    }
    
    public static final long getSavingsColor() {
        return 0L;
    }
}