package com.example.myapplication.utils

import android.database.sqlite.SQLiteException
import java.io.IOException
import java.net.SocketTimeoutException
import java.net.UnknownHostException

object ErrorHandler {
    
    fun getErrorMessage(throwable: Throwable): String {
        return when (throwable) {
            is SQLiteException -> "Lỗi cơ sở dữ liệu: ${throwable.message}"
            is IOException -> "Lỗi kết nối: Vui lòng kiểm tra kết nối mạng"
            is SocketTimeoutException -> "Kết nối quá chậm: Vui lòng thử lại"
            is UnknownHostException -> "Không thể kết nối: Vui lòng kiểm tra kết nối mạng"
            is NumberFormatException -> "Định dạng số không hợp lệ"
            is IllegalArgumentException -> "Dữ liệu đầu vào không hợp lệ: ${throwable.message}"
            is IllegalStateException -> "Trạng thái ứng dụng không hợp lệ: ${throwable.message}"
            else -> "Đã xảy ra lỗi: ${throwable.message ?: "Lỗi không xác định"}"
        }
    }
    
    fun logError(tag: String, message: String, throwable: Throwable? = null) {
        // In a real app, you might use a logging library like Timber
        if (throwable != null) {
            android.util.Log.e(tag, message, throwable)
        } else {
            android.util.Log.e(tag, message)
        }
    }
    
    sealed class AppError(val message: String) {
        object NetworkError : AppError("Lỗi kết nối mạng")
        object DatabaseError : AppError("Lỗi cơ sở dữ liệu")
        object ValidationError : AppError("Dữ liệu không hợp lệ")
        object InsufficientFunds : AppError("Số dư không đủ")
        data class CustomError(val customMessage: String) : AppError(customMessage)
    }
}
