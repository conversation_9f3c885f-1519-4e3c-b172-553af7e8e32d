package com.example.myapplication.ui.screen.add_transaction;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000:\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0006\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010 \n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\u001a \u0010\u0000\u001a\u00020\u00012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u0005H\u0007\u001a\u0010\u0010\u0006\u001a\u00020\u00012\u0006\u0010\u0007\u001a\u00020\bH\u0003\u001a2\u0010\t\u001a\u00020\u00012\u0006\u0010\n\u001a\u00020\u000b2\f\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u000b0\r2\u0012\u0010\u000e\u001a\u000e\u0012\u0004\u0012\u00020\u000b\u0012\u0004\u0012\u00020\u00010\u000fH\u0003\u001a$\u0010\u0010\u001a\u00020\u00012\u0006\u0010\u0011\u001a\u00020\u00122\u0012\u0010\u0013\u001a\u000e\u0012\u0004\u0012\u00020\u0012\u0012\u0004\u0012\u00020\u00010\u000fH\u0003\u001a\u0010\u0010\u0014\u001a\u00020\u000b2\u0006\u0010\u0015\u001a\u00020\bH\u0002\u00a8\u0006\u0016"}, d2 = {"AddTransactionScreen", "", "onNavigateBack", "Lkotlin/Function0;", "viewModel", "Lcom/example/myapplication/ui/screen/add_transaction/AddTransactionViewModel;", "BalanceCard", "balance", "", "CategorySelector", "selectedCategory", "", "categories", "", "onCategorySelected", "Lkotlin/Function1;", "TransactionTypeSelector", "selectedType", "Lcom/example/myapplication/ui/screen/add_transaction/TransactionType;", "onTypeSelected", "formatCurrency", "amount", "app_debug"})
public final class AddTransactionScreenKt {
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void AddTransactionScreen(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateBack, @org.jetbrains.annotations.NotNull()
    com.example.myapplication.ui.screen.add_transaction.AddTransactionViewModel viewModel) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void BalanceCard(double balance) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void TransactionTypeSelector(com.example.myapplication.ui.screen.add_transaction.TransactionType selectedType, kotlin.jvm.functions.Function1<? super com.example.myapplication.ui.screen.add_transaction.TransactionType, kotlin.Unit> onTypeSelected) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void CategorySelector(java.lang.String selectedCategory, java.util.List<java.lang.String> categories, kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onCategorySelected) {
    }
    
    private static final java.lang.String formatCurrency(double amount) {
        return null;
    }
}