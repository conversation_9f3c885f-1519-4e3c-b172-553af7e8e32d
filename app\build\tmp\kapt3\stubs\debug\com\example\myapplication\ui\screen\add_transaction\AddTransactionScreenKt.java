package com.example.myapplication.ui.screen.add_transaction;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000@\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010 \n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0010\u0006\n\u0000\u001a \u0010\u0000\u001a\u00020\u00012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u0005H\u0007\u001a2\u0010\u0006\u001a\u00020\u00012\u0006\u0010\u0007\u001a\u00020\b2\f\u0010\t\u001a\b\u0012\u0004\u0012\u00020\b0\n2\u0012\u0010\u000b\u001a\u000e\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\u00010\fH\u0003\u001a$\u0010\r\u001a\u00020\u00012\u0006\u0010\u000e\u001a\u00020\u000f2\u0012\u0010\u0010\u001a\u000e\u0012\u0004\u0012\u00020\u000f\u0012\u0004\u0012\u00020\u00010\fH\u0003\u001a \u0010\u0011\u001a\u00020\u00012\b\u0010\u0012\u001a\u0004\u0018\u00010\u00132\f\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u00010\u0003H\u0003\u001aB\u0010\u0015\u001a\u00020\u00012\f\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\u00130\n2\b\u0010\u0012\u001a\u0004\u0018\u00010\u00132\u0012\u0010\u0017\u001a\u000e\u0012\u0004\u0012\u00020\u0013\u0012\u0004\u0012\u00020\u00010\f2\f\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\u00010\u0003H\u0003\u001a\u0010\u0010\u0019\u001a\u00020\b2\u0006\u0010\u001a\u001a\u00020\u001bH\u0002\u00a8\u0006\u001c"}, d2 = {"AddTransactionScreen", "", "onNavigateBack", "Lkotlin/Function0;", "viewModel", "Lcom/example/myapplication/ui/screen/add_transaction/AddTransactionViewModel;", "CategorySelector", "selectedCategory", "", "categories", "", "onCategorySelected", "Lkotlin/Function1;", "TransactionTypeSelector", "selectedType", "Lcom/example/myapplication/ui/screen/add_transaction/TransactionType;", "onTypeSelected", "WalletSelectionCard", "selectedWallet", "Lcom/example/myapplication/data/local/entity/WalletEntity;", "onWalletClick", "WalletSelectorDialog", "wallets", "onWalletSelected", "onDismiss", "formatCurrency", "amount", "", "app_debug"})
public final class AddTransactionScreenKt {
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void AddTransactionScreen(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateBack, @org.jetbrains.annotations.NotNull()
    com.example.myapplication.ui.screen.add_transaction.AddTransactionViewModel viewModel) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void WalletSelectionCard(com.example.myapplication.data.local.entity.WalletEntity selectedWallet, kotlin.jvm.functions.Function0<kotlin.Unit> onWalletClick) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void TransactionTypeSelector(com.example.myapplication.ui.screen.add_transaction.TransactionType selectedType, kotlin.jvm.functions.Function1<? super com.example.myapplication.ui.screen.add_transaction.TransactionType, kotlin.Unit> onTypeSelected) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void CategorySelector(java.lang.String selectedCategory, java.util.List<java.lang.String> categories, kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onCategorySelected) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void WalletSelectorDialog(java.util.List<com.example.myapplication.data.local.entity.WalletEntity> wallets, com.example.myapplication.data.local.entity.WalletEntity selectedWallet, kotlin.jvm.functions.Function1<? super com.example.myapplication.data.local.entity.WalletEntity, kotlin.Unit> onWalletSelected, kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss) {
    }
    
    private static final java.lang.String formatCurrency(double amount) {
        return null;
    }
}