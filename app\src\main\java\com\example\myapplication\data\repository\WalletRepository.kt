package com.example.myapplication.data.repository

import com.example.myapplication.data.local.dao.WalletDao
import com.example.myapplication.data.local.entity.WalletEntity
import kotlinx.coroutines.flow.Flow

class WalletRepository(private val walletDao: WalletDao) {

    fun getAllActiveWallets(): Flow<List<WalletEntity>> = walletDao.getAllActiveWallets()

    fun getAllWallets(): Flow<List<WalletEntity>> = walletDao.getAllWallets()

    fun getAvailableWallets(): Flow<List<WalletEntity>> = walletDao.getAvailableWallets()

    fun getFirstActiveWallet(): Flow<WalletEntity?> = walletDao.getFirstActiveWalletFlow()

    suspend fun getWalletById(id: Int): WalletEntity? = walletDao.getWalletById(id)

    suspend fun getFirstActiveWalletSync(): WalletEntity? = walletDao.getFirstActiveWallet()

    suspend fun getFirstAvailableWallet(): WalletEntity? = walletDao.getFirstAvailableWallet()

    suspend fun insertWallet(wallet: WalletEntity): Long = walletDao.insertWallet(wallet)

    suspend fun updateWallet(wallet: WalletEntity) = walletDao.updateWallet(wallet)

    suspend fun deleteWallet(wallet: WalletEntity) = walletDao.deleteWallet(wallet)

    suspend fun updateBalance(walletId: Int, amount: Double) =
        walletDao.updateBalance(walletId, amount, System.currentTimeMillis())

    suspend fun getBalance(walletId: Int): Double? = walletDao.getBalance(walletId)

    suspend fun getTotalBalance(): Double = walletDao.getTotalBalance() ?: 0.0

    suspend fun getActiveWalletCount(): Int = walletDao.getActiveWalletCount()

    fun searchWallets(searchQuery: String): Flow<List<WalletEntity>> =
        walletDao.searchWallets(searchQuery)

    suspend fun addMoney(walletId: Int, amount: Double): Boolean {
        if (amount <= 0) return false

        val wallet = walletDao.getWalletById(walletId)
        return if (wallet != null && wallet.isActive && !wallet.isFrozen) {
            walletDao.updateBalance(walletId, amount, System.currentTimeMillis())
            true
        } else {
            false
        }
    }

    suspend fun removeMoney(walletId: Int, amount: Double): Boolean {
        if (amount <= 0) return false

        val wallet = walletDao.getWalletById(walletId)
        if (wallet == null || !wallet.isActive || wallet.isFrozen) return false

        return if (wallet.balance >= amount) {
            walletDao.updateBalance(walletId, -amount, System.currentTimeMillis())
            true
        } else {
            false // Insufficient funds
        }
    }

    suspend fun freezeWallet(walletId: Int): Boolean {
        val wallet = walletDao.getWalletById(walletId)
        return if (wallet != null && wallet.isActive) {
            walletDao.updateFrozenStatus(walletId, true, System.currentTimeMillis())
            true
        } else {
            false
        }
    }

    suspend fun unfreezeWallet(walletId: Int): Boolean {
        val wallet = walletDao.getWalletById(walletId)
        return if (wallet != null && wallet.isActive) {
            walletDao.updateFrozenStatus(walletId, false, System.currentTimeMillis())
            true
        } else {
            false
        }
    }

    suspend fun deactivateWallet(walletId: Int): Boolean {
        val activeCount = walletDao.getActiveWalletCount()
        if (activeCount <= 1) return false // Don't allow deactivating the last wallet

        walletDao.deactivateWallet(walletId, System.currentTimeMillis())
        return true
    }

    suspend fun activateWallet(walletId: Int) {
        walletDao.activateWallet(walletId, System.currentTimeMillis())
    }

    suspend fun createWallet(
        name: String,
        initialBalance: Double = 0.0,
        currency: String = "VND",
        description: String? = null,
        color: String = "#1976D2",
        icon: String = "wallet"
    ): WalletEntity {
        val wallet = WalletEntity(
            name = name,
            balance = initialBalance,
            currency = currency,
            isActive = true,
            isFrozen = false,
            description = description,
            color = color,
            icon = icon,
            createdAt = System.currentTimeMillis(),
            updatedAt = System.currentTimeMillis()
        )
        val id = walletDao.insertWallet(wallet)
        return wallet.copy(id = id.toInt())
    }

    suspend fun createDefaultWalletsIfNeeded() {
        val activeCount = walletDao.getActiveWalletCount()
        if (activeCount == 0) {
            // Create default wallets
            val defaultWallets = listOf(
                WalletEntity(
                    name = "Ví tiền mặt",
                    balance = 0.0,
                    currency = "VND",
                    description = "Tiền mặt trong ví",
                    color = "#4CAF50",
                    icon = "wallet",
                    createdAt = System.currentTimeMillis(),
                    updatedAt = System.currentTimeMillis()
                ),
                WalletEntity(
                    name = "Tài khoản ngân hàng",
                    balance = 0.0,
                    currency = "VND",
                    description = "Tài khoản ngân hàng chính",
                    color = "#2196F3",
                    icon = "account_balance",
                    createdAt = System.currentTimeMillis(),
                    updatedAt = System.currentTimeMillis()
                )
            )

            defaultWallets.forEach { wallet ->
                walletDao.insertWallet(wallet)
            }
        }
    }

    suspend fun isWalletAvailableForTransaction(walletId: Int): Boolean {
        val wallet = walletDao.getWalletById(walletId)
        return wallet != null && wallet.isActive && !wallet.isFrozen
    }
}
