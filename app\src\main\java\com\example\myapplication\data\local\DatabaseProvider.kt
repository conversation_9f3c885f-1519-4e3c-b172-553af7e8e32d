package com.example.myapplication.data.local

import android.content.Context
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase

object DatabaseProvider {
    @Volatile
    private var INSTANCE: AppDatabase? = null

    private val MIGRATION_1_2 = object : Migration(1, 2) {
        override fun migrate(database: SupportSQLiteDatabase) {
            // Add new columns to wallets table
            database.execSQL("ALTER TABLE wallets ADD COLUMN currency TEXT NOT NULL DEFAULT 'VND'")
            database.execSQL("ALTER TABLE wallets ADD COLUMN isActive INTEGER NOT NULL DEFAULT 1")
            database.execSQL("ALTER TABLE wallets ADD COLUMN isFrozen INTEGER NOT NULL DEFAULT 0")
            database.execSQL("ALTER TABLE wallets ADD COLUMN createdAt INTEGER NOT NULL DEFAULT 0")
            database.execSQL("ALTER TABLE wallets ADD COLUMN updatedAt INTEGER NOT NULL DEFAULT 0")
            database.execSQL("ALTER TABLE wallets ADD COLUMN description TEXT")
            database.execSQL("ALTER TABLE wallets ADD COLUMN color TEXT NOT NULL DEFAULT '#1976D2'")
            database.execSQL("ALTER TABLE wallets ADD COLUMN icon TEXT NOT NULL DEFAULT 'wallet'")

            // Create categories table
            database.execSQL("""
                CREATE TABLE IF NOT EXISTS categories (
                    id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL,
                    name TEXT NOT NULL,
                    type TEXT NOT NULL,
                    icon TEXT NOT NULL DEFAULT 'category',
                    color TEXT NOT NULL DEFAULT '#757575',
                    isDefault INTEGER NOT NULL DEFAULT 0,
                    isActive INTEGER NOT NULL DEFAULT 1,
                    budgetLimit REAL,
                    createdAt INTEGER NOT NULL DEFAULT 0,
                    updatedAt INTEGER NOT NULL DEFAULT 0
                )
            """.trimIndent())

            // Add new columns to transactions table
            database.execSQL("ALTER TABLE transactions ADD COLUMN categoryId INTEGER")
            database.execSQL("ALTER TABLE transactions ADD COLUMN createdAt INTEGER NOT NULL DEFAULT 0")
            database.execSQL("ALTER TABLE transactions ADD COLUMN updatedAt INTEGER NOT NULL DEFAULT 0")

            // Since we can't add foreign key constraints to existing tables in SQLite,
            // we need to recreate the transactions table with the proper foreign keys

            // Create new transactions table with foreign keys
            database.execSQL("""
                CREATE TABLE IF NOT EXISTS transactions_new (
                    id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL,
                    walletId INTEGER NOT NULL,
                    categoryId INTEGER,
                    type TEXT NOT NULL,
                    category TEXT NOT NULL,
                    amount REAL NOT NULL,
                    note TEXT,
                    date INTEGER NOT NULL,
                    createdAt INTEGER NOT NULL,
                    updatedAt INTEGER,
                    FOREIGN KEY(walletId) REFERENCES wallets(id) ON DELETE CASCADE,
                    FOREIGN KEY(categoryId) REFERENCES categories(id) ON DELETE SET NULL
                )
            """.trimIndent())

            // Copy data from old table to new table
            database.execSQL("""
                INSERT INTO transactions_new (id, walletId, categoryId, type, category, amount, note, date, createdAt, updatedAt)
                SELECT id, walletId, categoryId, type, category, amount, note, date,
                       COALESCE(createdAt, date) as createdAt,
                       COALESCE(updatedAt, date) as updatedAt
                FROM transactions
            """.trimIndent())

            // Drop old table and rename new table
            database.execSQL("DROP TABLE transactions")
            database.execSQL("ALTER TABLE transactions_new RENAME TO transactions")

            // Update timestamps for existing records
            val currentTime = System.currentTimeMillis()
            database.execSQL("UPDATE wallets SET createdAt = $currentTime, updatedAt = $currentTime")

            // Insert default categories
            insertDefaultCategories(database)
        }
    }

    private val MIGRATION_2_3 = object : Migration(2, 3) {
        override fun migrate(database: SupportSQLiteDatabase) {
            // This migration fixes the foreign key constraints for transactions table
            // if they weren't properly created in the previous migration

            // Check if the foreign key constraints exist, if not, recreate the table
            database.execSQL("""
                CREATE TABLE IF NOT EXISTS transactions_temp (
                    id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL,
                    walletId INTEGER NOT NULL,
                    categoryId INTEGER,
                    type TEXT NOT NULL,
                    category TEXT NOT NULL,
                    amount REAL NOT NULL,
                    note TEXT,
                    date INTEGER NOT NULL,
                    createdAt INTEGER NOT NULL,
                    updatedAt INTEGER,
                    FOREIGN KEY(walletId) REFERENCES wallets(id) ON DELETE CASCADE,
                    FOREIGN KEY(categoryId) REFERENCES categories(id) ON DELETE SET NULL
                )
            """.trimIndent())

            // Copy data from existing table
            database.execSQL("""
                INSERT INTO transactions_temp (id, walletId, categoryId, type, category, amount, note, date, createdAt, updatedAt)
                SELECT id, walletId, categoryId, type, category, amount, note, date, createdAt, updatedAt
                FROM transactions
            """.trimIndent())

            // Drop old table and rename temp table
            database.execSQL("DROP TABLE transactions")
            database.execSQL("ALTER TABLE transactions_temp RENAME TO transactions")
        }
    }

    private fun insertDefaultCategories(database: SupportSQLiteDatabase) {
        val currentTime = System.currentTimeMillis()

        // Default expense categories
        val expenseCategories = listOf(
            "Ăn uống" to "restaurant",
            "Xăng xe" to "local_gas_station",
            "Mua sắm" to "shopping_cart",
            "Giải trí" to "movie",
            "Y tế" to "local_hospital",
            "Giáo dục" to "school",
            "Hóa đơn" to "receipt",
            "Du lịch" to "flight",
            "Quà tặng" to "card_giftcard",
            "Khác" to "category"
        )

        expenseCategories.forEach { (name, icon) ->
            database.execSQL("""
                INSERT INTO categories (name, type, icon, color, isDefault, isActive, createdAt, updatedAt)
                VALUES ('$name', 'expense', '$icon', '#F44336', 1, 1, $currentTime, $currentTime)
            """.trimIndent())
        }

        // Default income categories
        val incomeCategories = listOf(
            "Lương" to "work",
            "Thưởng" to "star",
            "Đầu tư" to "trending_up",
            "Bán hàng" to "store",
            "Freelance" to "computer",
            "Cho thuê" to "home",
            "Lãi suất" to "account_balance",
            "Quà tặng" to "card_giftcard",
            "Khác" to "category"
        )

        incomeCategories.forEach { (name, icon) ->
            database.execSQL("""
                INSERT INTO categories (name, type, icon, color, isDefault, isActive, createdAt, updatedAt)
                VALUES ('$name', 'income', '$icon', '#4CAF50', 1, 1, $currentTime, $currentTime)
            """.trimIndent())
        }
    }

    fun getDatabase(context: Context): AppDatabase {
        return INSTANCE ?: synchronized(this) {
            val instance = Room.databaseBuilder(
                context.applicationContext,
                AppDatabase::class.java,
                "wallet_database"
            )
                .addMigrations(MIGRATION_1_2, MIGRATION_2_3)
                .fallbackToDestructiveMigration() // For development, remove in production
                .addCallback(object : RoomDatabase.Callback() {
                    override fun onCreate(db: SupportSQLiteDatabase) {
                        super.onCreate(db)
                        // Database created successfully
                        android.util.Log.d("DatabaseProvider", "Database created successfully")
                    }

                    override fun onOpen(db: SupportSQLiteDatabase) {
                        super.onOpen(db)
                        // Database opened successfully
                        android.util.Log.d("DatabaseProvider", "Database opened successfully")

                        // Enable foreign key constraints
                        db.execSQL("PRAGMA foreign_keys=ON")
                    }
                })
                .build()
            INSTANCE = instance
            instance
        }
    }

    // For testing purposes - clears the database instance
    fun clearDatabase() {
        INSTANCE?.close()
        INSTANCE = null
    }

    // Check database health
    suspend fun checkDatabaseHealth(context: Context): Boolean {
        return try {
            val db = getDatabase(context)
            val walletCount = db.walletDao().getActiveWalletCount()
            val categoryCount = db.categoryDao().getAllCategoriesSync().size
            android.util.Log.d("DatabaseProvider", "Database health check: $walletCount wallets, $categoryCount categories")
            true
        } catch (e: Exception) {
            android.util.Log.e("DatabaseProvider", "Database health check failed", e)
            false
        }
    }
}
