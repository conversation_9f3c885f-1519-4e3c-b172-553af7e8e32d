package com.example.myapplication.data.local

import android.content.Context
import androidx.room.Room
import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase

object DatabaseProvider {
    @Volatile
    private var INSTANCE: AppDatabase? = null

    private val MIGRATION_1_2 = object : Migration(1, 2) {
        override fun migrate(database: SupportSQLiteDatabase) {
            // Add new columns to wallets table
            database.execSQL("ALTER TABLE wallets ADD COLUMN currency TEXT NOT NULL DEFAULT 'VND'")
            database.execSQL("ALTER TABLE wallets ADD COLUMN isActive INTEGER NOT NULL DEFAULT 1")
            database.execSQL("ALTER TABLE wallets ADD COLUMN isFrozen INTEGER NOT NULL DEFAULT 0")
            database.execSQL("ALTER TABLE wallets ADD COLUMN createdAt INTEGER NOT NULL DEFAULT 0")
            database.execSQL("ALTER TABLE wallets ADD COLUMN updatedAt INTEGER NOT NULL DEFAULT 0")
            database.execSQL("ALTER TABLE wallets ADD COLUMN description TEXT")
            database.execSQL("ALTER TABLE wallets ADD COLUMN color TEXT NOT NULL DEFAULT '#1976D2'")
            database.execSQL("ALTER TABLE wallets ADD COLUMN icon TEXT NOT NULL DEFAULT 'wallet'")

            // Create categories table
            database.execSQL("""
                CREATE TABLE IF NOT EXISTS categories (
                    id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL,
                    name TEXT NOT NULL,
                    type TEXT NOT NULL,
                    icon TEXT NOT NULL DEFAULT 'category',
                    color TEXT NOT NULL DEFAULT '#757575',
                    isDefault INTEGER NOT NULL DEFAULT 0,
                    isActive INTEGER NOT NULL DEFAULT 1,
                    budgetLimit REAL,
                    createdAt INTEGER NOT NULL DEFAULT 0,
                    updatedAt INTEGER NOT NULL DEFAULT 0
                )
            """.trimIndent())

            // Add new columns to transactions table
            database.execSQL("ALTER TABLE transactions ADD COLUMN categoryId INTEGER")
            database.execSQL("ALTER TABLE transactions ADD COLUMN createdAt INTEGER NOT NULL DEFAULT 0")
            database.execSQL("ALTER TABLE transactions ADD COLUMN updatedAt INTEGER NOT NULL DEFAULT 0")

            // Update timestamps for existing records
            val currentTime = System.currentTimeMillis()
            database.execSQL("UPDATE wallets SET createdAt = $currentTime, updatedAt = $currentTime")
            database.execSQL("UPDATE transactions SET createdAt = date, updatedAt = date")

            // Insert default categories
            insertDefaultCategories(database)
        }
    }

    private fun insertDefaultCategories(database: SupportSQLiteDatabase) {
        val currentTime = System.currentTimeMillis()

        // Default expense categories
        val expenseCategories = listOf(
            "Ăn uống" to "restaurant",
            "Xăng xe" to "local_gas_station",
            "Mua sắm" to "shopping_cart",
            "Giải trí" to "movie",
            "Y tế" to "local_hospital",
            "Giáo dục" to "school",
            "Hóa đơn" to "receipt",
            "Du lịch" to "flight",
            "Quà tặng" to "card_giftcard",
            "Khác" to "category"
        )

        expenseCategories.forEach { (name, icon) ->
            database.execSQL("""
                INSERT INTO categories (name, type, icon, color, isDefault, isActive, createdAt, updatedAt)
                VALUES ('$name', 'expense', '$icon', '#F44336', 1, 1, $currentTime, $currentTime)
            """.trimIndent())
        }

        // Default income categories
        val incomeCategories = listOf(
            "Lương" to "work",
            "Thưởng" to "star",
            "Đầu tư" to "trending_up",
            "Bán hàng" to "store",
            "Freelance" to "computer",
            "Cho thuê" to "home",
            "Lãi suất" to "account_balance",
            "Quà tặng" to "card_giftcard",
            "Khác" to "category"
        )

        incomeCategories.forEach { (name, icon) ->
            database.execSQL("""
                INSERT INTO categories (name, type, icon, color, isDefault, isActive, createdAt, updatedAt)
                VALUES ('$name', 'income', '$icon', '#4CAF50', 1, 1, $currentTime, $currentTime)
            """.trimIndent())
        }
    }

    fun getDatabase(context: Context): AppDatabase {
        return INSTANCE ?: synchronized(this) {
            val instance = Room.databaseBuilder(
                context.applicationContext,
                AppDatabase::class.java,
                "wallet_database"
            )
                .addMigrations(MIGRATION_1_2)
                .build()
            INSTANCE = instance
            instance
        }
    }
}
