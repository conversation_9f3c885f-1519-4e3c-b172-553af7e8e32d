package com.example.myapplication.ui.theme

import androidx.compose.ui.graphics.Color

// Primary colors - Modern blue theme
val Primary = Color(0xFF1976D2)
val PrimaryVariant = Color(0xFF1565C0)
val Secondary = Color(0xFF03DAC6)
val SecondaryVariant = Color(0xFF018786)

// Background colors
val Background = Color(0xFFFAFAFA)
val Surface = Color(0xFFFFFFFF)
val SurfaceVariant = Color(0xFFF5F5F5)

// Content colors
val OnPrimary = Color(0xFFFFFFFF)
val OnSecondary = Color(0xFF000000)
val OnBackground = Color(0xFF1C1B1F)
val OnSurface = Color(0xFF1C1B1F)
val OnSurfaceVariant = Color(0xFF49454F)

// Status colors
val Success = Color(0xFF4CAF50)
val Warning = Color(0xFFFF9800)
val Error = Color(0xFFF44336)
val Info = Color(0xFF2196F3)

// Material Design 3 Container Colors
val PrimaryContainer = Color(0xFFE3F2FD)
val OnPrimaryContainer = Color(0xFF0D47A1)
val SecondaryContainer = Color(0xFFE0F2F1)
val OnSecondaryContainer = Color(0xFF004D40)
val TertiaryContainer = Color(0xFFFFF3E0)
val OnTertiaryContainer = Color(0xFFE65100)
val ErrorContainer = Color(0xFFFFEBEE)
val OnErrorContainer = Color(0xFFB71C1C)

// Outline colors for Material Design 3
val Outline = Color(0xFF79747E)
val OutlineVariant = Color(0xFFCAC4D0)

// Dark theme colors - Material Design 3
val DarkPrimary = Color(0xFF90CAF9)
val DarkOnPrimary = Color(0xFF0D47A1)
val DarkPrimaryContainer = Color(0xFF1565C0)
val DarkOnPrimaryContainer = Color(0xFFE3F2FD)

val DarkSecondary = Color(0xFF4DB6AC)
val DarkOnSecondary = Color(0xFF004D40)
val DarkSecondaryContainer = Color(0xFF00695C)
val DarkOnSecondaryContainer = Color(0xFFE0F2F1)

val DarkSurface = Color(0xFF121212)
val DarkOnSurface = Color(0xFFE6E1E5)
val DarkSurfaceVariant = Color(0xFF1E1E1E)
val DarkOnSurfaceVariant = Color(0xFFCAC4D0)

val DarkBackground = Color(0xFF121212)
val DarkOnBackground = Color(0xFFE6E1E5)

val DarkOutline = Color(0xFF938F99)
val DarkOutlineVariant = Color(0xFF49454F)

// Semantic colors for financial app
val IncomeColor = Color(0xFF4CAF50)
val ExpenseColor = Color(0xFFF44336)
val TransferColor = Color(0xFF2196F3)
val SavingsColor = Color(0xFF9C27B0)