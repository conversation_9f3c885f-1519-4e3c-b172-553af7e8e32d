/ Header Record For PersistentHashMapValueStorage$ #androidx.activity.ComponentActivity androidx.room.RoomDatabase/ .com.example.myapplication.ui.navigation.Screen/ .com.example.myapplication.ui.navigation.Screen/ .com.example.myapplication.ui.navigation.Screen/ .com.example.myapplication.ui.navigation.Screen/ .com.example.myapplication.ui.navigation.Screen/ .com.example.myapplication.ui.navigation.Screen kotlin.Enum$ #androidx.lifecycle.AndroidViewModel kotlin.Enum$ #androidx.lifecycle.AndroidViewModel$ #androidx.lifecycle.AndroidViewModel$ #androidx.lifecycle.AndroidViewModel$ #androidx.lifecycle.AndroidViewModel$ #androidx.lifecycle.AndroidViewModel6 5com.example.myapplication.utils.ErrorHandler.AppError6 5com.example.myapplication.utils.ErrorHandler.AppError6 5com.example.myapplication.utils.ErrorHandler.AppError6 5com.example.myapplication.utils.ErrorHandler.AppError6 5com.example.myapplication.utils.ErrorHandler.AppError$ #androidx.activity.ComponentActivity androidx.room.RoomDatabase kotlin.Enum$ #androidx.lifecycle.AndroidViewModel$ #androidx.lifecycle.AndroidViewModel$ #androidx.activity.ComponentActivity$ #androidx.activity.ComponentActivity androidx.room.RoomDatabase