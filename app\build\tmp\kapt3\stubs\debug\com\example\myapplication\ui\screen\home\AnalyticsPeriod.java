package com.example.myapplication.ui.screen.home;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0000\n\u0002\u0010\u000e\n\u0002\b\b\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u000f\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006j\u0002\b\u0007j\u0002\b\bj\u0002\b\tj\u0002\b\n\u00a8\u0006\u000b"}, d2 = {"Lcom/example/myapplication/ui/screen/home/<USER>", "", "displayName", "", "(Ljava/lang/String;ILjava/lang/String;)V", "getDisplayName", "()Ljava/lang/String;", "THIS_WEEK", "THIS_MONTH", "THIS_YEAR", "ALL_TIME", "app_debug"})
public enum AnalyticsPeriod {
    /*public static final*/ THIS_WEEK /* = new THIS_WEEK(null) */,
    /*public static final*/ THIS_MONTH /* = new THIS_MONTH(null) */,
    /*public static final*/ THIS_YEAR /* = new THIS_YEAR(null) */,
    /*public static final*/ ALL_TIME /* = new ALL_TIME(null) */;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String displayName = null;
    
    AnalyticsPeriod(java.lang.String displayName) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getDisplayName() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<com.example.myapplication.ui.screen.home.AnalyticsPeriod> getEntries() {
        return null;
    }
}