package com.example.myapplication.ui.screen.add_transaction;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000B\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000b\n\u0002\b)\n\u0002\u0010\b\n\u0002\b\u0002\b\u0087\b\u0018\u00002\u00020\u0001B\u009b\u0001\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u0012\u000e\b\u0002\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00050\u0007\u0012\b\b\u0002\u0010\b\u001a\u00020\u0003\u0012\b\b\u0002\u0010\t\u001a\u00020\n\u0012\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\f\u0012\u000e\b\u0002\u0010\r\u001a\b\u0012\u0004\u0012\u00020\f0\u0007\u0012\b\b\u0002\u0010\u000e\u001a\u00020\u000f\u0012\b\b\u0002\u0010\u0010\u001a\u00020\u0011\u0012\n\b\u0002\u0010\u0012\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0013\u001a\u0004\u0018\u00010\u0003\u0012\b\b\u0002\u0010\u0014\u001a\u00020\u0011\u0012\b\b\u0002\u0010\u0015\u001a\u00020\u0011\u00a2\u0006\u0002\u0010\u0016J\t\u0010*\u001a\u00020\u0003H\u00c6\u0003J\u000b\u0010+\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010,\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\t\u0010-\u001a\u00020\u0011H\u00c6\u0003J\t\u0010.\u001a\u00020\u0011H\u00c6\u0003J\u000b\u0010/\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u000f\u00100\u001a\b\u0012\u0004\u0012\u00020\u00050\u0007H\u00c6\u0003J\t\u00101\u001a\u00020\u0003H\u00c6\u0003J\t\u00102\u001a\u00020\nH\u00c6\u0003J\u000b\u00103\u001a\u0004\u0018\u00010\fH\u00c6\u0003J\u000f\u00104\u001a\b\u0012\u0004\u0012\u00020\f0\u0007H\u00c6\u0003J\t\u00105\u001a\u00020\u000fH\u00c6\u0003J\t\u00106\u001a\u00020\u0011H\u00c6\u0003J\u009f\u0001\u00107\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u00052\u000e\b\u0002\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00050\u00072\b\b\u0002\u0010\b\u001a\u00020\u00032\b\b\u0002\u0010\t\u001a\u00020\n2\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\f2\u000e\b\u0002\u0010\r\u001a\b\u0012\u0004\u0012\u00020\f0\u00072\b\b\u0002\u0010\u000e\u001a\u00020\u000f2\b\b\u0002\u0010\u0010\u001a\u00020\u00112\n\b\u0002\u0010\u0012\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0013\u001a\u0004\u0018\u00010\u00032\b\b\u0002\u0010\u0014\u001a\u00020\u00112\b\b\u0002\u0010\u0015\u001a\u00020\u0011H\u00c6\u0001J\u0013\u00108\u001a\u00020\u00112\b\u00109\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010:\u001a\u00020;H\u00d6\u0001J\t\u0010<\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0018R\u0017\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00050\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u001aR\u0017\u0010\r\u001a\b\u0012\u0004\u0012\u00020\f0\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u001aR\u0013\u0010\u0012\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u0018R\u0011\u0010\u0010\u001a\u00020\u0011\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u001dR\u0011\u0010\b\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001e\u0010\u0018R\u0013\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001f\u0010 R\u0011\u0010\u000e\u001a\u00020\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b!\u0010\"R\u0011\u0010\t\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b#\u0010$R\u0013\u0010\u000b\u001a\u0004\u0018\u00010\f\u00a2\u0006\b\n\u0000\u001a\u0004\b%\u0010&R\u0011\u0010\u0015\u001a\u00020\u0011\u00a2\u0006\b\n\u0000\u001a\u0004\b\'\u0010\u001dR\u0011\u0010\u0014\u001a\u00020\u0011\u00a2\u0006\b\n\u0000\u001a\u0004\b(\u0010\u001dR\u0013\u0010\u0013\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b)\u0010\u0018\u00a8\u0006="}, d2 = {"Lcom/example/myapplication/ui/screen/add_transaction/AddTransactionUiState;", "", "amount", "", "selectedCategory", "Lcom/example/myapplication/data/local/entity/CategoryEntity;", "availableCategories", "", "note", "selectedType", "Lcom/example/myapplication/ui/screen/add_transaction/TransactionType;", "selectedWallet", "Lcom/example/myapplication/data/local/entity/WalletEntity;", "availableWallets", "selectedDateTime", "", "isLoading", "", "errorMessage", "successMessage", "showWalletSelector", "showCategorySelector", "(Ljava/lang/String;Lcom/example/myapplication/data/local/entity/CategoryEntity;Ljava/util/List;Ljava/lang/String;Lcom/example/myapplication/ui/screen/add_transaction/TransactionType;Lcom/example/myapplication/data/local/entity/WalletEntity;Ljava/util/List;JZLjava/lang/String;Ljava/lang/String;ZZ)V", "getAmount", "()Ljava/lang/String;", "getAvailableCategories", "()Ljava/util/List;", "getAvailableWallets", "getErrorMessage", "()Z", "getNote", "getSelectedCategory", "()Lcom/example/myapplication/data/local/entity/CategoryEntity;", "getSelectedDateTime", "()J", "getSelectedType", "()Lcom/example/myapplication/ui/screen/add_transaction/TransactionType;", "getSelectedWallet", "()Lcom/example/myapplication/data/local/entity/WalletEntity;", "getShowCategorySelector", "getShowWalletSelector", "getSuccessMessage", "component1", "component10", "component11", "component12", "component13", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "other", "hashCode", "", "toString", "app_debug"})
public final class AddTransactionUiState {
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String amount = null;
    @org.jetbrains.annotations.Nullable()
    private final com.example.myapplication.data.local.entity.CategoryEntity selectedCategory = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.example.myapplication.data.local.entity.CategoryEntity> availableCategories = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String note = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.myapplication.ui.screen.add_transaction.TransactionType selectedType = null;
    @org.jetbrains.annotations.Nullable()
    private final com.example.myapplication.data.local.entity.WalletEntity selectedWallet = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.example.myapplication.data.local.entity.WalletEntity> availableWallets = null;
    private final long selectedDateTime = 0L;
    private final boolean isLoading = false;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String errorMessage = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String successMessage = null;
    private final boolean showWalletSelector = false;
    private final boolean showCategorySelector = false;
    
    public AddTransactionUiState(@org.jetbrains.annotations.NotNull()
    java.lang.String amount, @org.jetbrains.annotations.Nullable()
    com.example.myapplication.data.local.entity.CategoryEntity selectedCategory, @org.jetbrains.annotations.NotNull()
    java.util.List<com.example.myapplication.data.local.entity.CategoryEntity> availableCategories, @org.jetbrains.annotations.NotNull()
    java.lang.String note, @org.jetbrains.annotations.NotNull()
    com.example.myapplication.ui.screen.add_transaction.TransactionType selectedType, @org.jetbrains.annotations.Nullable()
    com.example.myapplication.data.local.entity.WalletEntity selectedWallet, @org.jetbrains.annotations.NotNull()
    java.util.List<com.example.myapplication.data.local.entity.WalletEntity> availableWallets, long selectedDateTime, boolean isLoading, @org.jetbrains.annotations.Nullable()
    java.lang.String errorMessage, @org.jetbrains.annotations.Nullable()
    java.lang.String successMessage, boolean showWalletSelector, boolean showCategorySelector) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getAmount() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.example.myapplication.data.local.entity.CategoryEntity getSelectedCategory() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.myapplication.data.local.entity.CategoryEntity> getAvailableCategories() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getNote() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.myapplication.ui.screen.add_transaction.TransactionType getSelectedType() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.example.myapplication.data.local.entity.WalletEntity getSelectedWallet() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.myapplication.data.local.entity.WalletEntity> getAvailableWallets() {
        return null;
    }
    
    public final long getSelectedDateTime() {
        return 0L;
    }
    
    public final boolean isLoading() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getErrorMessage() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getSuccessMessage() {
        return null;
    }
    
    public final boolean getShowWalletSelector() {
        return false;
    }
    
    public final boolean getShowCategorySelector() {
        return false;
    }
    
    public AddTransactionUiState() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component1() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component10() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component11() {
        return null;
    }
    
    public final boolean component12() {
        return false;
    }
    
    public final boolean component13() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.example.myapplication.data.local.entity.CategoryEntity component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.myapplication.data.local.entity.CategoryEntity> component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component4() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.myapplication.ui.screen.add_transaction.TransactionType component5() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.example.myapplication.data.local.entity.WalletEntity component6() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.myapplication.data.local.entity.WalletEntity> component7() {
        return null;
    }
    
    public final long component8() {
        return 0L;
    }
    
    public final boolean component9() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.myapplication.ui.screen.add_transaction.AddTransactionUiState copy(@org.jetbrains.annotations.NotNull()
    java.lang.String amount, @org.jetbrains.annotations.Nullable()
    com.example.myapplication.data.local.entity.CategoryEntity selectedCategory, @org.jetbrains.annotations.NotNull()
    java.util.List<com.example.myapplication.data.local.entity.CategoryEntity> availableCategories, @org.jetbrains.annotations.NotNull()
    java.lang.String note, @org.jetbrains.annotations.NotNull()
    com.example.myapplication.ui.screen.add_transaction.TransactionType selectedType, @org.jetbrains.annotations.Nullable()
    com.example.myapplication.data.local.entity.WalletEntity selectedWallet, @org.jetbrains.annotations.NotNull()
    java.util.List<com.example.myapplication.data.local.entity.WalletEntity> availableWallets, long selectedDateTime, boolean isLoading, @org.jetbrains.annotations.Nullable()
    java.lang.String errorMessage, @org.jetbrains.annotations.Nullable()
    java.lang.String successMessage, boolean showWalletSelector, boolean showCategorySelector) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}