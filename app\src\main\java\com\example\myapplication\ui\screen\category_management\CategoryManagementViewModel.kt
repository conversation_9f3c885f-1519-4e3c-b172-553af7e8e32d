package com.example.myapplication.ui.screen.category_management

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.example.myapplication.data.local.entity.CategoryEntity
import com.example.myapplication.data.repository.CategoryRepository
import com.example.myapplication.data.repository.RepositoryProvider
import com.example.myapplication.utils.ErrorHandler
import com.example.myapplication.utils.ValidationUtils
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

data class CategoryManagementUiState(
    val expenseCategories: List<CategoryEntity> = emptyList(),
    val incomeCategories: List<CategoryEntity> = emptyList(),
    val selectedTab: CategoryType = CategoryType.EXPENSE,
    val isLoading: Boolean = false,
    val errorMessage: String? = null,
    val successMessage: String? = null,
    val showCreateCategoryDialog: Boolean = false,
    val showEditCategoryDialog: Boolean = false,
    val showDeleteConfirmDialog: Boolean = false,
    val selectedCategory: CategoryEntity? = null
)

data class CreateCategoryFormState(
    val name: String = "",
    val selectedIcon: String = "category",
    val selectedColor: String = "#757575",
    val budgetLimit: String = "",
    val hasBudgetLimit: Boolean = false
)

enum class CategoryType(val displayName: String, val value: String) {
    EXPENSE("Chi tiêu", "expense"),
    INCOME("Thu nhập", "income")
}

class CategoryManagementViewModel(application: Application) : AndroidViewModel(application) {
    
    private val categoryRepository: CategoryRepository = RepositoryProvider.provideCategoryRepository(application)
    
    private val _uiState = MutableStateFlow(CategoryManagementUiState())
    val uiState: StateFlow<CategoryManagementUiState> = _uiState.asStateFlow()
    
    private val _createFormState = MutableStateFlow(CreateCategoryFormState())
    val createFormState: StateFlow<CreateCategoryFormState> = _createFormState.asStateFlow()
    
    // Available icons for categories
    val availableIcons = listOf(
        "restaurant", "local_gas_station", "shopping_cart", "movie", "local_hospital",
        "school", "receipt", "flight", "card_giftcard", "work", "star", "trending_up",
        "store", "computer", "home", "account_balance", "category", "sports_esports",
        "fitness_center", "local_taxi", "phone", "wifi", "electric_bolt", "water_drop"
    )
    
    // Available colors for categories
    val availableColors = listOf(
        "#F44336", "#E91E63", "#9C27B0", "#673AB7", "#3F51B5", "#2196F3",
        "#03DAC6", "#4CAF50", "#8BC34A", "#CDDC39", "#FFEB3B", "#FFC107",
        "#FF9800", "#FF5722", "#795548", "#9E9E9E", "#607D8B", "#000000"
    )
    
    init {
        loadCategories()
    }
    
    private fun loadCategories() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true)
            try {
                categoryRepository.getCategoriesByType("expense").collect { expenseCategories ->
                    _uiState.value = _uiState.value.copy(
                        expenseCategories = expenseCategories,
                        isLoading = false,
                        errorMessage = null
                    )
                }
            } catch (e: Exception) {
                ErrorHandler.logError("CategoryManagementViewModel", "Error loading expense categories", e)
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    errorMessage = ErrorHandler.getErrorMessage(e)
                )
            }
        }
        
        viewModelScope.launch {
            try {
                categoryRepository.getCategoriesByType("income").collect { incomeCategories ->
                    _uiState.value = _uiState.value.copy(
                        incomeCategories = incomeCategories,
                        errorMessage = null
                    )
                }
            } catch (e: Exception) {
                ErrorHandler.logError("CategoryManagementViewModel", "Error loading income categories", e)
            }
        }
    }
    
    fun selectTab(tab: CategoryType) {
        _uiState.value = _uiState.value.copy(selectedTab = tab)
    }
    
    fun showCreateCategoryDialog() {
        _uiState.value = _uiState.value.copy(showCreateCategoryDialog = true)
        _createFormState.value = CreateCategoryFormState()
    }
    
    fun hideCreateCategoryDialog() {
        _uiState.value = _uiState.value.copy(showCreateCategoryDialog = false)
    }
    
    fun showEditCategoryDialog(category: CategoryEntity) {
        _uiState.value = _uiState.value.copy(
            showEditCategoryDialog = true,
            selectedCategory = category
        )
        _createFormState.value = CreateCategoryFormState(
            name = category.name,
            selectedIcon = category.icon,
            selectedColor = category.color,
            budgetLimit = category.budgetLimit?.toString() ?: "",
            hasBudgetLimit = category.budgetLimit != null
        )
    }
    
    fun hideEditCategoryDialog() {
        _uiState.value = _uiState.value.copy(
            showEditCategoryDialog = false,
            selectedCategory = null
        )
    }
    
    fun showDeleteConfirmDialog(category: CategoryEntity) {
        _uiState.value = _uiState.value.copy(
            showDeleteConfirmDialog = true,
            selectedCategory = category
        )
    }
    
    fun hideDeleteConfirmDialog() {
        _uiState.value = _uiState.value.copy(
            showDeleteConfirmDialog = false,
            selectedCategory = null
        )
    }
    
    fun updateFormName(name: String) {
        _createFormState.value = _createFormState.value.copy(name = ValidationUtils.sanitizeInput(name))
    }
    
    fun updateFormIcon(icon: String) {
        _createFormState.value = _createFormState.value.copy(selectedIcon = icon)
    }
    
    fun updateFormColor(color: String) {
        _createFormState.value = _createFormState.value.copy(selectedColor = color)
    }
    
    fun updateFormBudgetLimit(budgetLimit: String) {
        _createFormState.value = _createFormState.value.copy(budgetLimit = budgetLimit)
    }
    
    fun updateFormHasBudgetLimit(hasBudgetLimit: Boolean) {
        _createFormState.value = _createFormState.value.copy(
            hasBudgetLimit = hasBudgetLimit,
            budgetLimit = if (!hasBudgetLimit) "" else _createFormState.value.budgetLimit
        )
    }
    
    fun createCategory() {
        viewModelScope.launch {
            val formState = _createFormState.value
            val selectedTab = _uiState.value.selectedTab
            
            // Validation
            val nameValidation = ValidationUtils.validateCategory(formState.name)
            if (!nameValidation.isValid) {
                _uiState.value = _uiState.value.copy(errorMessage = nameValidation.errorMessage)
                return@launch
            }
            
            val budgetLimit = if (formState.hasBudgetLimit && formState.budgetLimit.isNotBlank()) {
                val budgetValidation = ValidationUtils.validateAmount(formState.budgetLimit)
                if (!budgetValidation.isValid) {
                    _uiState.value = _uiState.value.copy(errorMessage = budgetValidation.errorMessage)
                    return@launch
                }
                formState.budgetLimit.toDouble()
            } else null
            
            try {
                _uiState.value = _uiState.value.copy(isLoading = true)
                
                categoryRepository.createCustomCategory(
                    name = formState.name,
                    type = selectedTab.value,
                    icon = formState.selectedIcon,
                    color = formState.selectedColor,
                    budgetLimit = budgetLimit
                )
                
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    showCreateCategoryDialog = false,
                    successMessage = "Danh mục đã được tạo thành công"
                )
                
            } catch (e: Exception) {
                ErrorHandler.logError("CategoryManagementViewModel", "Error creating category", e)
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    errorMessage = ErrorHandler.getErrorMessage(e)
                )
            }
        }
    }
    
    fun updateCategory() {
        viewModelScope.launch {
            val formState = _createFormState.value
            val selectedCategory = _uiState.value.selectedCategory ?: return@launch
            
            // Validation
            val nameValidation = ValidationUtils.validateCategory(formState.name)
            if (!nameValidation.isValid) {
                _uiState.value = _uiState.value.copy(errorMessage = nameValidation.errorMessage)
                return@launch
            }
            
            val budgetLimit = if (formState.hasBudgetLimit && formState.budgetLimit.isNotBlank()) {
                val budgetValidation = ValidationUtils.validateAmount(formState.budgetLimit)
                if (!budgetValidation.isValid) {
                    _uiState.value = _uiState.value.copy(errorMessage = budgetValidation.errorMessage)
                    return@launch
                }
                formState.budgetLimit.toDouble()
            } else null
            
            try {
                _uiState.value = _uiState.value.copy(isLoading = true)
                
                val updatedCategory = selectedCategory.copy(
                    name = formState.name,
                    icon = formState.selectedIcon,
                    color = formState.selectedColor,
                    budgetLimit = budgetLimit,
                    updatedAt = System.currentTimeMillis()
                )
                
                categoryRepository.updateCategory(updatedCategory)
                
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    showEditCategoryDialog = false,
                    selectedCategory = null,
                    successMessage = "Danh mục đã được cập nhật thành công"
                )
                
            } catch (e: Exception) {
                ErrorHandler.logError("CategoryManagementViewModel", "Error updating category", e)
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    errorMessage = ErrorHandler.getErrorMessage(e)
                )
            }
        }
    }
    
    fun deleteCategory() {
        viewModelScope.launch {
            val selectedCategory = _uiState.value.selectedCategory ?: return@launch
            
            if (selectedCategory.isDefault) {
                _uiState.value = _uiState.value.copy(
                    errorMessage = "Không thể xóa danh mục mặc định"
                )
                return@launch
            }
            
            try {
                _uiState.value = _uiState.value.copy(isLoading = true)
                
                categoryRepository.deactivateCategory(selectedCategory.id)
                
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    showDeleteConfirmDialog = false,
                    selectedCategory = null,
                    successMessage = "Danh mục đã được xóa thành công"
                )
                
            } catch (e: Exception) {
                ErrorHandler.logError("CategoryManagementViewModel", "Error deleting category", e)
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    errorMessage = ErrorHandler.getErrorMessage(e)
                )
            }
        }
    }
    
    fun clearMessages() {
        _uiState.value = _uiState.value.copy(
            errorMessage = null,
            successMessage = null
        )
    }
}
