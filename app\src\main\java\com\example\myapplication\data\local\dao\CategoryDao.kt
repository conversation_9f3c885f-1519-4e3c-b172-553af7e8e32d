package com.example.myapplication.data.local.dao

import androidx.room.*
import com.example.myapplication.data.local.entity.CategoryEntity
import kotlinx.coroutines.flow.Flow

@Dao
interface CategoryDao {
    @Query("SELECT * FROM categories WHERE isActive = 1 ORDER BY name ASC")
    fun getAllActiveCategories(): Flow<List<CategoryEntity>>

    @Query("SELECT * FROM categories WHERE type = :type AND isActive = 1 ORDER BY name ASC")
    fun getCategoriesByType(type: String): Flow<List<CategoryEntity>>

    @Query("SELECT * FROM categories WHERE id = :id")
    suspend fun getCategoryById(id: Int): CategoryEntity?

    @Query("SELECT * FROM categories WHERE isDefault = 1")
    suspend fun getDefaultCategories(): List<CategoryEntity>

    @Query("SELECT * FROM categories WHERE type = :type AND isDefault = 1")
    suspend fun getDefaultCategoriesByType(type: String): List<CategoryEntity>

    @Insert
    suspend fun insertCategory(category: CategoryEntity): Long

    @Insert
    suspend fun insertCategories(categories: List<CategoryEntity>)

    @Update
    suspend fun updateCategory(category: CategoryEntity)

    @Delete
    suspend fun deleteCategory(category: CategoryEntity)

    @Query("UPDATE categories SET isActive = 0 WHERE id = :categoryId")
    suspend fun deactivateCategory(categoryId: Int)

    @Query("UPDATE categories SET isActive = 1 WHERE id = :categoryId")
    suspend fun activateCategory(categoryId: Int)

    @Query("SELECT COUNT(*) FROM categories WHERE type = :type AND isActive = 1")
    suspend fun getCategoryCountByType(type: String): Int

    @Query("SELECT * FROM categories WHERE name LIKE '%' || :searchQuery || '%' AND isActive = 1")
    fun searchCategories(searchQuery: String): Flow<List<CategoryEntity>>
}
