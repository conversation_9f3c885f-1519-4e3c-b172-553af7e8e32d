package com.example.myapplication.data.repository;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006J\u000e\u0010\u0007\u001a\u00020\b2\u0006\u0010\u0005\u001a\u00020\u0006\u00a8\u0006\t"}, d2 = {"Lcom/example/myapplication/data/repository/RepositoryProvider;", "", "()V", "provideTransactionRepository", "Lcom/example/myapplication/data/repository/TransactionRepository;", "context", "Landroid/content/Context;", "provideWalletRepository", "Lcom/example/myapplication/data/repository/WalletRepository;", "app_debug"})
public final class RepositoryProvider {
    @org.jetbrains.annotations.NotNull()
    public static final com.example.myapplication.data.repository.RepositoryProvider INSTANCE = null;
    
    private RepositoryProvider() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.myapplication.data.repository.WalletRepository provideWalletRepository(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.myapplication.data.repository.TransactionRepository provideTransactionRepository(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        return null;
    }
}