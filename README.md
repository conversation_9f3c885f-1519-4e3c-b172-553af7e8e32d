# Wallet Management & Expense Tracking App

A modern Android application built with Jetpack Compose for managing personal finances, including wallet balance management and expense tracking.

## Features

### 🏦 Wallet Management
- View current wallet balance prominently displayed
- Add money to wallet with transaction recording
- Remove money from wallet with validation
- Automatic balance updates with transaction history
- Prevents negative balance operations

### 💰 Expense Tracking
- Record expenses with automatic wallet deduction
- Record income with automatic wallet addition
- Categorized transactions (Food, Transport, Entertainment, etc.)
- Transaction history with date and time
- Input validation to prevent overspending
- Optional notes for each transaction

### 🎨 Modern UI/UX
- Material Design 3 components and theming
- Clean and intuitive user interface
- Professional color scheme and typography
- Responsive layout with proper spacing
- Bottom navigation for easy screen switching
- Floating Action Button for quick transaction entry

### 🔒 Data Management
- Room database for persistent storage
- Repository pattern for data access
- MVVM architecture with ViewModels
- Coroutines for asynchronous operations
- Comprehensive input validation and error handling

## Architecture

The app follows Clean Architecture principles with MVVM pattern:

```
├── data/
│   ├── local/
│   │   ├── dao/           # Data Access Objects
│   │   ├── entity/        # Room entities
│   │   └── AppDatabase.kt # Database configuration
│   └── repository/        # Repository implementations
├── domain/
│   ├── model/            # Domain models
│   └── usecase/          # Business logic
├── ui/
│   ├── navigation/       # Navigation setup
│   ├── screen/          # UI screens and ViewModels
│   └── theme/           # Material Design theming
└── utils/               # Utility classes
```

## Key Components

### Database Schema
- **WalletEntity**: Stores wallet information (id, name, balance)
- **TransactionEntity**: Stores transaction details (id, walletId, type, category, amount, note, date)

### ViewModels
- **HomeViewModel**: Manages home screen state and data
- **WalletViewModel**: Handles wallet operations and balance management
- **AddTransactionViewModel**: Manages transaction creation and validation

### Validation & Error Handling
- **ValidationUtils**: Comprehensive input validation
- **ErrorHandler**: Centralized error message handling
- Real-time validation feedback
- User-friendly error messages in Vietnamese

## Screens

### 🏠 Home Screen
- Displays current wallet balance
- Shows recent transaction history
- Quick access to wallet and transaction features
- Empty state with call-to-action

### 💳 Wallet Screen
- Prominent balance display
- Add/Remove money functionality
- Transaction statistics (total income/expenses)
- Modal dialogs for money operations

### ➕ Add Transaction Screen
- Transaction type selection (Income/Expense)
- Category selection with predefined options
- Amount input with validation
- Optional note field
- Real-time balance checking for expenses

## Technical Features

### Input Validation
- Amount validation (positive numbers, reasonable limits)
- Category validation (required, length limits)
- Note validation (optional, length limits)
- Balance validation for expenses
- Input sanitization

### Error Handling
- Database operation error handling
- Network error handling (for future features)
- User-friendly error messages
- Automatic error message clearing

### Testing
- Unit tests for validation utilities
- Unit tests for error handling
- Integration tests for database operations
- Comprehensive test coverage

## Dependencies

- **Jetpack Compose**: Modern UI toolkit
- **Room Database**: Local data persistence
- **Navigation Compose**: Screen navigation
- **Material Design 3**: UI components and theming
- **Coroutines**: Asynchronous programming
- **ViewModel**: UI state management

## Getting Started

1. Clone the repository
2. Open in Android Studio
3. Sync project with Gradle files
4. Run the app on an emulator or device

## Future Enhancements

- Multiple wallet support
- Transaction categories customization
- Data export/import functionality
- Charts and analytics
- Backup and sync features
- Dark theme support
- Biometric authentication

## License

This project is for educational and demonstration purposes.
