# Advanced Wallet Management & Expense Tracking App

A comprehensive Android application built with Jetpack Compose for managing personal finances with advanced multi-wallet support, custom categories, and modern UX features.

## ✨ Advanced Features

### 🏦 Multi-Wallet Management System
- **Multiple Wallet Support**: Create and manage multiple wallets (Cash, Bank Account, Savings, Credit Card)
- **Wallet Customization**: Custom names, colors, icons, and descriptions for each wallet
- **Wallet Status Management**: Freeze/unfreeze wallets to temporarily disable transactions
- **Balance Tracking**: Real-time balance updates across all wallets
- **Wallet Statistics**: Total balance and active wallet count
- **Visual Indicators**: Clear status indicators for frozen/active wallets

### 💰 Advanced Expense Tracking
- **Transaction Source Selection**: Choose which wallet to use for each transaction
- **Smart Validation**: Prevents transactions from frozen wallets and insufficient balances
- **Enhanced Transaction History**: Detailed transaction records with wallet information
- **Automatic Balance Management**: Real-time wallet balance updates
- **Transaction Categories**: Custom and default categories with icons and colors
- **Budget Limits**: Set spending limits for expense categories

### 🏷️ Category Management System
- **Custom Categories**: Create, edit, and delete custom expense and income categories
- **Category Icons & Colors**: Visual customization with predefined icon and color sets
- **Budget Management**: Set spending limits for expense categories
- **Default Categories**: Pre-configured categories for common expenses and income
- **Category Statistics**: Track usage and spending by category
- **Smart Organization**: Separate management for expense and income categories

### ⚙️ Comprehensive Settings
- **Account Preferences**: Default wallet, currency format settings
- **App Preferences**: Dark theme, notifications, language settings
- **Data Management**: Export data, backup functionality, clear all data
- **Security Settings**: App security and privacy controls
- **About Section**: App version, privacy policy, terms of service

### 🎨 Enhanced UI/UX
- **Material Design 3**: Latest design system with dynamic theming
- **Pull-to-Refresh**: Refresh data with intuitive pull gesture
- **Swipe Gestures**: Swipe-to-delete transactions with visual feedback
- **Loading States**: Shimmer effects and skeleton screens for better perceived performance
- **Accessibility**: Semantic descriptions and screen reader support
- **Visual Feedback**: Snackbars, animations, and status indicators
- **Responsive Design**: Optimized for different screen sizes and orientations

### 🔒 Advanced Data Management
- **Room Database**: Robust local storage with proper migrations
- **Repository Pattern**: Clean architecture with data abstraction
- **MVVM Architecture**: Reactive UI with ViewModels and StateFlow
- **Coroutines**: Asynchronous operations for smooth performance
- **Input Validation**: Comprehensive validation with user-friendly error messages
- **Error Handling**: Graceful error recovery and user feedback

## Architecture

The app follows Clean Architecture principles with MVVM pattern:

```
├── data/
│   ├── local/
│   │   ├── dao/           # Data Access Objects
│   │   ├── entity/        # Room entities
│   │   └── AppDatabase.kt # Database configuration
│   └── repository/        # Repository implementations
├── domain/
│   ├── model/            # Domain models
│   └── usecase/          # Business logic
├── ui/
│   ├── navigation/       # Navigation setup
│   ├── screen/          # UI screens and ViewModels
│   └── theme/           # Material Design theming
└── utils/               # Utility classes
```

## Key Components

### Database Schema
- **WalletEntity**: Stores wallet information (id, name, balance)
- **TransactionEntity**: Stores transaction details (id, walletId, type, category, amount, note, date)

### ViewModels
- **HomeViewModel**: Manages home screen state and data
- **WalletViewModel**: Handles wallet operations and balance management
- **AddTransactionViewModel**: Manages transaction creation and validation

### Validation & Error Handling
- **ValidationUtils**: Comprehensive input validation
- **ErrorHandler**: Centralized error message handling
- Real-time validation feedback
- User-friendly error messages in Vietnamese

## 📱 Application Screens

### 🏠 Home Screen
- **Smart Dashboard**: Overview of total balance across all wallets
- **Recent Transactions**: Latest transactions with swipe-to-delete functionality
- **Pull-to-Refresh**: Update data with intuitive gesture
- **Quick Actions**: Fast access to add transactions and wallet management
- **Loading States**: Shimmer effects for better user experience
- **Empty States**: Helpful guidance when no transactions exist

### 💳 Wallet Management Screen
- **Multi-Wallet Overview**: View all wallets with status indicators
- **Wallet Statistics**: Total balance and active wallet count
- **CRUD Operations**: Create, edit, delete, and manage wallet status
- **Visual Customization**: Custom colors, icons, and descriptions
- **Freeze/Unfreeze**: Temporarily disable wallets from transactions
- **Search & Filter**: Find wallets quickly (future enhancement)

### ➕ Add Transaction Screen
- **Wallet Selection**: Choose source wallet with balance display
- **Smart Validation**: Prevents overspending and frozen wallet usage
- **Category Management**: Select from custom or default categories
- **Transaction Types**: Income and expense with visual indicators
- **Real-time Feedback**: Instant validation and error messages
- **Enhanced UX**: Intuitive form layout with clear visual hierarchy

### 🏷️ Category Management Screen
- **Dual Categories**: Separate management for income and expense categories
- **Visual Customization**: Custom icons, colors, and names
- **Budget Limits**: Set spending limits for expense categories
- **Default Protection**: Prevent deletion of system default categories
- **Usage Statistics**: Track category usage and spending patterns
- **Bulk Operations**: Efficient category management tools

### ⚙️ Settings Screen
- **Organized Sections**: Account, app, data management, and about sections
- **Data Export**: Backup functionality for user data
- **Theme Controls**: Dark/light mode toggle
- **Notification Settings**: Manage app notifications
- **Data Management**: Clear data with confirmation dialogs
- **App Information**: Version, privacy policy, and terms access

## 🔧 Technical Implementation

### Advanced Architecture
- **Clean Architecture**: Separation of concerns with data, domain, and UI layers
- **MVVM Pattern**: Reactive UI with ViewModels and StateFlow
- **Repository Pattern**: Abstracted data access with proper dependency injection
- **Database Migrations**: Seamless schema updates with Room migrations
- **Coroutines**: Asynchronous operations for smooth performance

### Enhanced Database Schema
- **Multi-Wallet Support**: Extended wallet entity with status, colors, and metadata
- **Category System**: Custom category entities with icons, colors, and budget limits
- **Transaction Relationships**: Foreign key relationships with proper cascade handling
- **Data Integrity**: Constraints and validations at database level
- **Migration Strategy**: Backward-compatible schema updates

### Advanced Input Validation
- **Real-time Validation**: Instant feedback as users type
- **Comprehensive Checks**: Amount, category, note, and balance validations
- **Wallet Availability**: Prevents transactions from frozen or inactive wallets
- **Budget Enforcement**: Category spending limit validation
- **Input Sanitization**: Clean and normalize user inputs

### Robust Error Handling
- **Centralized Error Management**: Consistent error handling across the app
- **User-Friendly Messages**: Localized error messages in Vietnamese
- **Graceful Recovery**: Automatic retry mechanisms and fallback states
- **Logging System**: Comprehensive error logging for debugging
- **Snackbar Feedback**: Non-intrusive error and success notifications

### Performance Optimizations
- **Lazy Loading**: Efficient data loading with pagination support
- **Shimmer Effects**: Skeleton screens for better perceived performance
- **State Management**: Optimized StateFlow usage for reactive UI
- **Memory Management**: Proper lifecycle handling and resource cleanup
- **Database Indexing**: Optimized queries for fast data retrieval

### Testing & Quality Assurance
- **Unit Tests**: Comprehensive testing for ViewModels and repositories
- **Integration Tests**: Database operations and data flow testing
- **Validation Tests**: Input validation and error handling verification
- **UI Tests**: User interaction and navigation testing (future enhancement)
- **Code Quality**: Consistent coding standards and documentation

## Dependencies

- **Jetpack Compose**: Modern UI toolkit
- **Room Database**: Local data persistence
- **Navigation Compose**: Screen navigation
- **Material Design 3**: UI components and theming
- **Coroutines**: Asynchronous programming
- **ViewModel**: UI state management

## Getting Started

1. Clone the repository
2. Open in Android Studio
3. Sync project with Gradle files
4. Run the app on an emulator or device

## 🚀 Future Enhancements

### Analytics & Insights
- **Spending Analytics**: Charts and graphs for spending patterns
- **Budget Tracking**: Visual budget progress and alerts
- **Financial Goals**: Set and track savings goals
- **Expense Trends**: Monthly and yearly spending analysis
- **Category Insights**: Detailed breakdown by categories

### Advanced Features
- **Recurring Transactions**: Automatic recurring income/expenses
- **Transaction Search**: Advanced search and filtering capabilities
- **Data Sync**: Cloud backup and multi-device synchronization
- **Biometric Security**: Fingerprint and face unlock
- **Currency Exchange**: Multi-currency support with live rates

### User Experience
- **Widgets**: Home screen widgets for quick balance view
- **Notifications**: Smart spending alerts and reminders
- **Voice Input**: Voice-to-text for transaction notes
- **Offline Mode**: Full functionality without internet connection
- **Accessibility**: Enhanced screen reader and accessibility support

### Integration & Export
- **Bank Integration**: Connect with bank accounts (future API integration)
- **CSV/PDF Export**: Detailed financial reports
- **Tax Preparation**: Export data for tax filing
- **Third-party Apps**: Integration with popular financial apps
- **API Access**: RESTful API for external integrations

## License

This project is for educational and demonstration purposes.
