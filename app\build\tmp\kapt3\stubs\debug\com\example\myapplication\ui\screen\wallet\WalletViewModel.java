package com.example.myapplication.ui.screen.wallet;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000D\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u0006\n\u0000\n\u0002\u0010\u000e\n\u0002\b\t\b\u0007\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0018\u0010\u0010\u001a\u00020\u00112\u0006\u0010\u0012\u001a\u00020\u00132\b\b\u0002\u0010\u0014\u001a\u00020\u0015J\u0006\u0010\u0016\u001a\u00020\u0011J\u0006\u0010\u0017\u001a\u00020\u0011J\u0006\u0010\u0018\u001a\u00020\u0011J\b\u0010\u0019\u001a\u00020\u0011H\u0002J\b\u0010\u001a\u001a\u00020\u0011H\u0002J\u0018\u0010\u001b\u001a\u00020\u00112\u0006\u0010\u0012\u001a\u00020\u00132\b\b\u0002\u0010\u0014\u001a\u00020\u0015J\u0006\u0010\u001c\u001a\u00020\u0011J\u0006\u0010\u001d\u001a\u00020\u0011R\u0014\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00070\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u000e\u0010\u000e\u001a\u00020\u000fX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u001e"}, d2 = {"Lcom/example/myapplication/ui/screen/wallet/WalletViewModel;", "Landroidx/lifecycle/AndroidViewModel;", "application", "Landroid/app/Application;", "(Landroid/app/Application;)V", "_uiState", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/example/myapplication/ui/screen/wallet/WalletUiState;", "transactionRepository", "Lcom/example/myapplication/data/repository/TransactionRepository;", "uiState", "Lkotlinx/coroutines/flow/StateFlow;", "getUiState", "()Lkotlinx/coroutines/flow/StateFlow;", "walletRepository", "Lcom/example/myapplication/data/repository/WalletRepository;", "addMoney", "", "amount", "", "note", "", "clearError", "hideAddMoneyDialog", "hideRemoveMoneyDialog", "initializeWallet", "observeWalletData", "removeMoney", "showAddMoneyDialog", "showRemoveMoneyDialog", "app_debug"})
public final class WalletViewModel extends androidx.lifecycle.AndroidViewModel {
    @org.jetbrains.annotations.NotNull()
    private final com.example.myapplication.data.repository.WalletRepository walletRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.myapplication.data.repository.TransactionRepository transactionRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.example.myapplication.ui.screen.wallet.WalletUiState> _uiState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.example.myapplication.ui.screen.wallet.WalletUiState> uiState = null;
    
    public WalletViewModel(@org.jetbrains.annotations.NotNull()
    android.app.Application application) {
        super(null);
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.example.myapplication.ui.screen.wallet.WalletUiState> getUiState() {
        return null;
    }
    
    private final void initializeWallet() {
    }
    
    private final void observeWalletData() {
    }
    
    public final void showAddMoneyDialog() {
    }
    
    public final void hideAddMoneyDialog() {
    }
    
    public final void showRemoveMoneyDialog() {
    }
    
    public final void hideRemoveMoneyDialog() {
    }
    
    public final void addMoney(double amount, @org.jetbrains.annotations.NotNull()
    java.lang.String note) {
    }
    
    public final void removeMoney(double amount, @org.jetbrains.annotations.NotNull()
    java.lang.String note) {
    }
    
    public final void clearError() {
    }
}