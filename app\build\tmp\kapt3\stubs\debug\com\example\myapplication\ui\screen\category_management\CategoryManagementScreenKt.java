package com.example.myapplication.ui.screen.category_management;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000D\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0007\u001a,\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\f\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00010\u0005H\u0003\u001a\"\u0010\u0007\u001a\u00020\u00012\u000e\b\u0002\u0010\b\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\t\u001a\u00020\nH\u0007\u001a\u00bc\u0001\u0010\u000b\u001a\u00020\u00012\u0006\u0010\f\u001a\u00020\r2\u0006\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u00112\f\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00140\u00132\f\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u00140\u00132\u0012\u0010\u0016\u001a\u000e\u0012\u0004\u0012\u00020\u0014\u0012\u0004\u0012\u00020\u00010\u00172\u0012\u0010\u0018\u001a\u000e\u0012\u0004\u0012\u00020\u0014\u0012\u0004\u0012\u00020\u00010\u00172\u0012\u0010\u0019\u001a\u000e\u0012\u0004\u0012\u00020\u0014\u0012\u0004\u0012\u00020\u00010\u00172\u0012\u0010\u001a\u001a\u000e\u0012\u0004\u0012\u00020\u0014\u0012\u0004\u0012\u00020\u00010\u00172\u0012\u0010\u001b\u001a\u000e\u0012\u0004\u0012\u00020\r\u0012\u0004\u0012\u00020\u00010\u00172\f\u0010\u001c\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\f\u0010\u001d\u001a\b\u0012\u0004\u0012\u00020\u00010\u0005H\u0003\u00a8\u0006\u001e"}, d2 = {"CategoryItem", "", "category", "Lcom/example/myapplication/data/local/entity/CategoryEntity;", "onEditClick", "Lkotlin/Function0;", "onDeleteClick", "CategoryManagementScreen", "onNavigateBack", "viewModel", "Lcom/example/myapplication/ui/screen/category_management/CategoryManagementViewModel;", "CreateEditCategoryDialog", "isEdit", "", "categoryType", "Lcom/example/myapplication/ui/screen/category_management/CategoryType;", "formState", "Lcom/example/myapplication/ui/screen/category_management/CreateCategoryFormState;", "availableIcons", "", "", "availableColors", "onNameChange", "Lkotlin/Function1;", "onIconChange", "onColorChange", "onBudgetLimitChange", "onHasBudgetLimitChange", "onConfirm", "onDismiss", "app_debug"})
public final class CategoryManagementScreenKt {
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void CategoryManagementScreen(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateBack, @org.jetbrains.annotations.NotNull()
    com.example.myapplication.ui.screen.category_management.CategoryManagementViewModel viewModel) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void CategoryItem(com.example.myapplication.data.local.entity.CategoryEntity category, kotlin.jvm.functions.Function0<kotlin.Unit> onEditClick, kotlin.jvm.functions.Function0<kotlin.Unit> onDeleteClick) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void CreateEditCategoryDialog(boolean isEdit, com.example.myapplication.ui.screen.category_management.CategoryType categoryType, com.example.myapplication.ui.screen.category_management.CreateCategoryFormState formState, java.util.List<java.lang.String> availableIcons, java.util.List<java.lang.String> availableColors, kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onNameChange, kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onIconChange, kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onColorChange, kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onBudgetLimitChange, kotlin.jvm.functions.Function1<? super java.lang.Boolean, kotlin.Unit> onHasBudgetLimitChange, kotlin.jvm.functions.Function0<kotlin.Unit> onConfirm, kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss) {
    }
}