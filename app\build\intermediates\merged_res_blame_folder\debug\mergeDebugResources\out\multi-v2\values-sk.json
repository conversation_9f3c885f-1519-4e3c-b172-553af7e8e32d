{"logs": [{"outputFile": "com.example.myapplication.app-mergeDebugResources-54:/values-sk/values-sk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ba5970ab7047a5364a7daf3d65e8c0dd\\transformed\\core-1.13.1\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,354,452,562,670,792", "endColumns": "95,101,100,97,109,107,121,100", "endOffsets": "146,248,349,447,557,665,787,888"}, "to": {"startLines": "2,3,4,5,6,7,8,81", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,201,303,404,502,612,720,8321", "endColumns": "95,101,100,97,109,107,121,100", "endOffsets": "196,298,399,497,607,715,837,8417"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4d46e445b81c8f129aed0f2227fc7b91\\transformed\\material3-release\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,285,395,510,608,703,815,950,1066,1218,1303,1404,1496,1593,1709,1831,1937,2070,2203,2337,2501,2629,2753,2883,3003,3096,3193,3314,3437,3535,3638,3747,3888,4037,4146,4246,4330,4424,4519,4635,4722,4809,4910,4990,5076,5173,5276,5369,5466,5554,5659,5756,5855,5975,6055,6157", "endColumns": "114,114,109,114,97,94,111,134,115,151,84,100,91,96,115,121,105,132,132,133,163,127,123,129,119,92,96,120,122,97,102,108,140,148,108,99,83,93,94,115,86,86,100,79,85,96,102,92,96,87,104,96,98,119,79,101,92", "endOffsets": "165,280,390,505,603,698,810,945,1061,1213,1298,1399,1491,1588,1704,1826,1932,2065,2198,2332,2496,2624,2748,2878,2998,3091,3188,3309,3432,3530,3633,3742,3883,4032,4141,4241,4325,4419,4514,4630,4717,4804,4905,4985,5071,5168,5271,5364,5461,5549,5654,5751,5850,5970,6050,6152,6245"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1484,1599,1714,1824,1939,2037,2132,2244,2379,2495,2647,2732,2833,2925,3022,3138,3260,3366,3499,3632,3766,3930,4058,4182,4312,4432,4525,4622,4743,4866,4964,5067,5176,5317,5466,5575,5675,5759,5853,5948,6064,6151,6238,6339,6419,6505,6602,6705,6798,6895,6983,7088,7185,7284,7404,7484,7586", "endColumns": "114,114,109,114,97,94,111,134,115,151,84,100,91,96,115,121,105,132,132,133,163,127,123,129,119,92,96,120,122,97,102,108,140,148,108,99,83,93,94,115,86,86,100,79,85,96,102,92,96,87,104,96,98,119,79,101,92", "endOffsets": "1594,1709,1819,1934,2032,2127,2239,2374,2490,2642,2727,2828,2920,3017,3133,3255,3361,3494,3627,3761,3925,4053,4177,4307,4427,4520,4617,4738,4861,4959,5062,5171,5312,5461,5570,5670,5754,5848,5943,6059,6146,6233,6334,6414,6500,6597,6700,6793,6890,6978,7083,7180,7279,7399,7479,7581,7674"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f697d2cda204ad860889257d58a63850\\transformed\\foundation-release\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,139", "endColumns": "83,86", "endOffsets": "134,221"}, "to": {"startLines": "85,86", "startColumns": "4,4", "startOffsets": "8682,8766", "endColumns": "83,86", "endOffsets": "8761,8848"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\26eebbcdc50d214896ef44b17b2bbc06\\transformed\\ui-release\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,284,379,482,574,653,747,837,918,1001,1088,1160,1238,1314,1389,1467,1535", "endColumns": "94,83,94,102,91,78,93,89,80,82,86,71,77,75,74,77,67,113", "endOffsets": "195,279,374,477,569,648,742,832,913,996,1083,1155,1233,1309,1384,1462,1530,1644"}, "to": {"startLines": "9,10,11,12,13,14,15,73,74,75,76,77,78,79,80,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "842,937,1021,1116,1219,1311,1390,7679,7769,7850,7933,8020,8092,8170,8246,8422,8500,8568", "endColumns": "94,83,94,102,91,78,93,89,80,82,86,71,77,75,74,77,67,113", "endOffsets": "932,1016,1111,1214,1306,1385,1479,7764,7845,7928,8015,8087,8165,8241,8316,8495,8563,8677"}}]}]}