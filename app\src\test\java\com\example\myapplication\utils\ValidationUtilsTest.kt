package com.example.myapplication.utils

import org.junit.Test
import org.junit.Assert.*

class ValidationUtilsTest {

    @Test
    fun validateAmount_validAmount_returnsTrue() {
        val result = ValidationUtils.validateAmount("100.50")
        assertTrue(result.isValid)
        assertNull(result.errorMessage)
    }

    @Test
    fun validateAmount_emptyAmount_returnsFalse() {
        val result = ValidationUtils.validateAmount("")
        assertFalse(result.isValid)
        assertEquals("Vui lòng nhập số tiền", result.errorMessage)
    }

    @Test
    fun validateAmount_invalidAmount_returnsFalse() {
        val result = ValidationUtils.validateAmount("abc")
        assertFalse(result.isValid)
        assertEquals("Số tiền không hợp lệ", result.errorMessage)
    }

    @Test
    fun validateAmount_negativeAmount_returnsFalse() {
        val result = ValidationUtils.validateAmount("-100")
        assertFalse(result.isValid)
        assertEquals("Số tiền phải lớn hơn 0", result.errorMessage)
    }

    @Test
    fun validateAmount_zeroAmount_returnsFalse() {
        val result = ValidationUtils.validateAmount("0")
        assertFalse(result.isValid)
        assertEquals("Số tiền phải lớn hơn 0", result.errorMessage)
    }

    @Test
    fun validateAmount_tooLargeAmount_returnsFalse() {
        val result = ValidationUtils.validateAmount("1000000000")
        assertFalse(result.isValid)
        assertEquals("Số tiền quá lớn", result.errorMessage)
    }

    @Test
    fun validateCategory_validCategory_returnsTrue() {
        val result = ValidationUtils.validateCategory("Ăn uống")
        assertTrue(result.isValid)
        assertNull(result.errorMessage)
    }

    @Test
    fun validateCategory_emptyCategory_returnsFalse() {
        val result = ValidationUtils.validateCategory("")
        assertFalse(result.isValid)
        assertEquals("Vui lòng chọn danh mục", result.errorMessage)
    }

    @Test
    fun validateCategory_tooLongCategory_returnsFalse() {
        val longCategory = "a".repeat(51)
        val result = ValidationUtils.validateCategory(longCategory)
        assertFalse(result.isValid)
        assertEquals("Tên danh mục quá dài", result.errorMessage)
    }

    @Test
    fun validateNote_validNote_returnsTrue() {
        val result = ValidationUtils.validateNote("This is a valid note")
        assertTrue(result.isValid)
        assertNull(result.errorMessage)
    }

    @Test
    fun validateNote_emptyNote_returnsTrue() {
        val result = ValidationUtils.validateNote("")
        assertTrue(result.isValid)
        assertNull(result.errorMessage)
    }

    @Test
    fun validateNote_tooLongNote_returnsFalse() {
        val longNote = "a".repeat(201)
        val result = ValidationUtils.validateNote(longNote)
        assertFalse(result.isValid)
        assertEquals("Ghi chú quá dài (tối đa 200 ký tự)", result.errorMessage)
    }

    @Test
    fun validateExpenseAgainstBalance_sufficientBalance_returnsTrue() {
        val result = ValidationUtils.validateExpenseAgainstBalance(100.0, 200.0)
        assertTrue(result.isValid)
        assertNull(result.errorMessage)
    }

    @Test
    fun validateExpenseAgainstBalance_insufficientBalance_returnsFalse() {
        val result = ValidationUtils.validateExpenseAgainstBalance(200.0, 100.0)
        assertFalse(result.isValid)
        assertEquals("Số dư không đủ để thực hiện giao dịch này", result.errorMessage)
    }

    @Test
    fun sanitizeInput_trimAndNormalizeSpaces() {
        val result = ValidationUtils.sanitizeInput("  hello    world  ")
        assertEquals("hello world", result)
    }

    @Test
    fun formatCurrency_validAmount() {
        val result = ValidationUtils.formatCurrency(100000.0)
        assertTrue(result.contains("100"))
        assertTrue(result.contains("đ"))
    }

    @Test
    fun formatDate_validTimestamp() {
        val timestamp = System.currentTimeMillis()
        val result = ValidationUtils.formatDate(timestamp)
        assertNotNull(result)
        assertNotEquals("N/A", result)
    }
}
