package com.example.myapplication.ui.screen.wallet_management;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000L\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0010\u000e\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\n\n\u0002\u0018\u0002\n\u0002\b\u0011\b\u0007\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0006\u0010\u0019\u001a\u00020\u001aJ\u0006\u0010\u001b\u001a\u00020\u001aJ\u0006\u0010\u001c\u001a\u00020\u001aJ\u0006\u0010\u001c\u001a\u00020\u001aJ\u0006\u0010\u001d\u001a\u00020\u001aJ\u0006\u0010\u001e\u001a\u00020\u001aJ\u0006\u0010\u001f\u001a\u00020\u001aJ\b\u0010 \u001a\u00020\u001aH\u0002J\b\u0010!\u001a\u00020\u001aH\u0002J\u0006\u0010\"\u001a\u00020\u001aJ\u000e\u0010#\u001a\u00020\u001a2\u0006\u0010$\u001a\u00020%J\u000e\u0010&\u001a\u00020\u001a2\u0006\u0010$\u001a\u00020%J\u000e\u0010\'\u001a\u00020\u001a2\u0006\u0010$\u001a\u00020%J\u000e\u0010(\u001a\u00020\u001a2\u0006\u0010$\u001a\u00020%J\u000e\u0010)\u001a\u00020\u001a2\u0006\u0010*\u001a\u00020\fJ\u000e\u0010+\u001a\u00020\u001a2\u0006\u0010,\u001a\u00020\fJ\u000e\u0010-\u001a\u00020\u001a2\u0006\u0010.\u001a\u00020\fJ\u000e\u0010/\u001a\u00020\u001a2\u0006\u00100\u001a\u00020\fJ\u000e\u00101\u001a\u00020\u001a2\u0006\u00102\u001a\u00020\fJ\u000e\u00103\u001a\u00020\u001a2\u0006\u00104\u001a\u00020\fJ\u0006\u00105\u001a\u00020\u001aJ\u0006\u00105\u001a\u00020\u001aR\u0014\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\b\u001a\b\u0012\u0004\u0012\u00020\t0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\n\u001a\b\u0012\u0004\u0012\u00020\f0\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000eR\u0017\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\f0\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u000eR\u0017\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00070\u0012\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014R\u0017\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\t0\u0012\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0014R\u000e\u0010\u0017\u001a\u00020\u0018X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u00066"}, d2 = {"Lcom/example/myapplication/ui/screen/wallet_management/WalletManagementViewModel;", "Landroidx/lifecycle/AndroidViewModel;", "application", "Landroid/app/Application;", "(Landroid/app/Application;)V", "_createFormState", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/example/myapplication/ui/screen/wallet_management/CreateWalletFormState;", "_uiState", "Lcom/example/myapplication/ui/screen/wallet_management/WalletManagementUiState;", "availableColors", "", "", "getAvailableColors", "()Ljava/util/List;", "availableIcons", "getAvailableIcons", "createFormState", "Lkotlinx/coroutines/flow/StateFlow;", "getCreateFormState", "()Lkotlinx/coroutines/flow/StateFlow;", "uiState", "getUiState", "walletRepository", "Lcom/example/myapplication/data/repository/WalletRepository;", "clearMessages", "", "createWallet", "deleteWallet", "hideCreateWalletDialog", "hideDeleteConfirmDialog", "hideEditWalletDialog", "loadStatistics", "loadWallets", "showCreateWalletDialog", "showDeleteConfirmDialog", "wallet", "Lcom/example/myapplication/data/local/entity/WalletEntity;", "showEditWalletDialog", "toggleWalletFrozenStatus", "toggleWalletStatus", "updateFormBalance", "balance", "updateFormColor", "color", "updateFormCurrency", "currency", "updateFormDescription", "description", "updateFormIcon", "icon", "updateFormName", "name", "updateWallet", "app_debug"})
public final class WalletManagementViewModel extends androidx.lifecycle.AndroidViewModel {
    @org.jetbrains.annotations.NotNull()
    private final com.example.myapplication.data.repository.WalletRepository walletRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.example.myapplication.ui.screen.wallet_management.WalletManagementUiState> _uiState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.example.myapplication.ui.screen.wallet_management.WalletManagementUiState> uiState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.example.myapplication.ui.screen.wallet_management.CreateWalletFormState> _createFormState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.example.myapplication.ui.screen.wallet_management.CreateWalletFormState> createFormState = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<java.lang.String> availableColors = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<java.lang.String> availableIcons = null;
    
    public WalletManagementViewModel(@org.jetbrains.annotations.NotNull()
    android.app.Application application) {
        super(null);
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.example.myapplication.ui.screen.wallet_management.WalletManagementUiState> getUiState() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.example.myapplication.ui.screen.wallet_management.CreateWalletFormState> getCreateFormState() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> getAvailableColors() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> getAvailableIcons() {
        return null;
    }
    
    private final void loadWallets() {
    }
    
    private final void loadStatistics() {
    }
    
    public final void showCreateWalletDialog() {
    }
    
    public final void hideCreateWalletDialog() {
    }
    
    public final void showEditWalletDialog(@org.jetbrains.annotations.NotNull()
    com.example.myapplication.data.local.entity.WalletEntity wallet) {
    }
    
    public final void hideEditWalletDialog() {
    }
    
    public final void showDeleteConfirmDialog(@org.jetbrains.annotations.NotNull()
    com.example.myapplication.data.local.entity.WalletEntity wallet) {
    }
    
    public final void hideDeleteConfirmDialog() {
    }
    
    public final void updateFormName(@org.jetbrains.annotations.NotNull()
    java.lang.String name) {
    }
    
    public final void updateFormBalance(@org.jetbrains.annotations.NotNull()
    java.lang.String balance) {
    }
    
    public final void updateFormCurrency(@org.jetbrains.annotations.NotNull()
    java.lang.String currency) {
    }
    
    public final void updateFormDescription(@org.jetbrains.annotations.NotNull()
    java.lang.String description) {
    }
    
    public final void updateFormColor(@org.jetbrains.annotations.NotNull()
    java.lang.String color) {
    }
    
    public final void updateFormIcon(@org.jetbrains.annotations.NotNull()
    java.lang.String icon) {
    }
    
    public final void createWallet() {
    }
    
    public final void updateWallet() {
    }
    
    public final void deleteWallet() {
    }
    
    public final void toggleWalletStatus(@org.jetbrains.annotations.NotNull()
    com.example.myapplication.data.local.entity.WalletEntity wallet) {
    }
    
    public final void toggleWalletFrozenStatus(@org.jetbrains.annotations.NotNull()
    com.example.myapplication.data.local.entity.WalletEntity wallet) {
    }
    
    public final void clearMessages() {
    }
    
    public final void updateWallet() {
    }
    
    public final void deleteWallet() {
    }
}