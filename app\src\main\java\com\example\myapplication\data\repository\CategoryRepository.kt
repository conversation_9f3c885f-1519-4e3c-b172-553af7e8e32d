package com.example.myapplication.data.repository

import com.example.myapplication.data.local.dao.CategoryDao
import com.example.myapplication.data.local.entity.CategoryEntity
import kotlinx.coroutines.flow.Flow

class CategoryRepository(private val categoryDao: CategoryDao) {
    
    fun getAllActiveCategories(): Flow<List<CategoryEntity>> = categoryDao.getAllActiveCategories()
    
    fun getCategoriesByType(type: String): Flow<List<CategoryEntity>> = 
        categoryDao.getCategoriesByType(type)
    
    suspend fun getCategoryById(id: Int): CategoryEntity? = categoryDao.getCategoryById(id)
    
    suspend fun getDefaultCategories(): List<CategoryEntity> = categoryDao.getDefaultCategories()
    
    suspend fun getDefaultCategoriesByType(type: String): List<CategoryEntity> = 
        categoryDao.getDefaultCategoriesByType(type)
    
    suspend fun insertCategory(category: CategoryEntity): Long = categoryDao.insertCategory(category)
    
    suspend fun insertCategories(categories: List<CategoryEntity>) = 
        categoryDao.insertCategories(categories)
    
    suspend fun updateCategory(category: CategoryEntity) = categoryDao.updateCategory(category)
    
    suspend fun deleteCategory(category: CategoryEntity) = categoryDao.deleteCategory(category)
    
    suspend fun deactivateCategory(categoryId: Int) = categoryDao.deactivateCategory(categoryId)
    
    suspend fun activateCategory(categoryId: Int) = categoryDao.activateCategory(categoryId)
    
    suspend fun getCategoryCountByType(type: String): Int = 
        categoryDao.getCategoryCountByType(type)
    
    fun searchCategories(searchQuery: String): Flow<List<CategoryEntity>> = 
        categoryDao.searchCategories(searchQuery)
    
    suspend fun createCustomCategory(
        name: String,
        type: String,
        icon: String = "category",
        color: String = "#757575",
        budgetLimit: Double? = null
    ): CategoryEntity {
        val category = CategoryEntity(
            name = name,
            type = type,
            icon = icon,
            color = color,
            isDefault = false,
            isActive = true,
            budgetLimit = budgetLimit,
            createdAt = System.currentTimeMillis(),
            updatedAt = System.currentTimeMillis()
        )
        val id = categoryDao.insertCategory(category)
        return category.copy(id = id.toInt())
    }
    
    suspend fun updateCategoryBudget(categoryId: Int, budgetLimit: Double?) {
        val category = categoryDao.getCategoryById(categoryId)
        category?.let {
            val updatedCategory = it.copy(
                budgetLimit = budgetLimit,
                updatedAt = System.currentTimeMillis()
            )
            categoryDao.updateCategory(updatedCategory)
        }
    }
    
    suspend fun toggleCategoryStatus(categoryId: Int) {
        val category = categoryDao.getCategoryById(categoryId)
        category?.let {
            if (it.isActive) {
                categoryDao.deactivateCategory(categoryId)
            } else {
                categoryDao.activateCategory(categoryId)
            }
        }
    }
}
