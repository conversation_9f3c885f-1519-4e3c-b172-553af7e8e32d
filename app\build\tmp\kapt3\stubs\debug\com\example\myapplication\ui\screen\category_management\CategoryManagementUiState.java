package com.example.myapplication.ui.screen.category_management;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u000e\n\u0002\b\"\n\u0002\u0010\b\n\u0002\b\u0002\b\u0087\b\u0018\u00002\u00020\u0001B{\u0012\u000e\b\u0002\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u0012\u000e\b\u0002\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0007\u0012\b\b\u0002\u0010\b\u001a\u00020\t\u0012\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\u000b\u0012\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\u000b\u0012\b\b\u0002\u0010\r\u001a\u00020\t\u0012\b\b\u0002\u0010\u000e\u001a\u00020\t\u0012\b\b\u0002\u0010\u000f\u001a\u00020\t\u0012\n\b\u0002\u0010\u0010\u001a\u0004\u0018\u00010\u0004\u00a2\u0006\u0002\u0010\u0011J\u000f\u0010 \u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u00c6\u0003J\u000b\u0010!\u001a\u0004\u0018\u00010\u0004H\u00c6\u0003J\u000f\u0010\"\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u00c6\u0003J\t\u0010#\u001a\u00020\u0007H\u00c6\u0003J\t\u0010$\u001a\u00020\tH\u00c6\u0003J\u000b\u0010%\u001a\u0004\u0018\u00010\u000bH\u00c6\u0003J\u000b\u0010&\u001a\u0004\u0018\u00010\u000bH\u00c6\u0003J\t\u0010\'\u001a\u00020\tH\u00c6\u0003J\t\u0010(\u001a\u00020\tH\u00c6\u0003J\t\u0010)\u001a\u00020\tH\u00c6\u0003J\u007f\u0010*\u001a\u00020\u00002\u000e\b\u0002\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u000e\b\u0002\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\t2\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\u000b2\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\u000b2\b\b\u0002\u0010\r\u001a\u00020\t2\b\b\u0002\u0010\u000e\u001a\u00020\t2\b\b\u0002\u0010\u000f\u001a\u00020\t2\n\b\u0002\u0010\u0010\u001a\u0004\u0018\u00010\u0004H\u00c6\u0001J\u0013\u0010+\u001a\u00020\t2\b\u0010,\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010-\u001a\u00020.H\u00d6\u0001J\t\u0010/\u001a\u00020\u000bH\u00d6\u0001R\u0013\u0010\n\u001a\u0004\u0018\u00010\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0013R\u0017\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0015R\u0017\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0015R\u0011\u0010\b\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\u0017R\u0013\u0010\u0010\u001a\u0004\u0018\u00010\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0019R\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u001bR\u0011\u0010\r\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u0017R\u0011\u0010\u000f\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u0010\u0017R\u0011\u0010\u000e\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001e\u0010\u0017R\u0013\u0010\f\u001a\u0004\u0018\u00010\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001f\u0010\u0013\u00a8\u00060"}, d2 = {"Lcom/example/myapplication/ui/screen/category_management/CategoryManagementUiState;", "", "expenseCategories", "", "Lcom/example/myapplication/data/local/entity/CategoryEntity;", "incomeCategories", "selectedTab", "Lcom/example/myapplication/ui/screen/category_management/CategoryType;", "isLoading", "", "errorMessage", "", "successMessage", "showCreateCategoryDialog", "showEditCategoryDialog", "showDeleteConfirmDialog", "selectedCategory", "(Ljava/util/List;Ljava/util/List;Lcom/example/myapplication/ui/screen/category_management/CategoryType;ZLjava/lang/String;Ljava/lang/String;ZZZLcom/example/myapplication/data/local/entity/CategoryEntity;)V", "getErrorMessage", "()Ljava/lang/String;", "getExpenseCategories", "()Ljava/util/List;", "getIncomeCategories", "()Z", "getSelectedCategory", "()Lcom/example/myapplication/data/local/entity/CategoryEntity;", "getSelectedTab", "()Lcom/example/myapplication/ui/screen/category_management/CategoryType;", "getShowCreateCategoryDialog", "getShowDeleteConfirmDialog", "getShowEditCategoryDialog", "getSuccessMessage", "component1", "component10", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "other", "hashCode", "", "toString", "app_debug"})
public final class CategoryManagementUiState {
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.example.myapplication.data.local.entity.CategoryEntity> expenseCategories = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.example.myapplication.data.local.entity.CategoryEntity> incomeCategories = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.myapplication.ui.screen.category_management.CategoryType selectedTab = null;
    private final boolean isLoading = false;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String errorMessage = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String successMessage = null;
    private final boolean showCreateCategoryDialog = false;
    private final boolean showEditCategoryDialog = false;
    private final boolean showDeleteConfirmDialog = false;
    @org.jetbrains.annotations.Nullable()
    private final com.example.myapplication.data.local.entity.CategoryEntity selectedCategory = null;
    
    public CategoryManagementUiState(@org.jetbrains.annotations.NotNull()
    java.util.List<com.example.myapplication.data.local.entity.CategoryEntity> expenseCategories, @org.jetbrains.annotations.NotNull()
    java.util.List<com.example.myapplication.data.local.entity.CategoryEntity> incomeCategories, @org.jetbrains.annotations.NotNull()
    com.example.myapplication.ui.screen.category_management.CategoryType selectedTab, boolean isLoading, @org.jetbrains.annotations.Nullable()
    java.lang.String errorMessage, @org.jetbrains.annotations.Nullable()
    java.lang.String successMessage, boolean showCreateCategoryDialog, boolean showEditCategoryDialog, boolean showDeleteConfirmDialog, @org.jetbrains.annotations.Nullable()
    com.example.myapplication.data.local.entity.CategoryEntity selectedCategory) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.myapplication.data.local.entity.CategoryEntity> getExpenseCategories() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.myapplication.data.local.entity.CategoryEntity> getIncomeCategories() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.myapplication.ui.screen.category_management.CategoryType getSelectedTab() {
        return null;
    }
    
    public final boolean isLoading() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getErrorMessage() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getSuccessMessage() {
        return null;
    }
    
    public final boolean getShowCreateCategoryDialog() {
        return false;
    }
    
    public final boolean getShowEditCategoryDialog() {
        return false;
    }
    
    public final boolean getShowDeleteConfirmDialog() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.example.myapplication.data.local.entity.CategoryEntity getSelectedCategory() {
        return null;
    }
    
    public CategoryManagementUiState() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.myapplication.data.local.entity.CategoryEntity> component1() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.example.myapplication.data.local.entity.CategoryEntity component10() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.myapplication.data.local.entity.CategoryEntity> component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.myapplication.ui.screen.category_management.CategoryType component3() {
        return null;
    }
    
    public final boolean component4() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component5() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component6() {
        return null;
    }
    
    public final boolean component7() {
        return false;
    }
    
    public final boolean component8() {
        return false;
    }
    
    public final boolean component9() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.myapplication.ui.screen.category_management.CategoryManagementUiState copy(@org.jetbrains.annotations.NotNull()
    java.util.List<com.example.myapplication.data.local.entity.CategoryEntity> expenseCategories, @org.jetbrains.annotations.NotNull()
    java.util.List<com.example.myapplication.data.local.entity.CategoryEntity> incomeCategories, @org.jetbrains.annotations.NotNull()
    com.example.myapplication.ui.screen.category_management.CategoryType selectedTab, boolean isLoading, @org.jetbrains.annotations.Nullable()
    java.lang.String errorMessage, @org.jetbrains.annotations.Nullable()
    java.lang.String successMessage, boolean showCreateCategoryDialog, boolean showEditCategoryDialog, boolean showDeleteConfirmDialog, @org.jetbrains.annotations.Nullable()
    com.example.myapplication.data.local.entity.CategoryEntity selectedCategory) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}