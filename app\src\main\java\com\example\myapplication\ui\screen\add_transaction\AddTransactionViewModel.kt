package com.example.myapplication.ui.screen.add_transaction

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.example.myapplication.data.repository.RepositoryProvider
import com.example.myapplication.data.repository.TransactionRepository
import com.example.myapplication.data.repository.WalletRepository
import com.example.myapplication.utils.ErrorHandler
import com.example.myapplication.utils.ValidationUtils
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

data class AddTransactionUiState(
    val amount: String = "",
    val category: String = "",
    val note: String = "",
    val selectedType: TransactionType = TransactionType.EXPENSE,
    val isLoading: Boolean = false,
    val errorMessage: String? = null,
    val successMessage: String? = null,
    val availableBalance: Double = 0.0
)

enum class TransactionType(val displayName: String, val value: String) {
    EXPENSE("Chi tiêu", "expense"),
    INCOME("Thu nhập", "income")
}

class AddTransactionViewModel(application: Application) : AndroidViewModel(application) {

    private val walletRepository: WalletRepository = RepositoryProvider.provideWalletRepository(application)
    private val transactionRepository: TransactionRepository = RepositoryProvider.provideTransactionRepository(application)

    private val _uiState = MutableStateFlow(AddTransactionUiState())
    val uiState: StateFlow<AddTransactionUiState> = _uiState.asStateFlow()

    // Predefined categories
    val expenseCategories = listOf(
        "Ăn uống", "Xăng xe", "Mua sắm", "Giải trí", "Y tế",
        "Giáo dục", "Hóa đơn", "Du lịch", "Quà tặng", "Khác"
    )

    val incomeCategories = listOf(
        "Lương", "Thưởng", "Đầu tư", "Bán hàng", "Freelance",
        "Cho thuê", "Lãi suất", "Quà tặng", "Khác"
    )

    init {
        loadWalletBalance()
    }

    private fun loadWalletBalance() {
        viewModelScope.launch {
            try {
                val wallet = walletRepository.getWalletById(1) // Assuming default wallet ID is 1
                _uiState.value = _uiState.value.copy(
                    availableBalance = wallet?.balance ?: 0.0
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    errorMessage = "Lỗi tải thông tin ví: ${e.message}"
                )
            }
        }
    }

    fun updateAmount(amount: String) {
        val sanitizedAmount = ValidationUtils.sanitizeInput(amount)
        _uiState.value = _uiState.value.copy(amount = sanitizedAmount, errorMessage = null)
    }

    fun updateCategory(category: String) {
        val sanitizedCategory = ValidationUtils.sanitizeInput(category)
        _uiState.value = _uiState.value.copy(category = sanitizedCategory, errorMessage = null)
    }

    fun updateNote(note: String) {
        val sanitizedNote = ValidationUtils.sanitizeInput(note)
        val validation = ValidationUtils.validateNote(sanitizedNote)
        _uiState.value = _uiState.value.copy(
            note = sanitizedNote,
            errorMessage = if (!validation.isValid) validation.errorMessage else null
        )
    }

    fun updateTransactionType(type: TransactionType) {
        _uiState.value = _uiState.value.copy(
            selectedType = type,
            category = "", // Reset category when type changes
            errorMessage = null
        )
    }

    fun addTransaction() {
        viewModelScope.launch {
            val currentState = _uiState.value

            // Comprehensive validation
            val amountValidation = ValidationUtils.validateAmount(currentState.amount)
            if (!amountValidation.isValid) {
                _uiState.value = currentState.copy(errorMessage = amountValidation.errorMessage)
                return@launch
            }

            val categoryValidation = ValidationUtils.validateCategory(currentState.category)
            if (!categoryValidation.isValid) {
                _uiState.value = currentState.copy(errorMessage = categoryValidation.errorMessage)
                return@launch
            }

            val noteValidation = ValidationUtils.validateNote(currentState.note)
            if (!noteValidation.isValid) {
                _uiState.value = currentState.copy(errorMessage = noteValidation.errorMessage)
                return@launch
            }

            val amount = currentState.amount.toDouble()

            // Check balance for expenses
            if (currentState.selectedType == TransactionType.EXPENSE) {
                val balanceValidation = ValidationUtils.validateExpenseAgainstBalance(amount, currentState.availableBalance)
                if (!balanceValidation.isValid) {
                    _uiState.value = currentState.copy(errorMessage = balanceValidation.errorMessage)
                    return@launch
                }
            }

            _uiState.value = currentState.copy(isLoading = true, errorMessage = null)

            try {
                val walletId = 1 // Default wallet ID

                when (currentState.selectedType) {
                    TransactionType.EXPENSE -> {
                        // Deduct from wallet and add expense transaction
                        val success = walletRepository.removeMoney(walletId, amount)
                        if (success) {
                            transactionRepository.addExpense(
                                walletId = walletId,
                                category = currentState.category,
                                amount = amount,
                                note = currentState.note.ifBlank { null }
                            )
                        } else {
                            throw Exception("Không thể trừ tiền từ ví")
                        }
                    }
                    TransactionType.INCOME -> {
                        // Add to wallet and add income transaction
                        walletRepository.addMoney(walletId, amount)
                        transactionRepository.addIncome(
                            walletId = walletId,
                            category = currentState.category,
                            amount = amount,
                            note = currentState.note.ifBlank { null }
                        )
                    }
                }

                // Update balance and show success
                loadWalletBalance()
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    successMessage = "Giao dịch đã được thêm thành công",
                    amount = "",
                    category = "",
                    note = ""
                )

            } catch (e: Exception) {
                ErrorHandler.logError("AddTransactionViewModel", "Error adding transaction", e)
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    errorMessage = ErrorHandler.getErrorMessage(e)
                )
            }
        }
    }

    fun clearMessages() {
        _uiState.value = _uiState.value.copy(
            errorMessage = null,
            successMessage = null
        )
    }
}