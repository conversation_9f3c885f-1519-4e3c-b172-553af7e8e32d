package com.example.myapplication.ui.screen.add_transaction

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.example.myapplication.data.local.entity.CategoryEntity
import com.example.myapplication.data.local.entity.WalletEntity
import com.example.myapplication.data.repository.CategoryRepository
import com.example.myapplication.data.repository.RepositoryProvider
import com.example.myapplication.data.repository.TransactionRepository
import com.example.myapplication.data.repository.WalletRepository
import com.example.myapplication.utils.ErrorHandler
import com.example.myapplication.utils.ValidationUtils
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

data class AddTransactionUiState(
    val amount: String = "",
    val selectedCategory: CategoryEntity? = null,
    val availableCategories: List<CategoryEntity> = emptyList(),
    val note: String = "",
    val selectedType: TransactionType = TransactionType.EXPENSE,
    val selectedWallet: WalletEntity? = null,
    val availableWallets: List<WalletEntity> = emptyList(),
    val selectedDateTime: Long = System.currentTimeMillis(),
    val isLoading: Boolean = false,
    val errorMessage: String? = null,
    val successMessage: String? = null,
    val showWalletSelector: Boolean = false,
    val showCategorySelector: Boolean = false
)

enum class TransactionType(val displayName: String, val value: String) {
    EXPENSE("Chi tiêu", "expense"),
    INCOME("Thu nhập", "income")
}

class AddTransactionViewModel(application: Application) : AndroidViewModel(application) {

    private val walletRepository: WalletRepository = RepositoryProvider.provideWalletRepository(application)
    private val transactionRepository: TransactionRepository = RepositoryProvider.provideTransactionRepository(application)
    private val categoryRepository: CategoryRepository = RepositoryProvider.provideCategoryRepository(application)

    private val _uiState = MutableStateFlow(AddTransactionUiState())
    val uiState: StateFlow<AddTransactionUiState> = _uiState.asStateFlow()

    init {
        loadAvailableWallets()
        loadAvailableCategories()
    }

    private fun loadAvailableWallets() {
        viewModelScope.launch {
            try {
                walletRepository.getAvailableWallets().collect { wallets ->
                    val selectedWallet = _uiState.value.selectedWallet
                        ?: wallets.firstOrNull()

                    _uiState.value = _uiState.value.copy(
                        availableWallets = wallets,
                        selectedWallet = selectedWallet,
                        errorMessage = null
                    )
                }
            } catch (e: Exception) {
                ErrorHandler.logError("AddTransactionViewModel", "Error loading wallets", e)
                _uiState.value = _uiState.value.copy(
                    errorMessage = ErrorHandler.getErrorMessage(e)
                )
            }
        }
    }

    private fun loadAvailableCategories() {
        viewModelScope.launch {
            try {
                categoryRepository.getCategoriesByType(_uiState.value.selectedType.value).collect { categories ->
                    _uiState.value = _uiState.value.copy(
                        availableCategories = categories,
                        selectedCategory = categories.firstOrNull(),
                        errorMessage = null
                    )
                }
            } catch (e: Exception) {
                ErrorHandler.logError("AddTransactionViewModel", "Error loading categories", e)
                _uiState.value = _uiState.value.copy(
                    errorMessage = ErrorHandler.getErrorMessage(e)
                )
            }
        }
    }

    fun updateAmount(amount: String) {
        val sanitizedAmount = ValidationUtils.sanitizeInput(amount)
        _uiState.value = _uiState.value.copy(amount = sanitizedAmount, errorMessage = null)
    }

    fun selectCategory(category: CategoryEntity) {
        _uiState.value = _uiState.value.copy(
            selectedCategory = category,
            showCategorySelector = false,
            errorMessage = null
        )
    }

    fun showCategorySelector() {
        _uiState.value = _uiState.value.copy(showCategorySelector = true)
    }

    fun hideCategorySelector() {
        _uiState.value = _uiState.value.copy(showCategorySelector = false)
    }

    fun updateNote(note: String) {
        val sanitizedNote = ValidationUtils.sanitizeInput(note)
        val validation = ValidationUtils.validateNote(sanitizedNote)
        _uiState.value = _uiState.value.copy(
            note = sanitizedNote,
            errorMessage = if (!validation.isValid) validation.errorMessage else null
        )
    }

    fun updateDateTime(dateTime: Long) {
        _uiState.value = _uiState.value.copy(selectedDateTime = dateTime, errorMessage = null)
    }

    fun updateTransactionType(type: TransactionType) {
        _uiState.value = _uiState.value.copy(
            selectedType = type,
            selectedCategory = null, // Reset category when type changes
            errorMessage = null
        )
        // Reload categories for the new type
        loadCategoriesForType(type.value)
    }

    private fun loadCategoriesForType(type: String) {
        viewModelScope.launch {
            try {
                categoryRepository.getCategoriesByType(type).collect { categories ->
                    _uiState.value = _uiState.value.copy(
                        availableCategories = categories,
                        selectedCategory = categories.firstOrNull(),
                        errorMessage = null
                    )
                }
            } catch (e: Exception) {
                ErrorHandler.logError("AddTransactionViewModel", "Error loading categories for type $type", e)
            }
        }
    }

    fun selectWallet(wallet: WalletEntity) {
        _uiState.value = _uiState.value.copy(
            selectedWallet = wallet,
            showWalletSelector = false,
            errorMessage = null
        )
    }

    fun showWalletSelector() {
        _uiState.value = _uiState.value.copy(showWalletSelector = true)
    }

    fun hideWalletSelector() {
        _uiState.value = _uiState.value.copy(showWalletSelector = false)
    }

    fun addTransaction() {
        viewModelScope.launch {
            val currentState = _uiState.value

            // Comprehensive validation
            val amountValidation = ValidationUtils.validateAmount(currentState.amount)
            if (!amountValidation.isValid) {
                _uiState.value = currentState.copy(errorMessage = amountValidation.errorMessage)
                return@launch
            }

            // Validate category selection
            val selectedCategory = currentState.selectedCategory
            if (selectedCategory == null) {
                _uiState.value = currentState.copy(errorMessage = "Vui lòng chọn danh mục")
                return@launch
            }

            val noteValidation = ValidationUtils.validateNote(currentState.note)
            if (!noteValidation.isValid) {
                _uiState.value = currentState.copy(errorMessage = noteValidation.errorMessage)
                return@launch
            }

            val amount = currentState.amount.toDouble()

            // Validate wallet selection
            val selectedWallet = currentState.selectedWallet
            if (selectedWallet == null) {
                _uiState.value = currentState.copy(errorMessage = "Vui lòng chọn ví")
                return@launch
            }

            // Check if wallet is available for transactions
            if (!walletRepository.isWalletAvailableForTransaction(selectedWallet.id)) {
                _uiState.value = currentState.copy(errorMessage = "Ví đã bị khóa hoặc không khả dụng")
                return@launch
            }

            // Check balance for expenses
            if (currentState.selectedType == TransactionType.EXPENSE) {
                val balanceValidation = ValidationUtils.validateExpenseAgainstBalance(amount, selectedWallet.balance)
                if (!balanceValidation.isValid) {
                    _uiState.value = currentState.copy(errorMessage = balanceValidation.errorMessage)
                    return@launch
                }
            }

            _uiState.value = currentState.copy(isLoading = true, errorMessage = null)

            try {
                val walletId = selectedWallet.id

                when (currentState.selectedType) {
                    TransactionType.EXPENSE -> {
                        // Deduct from wallet and add expense transaction
                        val success = walletRepository.removeMoney(walletId, amount)
                        if (success) {
                            transactionRepository.addExpense(
                                walletId = walletId,
                                category = selectedCategory.name,
                                amount = amount,
                                note = currentState.note.ifBlank { null },
                                categoryId = selectedCategory.id,
                                date = currentState.selectedDateTime
                            )
                        } else {
                            throw Exception("Không thể trừ tiền từ ví")
                        }
                    }
                    TransactionType.INCOME -> {
                        // Add to wallet and add income transaction
                        val success = walletRepository.addMoney(walletId, amount)
                        if (success) {
                            transactionRepository.addIncome(
                                walletId = walletId,
                                category = selectedCategory.name,
                                amount = amount,
                                note = currentState.note.ifBlank { null },
                                categoryId = selectedCategory.id,
                                date = currentState.selectedDateTime
                            )
                        } else {
                            throw Exception("Không thể thêm tiền vào ví")
                        }
                    }
                }

                // Show success and reset form
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    successMessage = "Giao dịch đã được thêm thành công",
                    amount = "",
                    selectedCategory = _uiState.value.availableCategories.firstOrNull(),
                    note = ""
                )

            } catch (e: Exception) {
                ErrorHandler.logError("AddTransactionViewModel", "Error adding transaction", e)
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    errorMessage = ErrorHandler.getErrorMessage(e)
                )
            }
        }
    }

    fun clearMessages() {
        _uiState.value = _uiState.value.copy(
            errorMessage = null,
            successMessage = null
        )
    }
}