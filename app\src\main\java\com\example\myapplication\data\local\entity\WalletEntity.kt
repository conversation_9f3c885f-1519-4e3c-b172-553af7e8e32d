package com.example.myapplication.data.local.entity

import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "wallets")
data class WalletEntity(
    @PrimaryKey(autoGenerate = true) val id: Int = 0,
    val name: String,
    val balance: Double,
    val currency: String = "VND",
    val isActive: Boolean = true,
    val isFrozen: Boolean = false,
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis(),
    val description: String? = null,
    val color: String = "#1976D2", // Default blue color
    val icon: String = "wallet" // Default icon identifier
)
