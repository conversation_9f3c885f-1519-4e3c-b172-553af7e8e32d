package com.example.myapplication.ui.screen.home;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000>\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0006\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0012\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0087\b\u0018\u00002\u00020\u0001BY\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0003\u0012\u000e\b\u0002\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006\u0012\u000e\b\u0002\u0010\b\u001a\b\u0012\u0004\u0012\u00020\t0\u0006\u0012\u000e\b\u0002\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u000b0\u0006\u0012\u000e\b\u0002\u0010\f\u001a\b\u0012\u0004\u0012\u00020\t0\u0006\u00a2\u0006\u0002\u0010\rJ\t\u0010\u0016\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0017\u001a\u00020\u0003H\u00c6\u0003J\u000f\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006H\u00c6\u0003J\u000f\u0010\u0019\u001a\b\u0012\u0004\u0012\u00020\t0\u0006H\u00c6\u0003J\u000f\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\u000b0\u0006H\u00c6\u0003J\u000f\u0010\u001b\u001a\b\u0012\u0004\u0012\u00020\t0\u0006H\u00c6\u0003J]\u0010\u001c\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\u000e\b\u0002\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u00062\u000e\b\u0002\u0010\b\u001a\b\u0012\u0004\u0012\u00020\t0\u00062\u000e\b\u0002\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u000b0\u00062\u000e\b\u0002\u0010\f\u001a\b\u0012\u0004\u0012\u00020\t0\u0006H\u00c6\u0001J\u0013\u0010\u001d\u001a\u00020\u001e2\b\u0010\u001f\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010 \u001a\u00020!H\u00d6\u0001J\t\u0010\"\u001a\u00020#H\u00d6\u0001R\u0017\u0010\b\u001a\b\u0012\u0004\u0012\u00020\t0\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u0017\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u000fR\u0017\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u000b0\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u000fR\u0017\u0010\f\u001a\b\u0012\u0004\u0012\u00020\t0\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u000fR\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0014\u00a8\u0006$"}, d2 = {"Lcom/example/myapplication/ui/screen/home/<USER>", "", "totalIncome", "", "totalExpense", "monthlyTrend", "", "Lcom/example/myapplication/ui/screen/home/<USER>", "categoryBreakdown", "Lcom/example/myapplication/ui/screen/home/<USER>", "recentTransactions", "Lcom/example/myapplication/data/local/entity/TransactionEntity;", "topCategories", "(DDLjava/util/List;Ljava/util/List;Ljava/util/List;Ljava/util/List;)V", "getCategoryBreakdown", "()Ljava/util/List;", "getMonthlyTrend", "getRecentTransactions", "getTopCategories", "getTotalExpense", "()D", "getTotalIncome", "component1", "component2", "component3", "component4", "component5", "component6", "copy", "equals", "", "other", "hashCode", "", "toString", "", "app_debug"})
public final class SpendingAnalytics {
    private final double totalIncome = 0.0;
    private final double totalExpense = 0.0;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.example.myapplication.ui.screen.home.MonthlyData> monthlyTrend = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.example.myapplication.ui.screen.home.CategoryData> categoryBreakdown = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.example.myapplication.data.local.entity.TransactionEntity> recentTransactions = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.example.myapplication.ui.screen.home.CategoryData> topCategories = null;
    
    public SpendingAnalytics(double totalIncome, double totalExpense, @org.jetbrains.annotations.NotNull()
    java.util.List<com.example.myapplication.ui.screen.home.MonthlyData> monthlyTrend, @org.jetbrains.annotations.NotNull()
    java.util.List<com.example.myapplication.ui.screen.home.CategoryData> categoryBreakdown, @org.jetbrains.annotations.NotNull()
    java.util.List<com.example.myapplication.data.local.entity.TransactionEntity> recentTransactions, @org.jetbrains.annotations.NotNull()
    java.util.List<com.example.myapplication.ui.screen.home.CategoryData> topCategories) {
        super();
    }
    
    public final double getTotalIncome() {
        return 0.0;
    }
    
    public final double getTotalExpense() {
        return 0.0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.myapplication.ui.screen.home.MonthlyData> getMonthlyTrend() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.myapplication.ui.screen.home.CategoryData> getCategoryBreakdown() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.myapplication.data.local.entity.TransactionEntity> getRecentTransactions() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.myapplication.ui.screen.home.CategoryData> getTopCategories() {
        return null;
    }
    
    public SpendingAnalytics() {
        super();
    }
    
    public final double component1() {
        return 0.0;
    }
    
    public final double component2() {
        return 0.0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.myapplication.ui.screen.home.MonthlyData> component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.myapplication.ui.screen.home.CategoryData> component4() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.myapplication.data.local.entity.TransactionEntity> component5() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.myapplication.ui.screen.home.CategoryData> component6() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.myapplication.ui.screen.home.SpendingAnalytics copy(double totalIncome, double totalExpense, @org.jetbrains.annotations.NotNull()
    java.util.List<com.example.myapplication.ui.screen.home.MonthlyData> monthlyTrend, @org.jetbrains.annotations.NotNull()
    java.util.List<com.example.myapplication.ui.screen.home.CategoryData> categoryBreakdown, @org.jetbrains.annotations.NotNull()
    java.util.List<com.example.myapplication.data.local.entity.TransactionEntity> recentTransactions, @org.jetbrains.annotations.NotNull()
    java.util.List<com.example.myapplication.ui.screen.home.CategoryData> topCategories) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}