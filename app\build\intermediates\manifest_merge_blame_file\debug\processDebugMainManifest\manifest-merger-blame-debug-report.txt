1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.myapplication"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <permission
11-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ba5970ab7047a5364a7daf3d65e8c0dd\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
12        android:name="com.example.myapplication.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
12-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ba5970ab7047a5364a7daf3d65e8c0dd\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
13        android:protectionLevel="signature" />
13-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ba5970ab7047a5364a7daf3d65e8c0dd\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
14
15    <uses-permission android:name="com.example.myapplication.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
15-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ba5970ab7047a5364a7daf3d65e8c0dd\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
15-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ba5970ab7047a5364a7daf3d65e8c0dd\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
16
17    <application
17-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:5:5-25:19
18        android:allowBackup="true"
18-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:6:9-35
19        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
19-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ba5970ab7047a5364a7daf3d65e8c0dd\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
20        android:dataExtractionRules="@xml/data_extraction_rules"
20-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:7:9-65
21        android:debuggable="true"
22        android:extractNativeLibs="false"
23        android:fullBackupContent="@xml/backup_rules"
23-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:8:9-54
24        android:icon="@mipmap/ic_launcher"
24-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:9:9-43
25        android:label="@string/app_name"
25-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:10:9-41
26        android:roundIcon="@mipmap/ic_launcher_round"
26-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:11:9-54
27        android:supportsRtl="true"
27-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:12:9-35
28        android:testOnly="true"
29        android:theme="@style/Theme.MyApplication" >
29-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:13:9-51
30        <activity
30-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:14:9-24:20
31            android:name="com.example.myapplication.MainActivity"
31-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:15:13-41
32            android:exported="true"
32-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:16:13-36
33            android:label="@string/app_name"
33-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:17:13-45
34            android:theme="@style/Theme.MyApplication" >
34-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:18:13-55
35            <intent-filter>
35-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:19:13-23:29
36                <action android:name="android.intent.action.MAIN" />
36-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:20:17-69
36-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:20:25-66
37
38                <category android:name="android.intent.category.LAUNCHER" />
38-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:22:17-77
38-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:22:27-74
39            </intent-filter>
40        </activity>
41
42        <service
42-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2c50770cc4a726c011ac2ec64c5cbb94\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
43            android:name="androidx.room.MultiInstanceInvalidationService"
43-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2c50770cc4a726c011ac2ec64c5cbb94\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
44            android:directBootAware="true"
44-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2c50770cc4a726c011ac2ec64c5cbb94\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
45            android:exported="false" />
45-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2c50770cc4a726c011ac2ec64c5cbb94\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
46
47        <activity
47-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f8b82bf74cb0994cbb19ff17592f6518\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
48            android:name="androidx.compose.ui.tooling.PreviewActivity"
48-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f8b82bf74cb0994cbb19ff17592f6518\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
49            android:exported="true" />
49-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f8b82bf74cb0994cbb19ff17592f6518\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
50        <activity
50-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\95868c8a686a3a032ad8c267d34bccc8\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:23:9-25:39
51            android:name="androidx.activity.ComponentActivity"
51-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\95868c8a686a3a032ad8c267d34bccc8\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:24:13-63
52            android:exported="true" />
52-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\95868c8a686a3a032ad8c267d34bccc8\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:25:13-36
53
54        <provider
54-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a511498e51ca6fddfc2c08ea5511bda9\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
55            android:name="androidx.startup.InitializationProvider"
55-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a511498e51ca6fddfc2c08ea5511bda9\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
56            android:authorities="com.example.myapplication.androidx-startup"
56-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a511498e51ca6fddfc2c08ea5511bda9\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
57            android:exported="false" >
57-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a511498e51ca6fddfc2c08ea5511bda9\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
58            <meta-data
58-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a511498e51ca6fddfc2c08ea5511bda9\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
59                android:name="androidx.emoji2.text.EmojiCompatInitializer"
59-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a511498e51ca6fddfc2c08ea5511bda9\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
60                android:value="androidx.startup" />
60-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a511498e51ca6fddfc2c08ea5511bda9\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
61            <meta-data
61-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\4f4ec2d7a3243f5c26bdbadb2bfbb96a\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:29:13-31:52
62                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
62-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\4f4ec2d7a3243f5c26bdbadb2bfbb96a\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:30:17-78
63                android:value="androidx.startup" />
63-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\4f4ec2d7a3243f5c26bdbadb2bfbb96a\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:31:17-49
64            <meta-data
64-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e6be7216d7b4c46900b8deed99b35237\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
65                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
65-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e6be7216d7b4c46900b8deed99b35237\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
66                android:value="androidx.startup" />
66-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e6be7216d7b4c46900b8deed99b35237\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
67        </provider>
68
69        <receiver
69-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e6be7216d7b4c46900b8deed99b35237\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
70            android:name="androidx.profileinstaller.ProfileInstallReceiver"
70-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e6be7216d7b4c46900b8deed99b35237\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
71            android:directBootAware="false"
71-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e6be7216d7b4c46900b8deed99b35237\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
72            android:enabled="true"
72-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e6be7216d7b4c46900b8deed99b35237\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
73            android:exported="true"
73-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e6be7216d7b4c46900b8deed99b35237\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
74            android:permission="android.permission.DUMP" >
74-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e6be7216d7b4c46900b8deed99b35237\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
75            <intent-filter>
75-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e6be7216d7b4c46900b8deed99b35237\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
76                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
76-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e6be7216d7b4c46900b8deed99b35237\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
76-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e6be7216d7b4c46900b8deed99b35237\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
77            </intent-filter>
78            <intent-filter>
78-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e6be7216d7b4c46900b8deed99b35237\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
79                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
79-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e6be7216d7b4c46900b8deed99b35237\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
79-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e6be7216d7b4c46900b8deed99b35237\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
80            </intent-filter>
81            <intent-filter>
81-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e6be7216d7b4c46900b8deed99b35237\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
82                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
82-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e6be7216d7b4c46900b8deed99b35237\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
82-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e6be7216d7b4c46900b8deed99b35237\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
83            </intent-filter>
84            <intent-filter>
84-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e6be7216d7b4c46900b8deed99b35237\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
85                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
85-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e6be7216d7b4c46900b8deed99b35237\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
85-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e6be7216d7b4c46900b8deed99b35237\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
86            </intent-filter>
87        </receiver>
88    </application>
89
90</manifest>
