package com.example.myapplication.ui.screen.wallet

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.example.myapplication.data.local.entity.WalletEntity
import com.example.myapplication.data.repository.RepositoryProvider
import com.example.myapplication.data.repository.TransactionRepository
import com.example.myapplication.data.repository.WalletRepository
import com.example.myapplication.utils.ErrorHandler
import com.example.myapplication.utils.ValidationUtils
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.launch

data class WalletUiState(
    val wallet: WalletEntity? = null,
    val isLoading: Boolean = false,
    val errorMessage: String? = null,
    val showAddMoneyDialog: Boolean = false,
    val showRemoveMoneyDialog: Boolean = false,
    val totalIncome: Double = 0.0,
    val totalExpenses: Double = 0.0
)

class WalletViewModel(application: Application) : AndroidViewModel(application) {

    private val walletRepository: WalletRepository = RepositoryProvider.provideWalletRepository(application)
    private val transactionRepository: TransactionRepository = RepositoryProvider.provideTransactionRepository(application)

    private val _uiState = MutableStateFlow(WalletUiState())
    val uiState: StateFlow<WalletUiState> = _uiState.asStateFlow()

    init {
        initializeWallet()
        observeWalletData()
    }

    private fun initializeWallet() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true)
            try {
                walletRepository.createDefaultWalletIfNeeded()
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    errorMessage = "Lỗi khởi tạo ví: ${e.message}"
                )
            }
        }
    }

    private fun observeWalletData() {
        viewModelScope.launch {
            combine(
                walletRepository.getFirstWallet(),
                transactionRepository.getAllTransactions()
            ) { wallet, transactions ->
                val walletId = wallet?.id ?: 1
                val totalIncome = transactionRepository.getTotalIncome(walletId)
                val totalExpenses = transactionRepository.getTotalExpenses(walletId)

                _uiState.value = _uiState.value.copy(
                    wallet = wallet,
                    isLoading = false,
                    totalIncome = totalIncome,
                    totalExpenses = totalExpenses,
                    errorMessage = null
                )
            }.collect { }
        }
    }

    fun showAddMoneyDialog() {
        _uiState.value = _uiState.value.copy(showAddMoneyDialog = true)
    }

    fun hideAddMoneyDialog() {
        _uiState.value = _uiState.value.copy(showAddMoneyDialog = false)
    }

    fun showRemoveMoneyDialog() {
        _uiState.value = _uiState.value.copy(showRemoveMoneyDialog = true)
    }

    fun hideRemoveMoneyDialog() {
        _uiState.value = _uiState.value.copy(showRemoveMoneyDialog = false)
    }

    fun addMoney(amount: Double, note: String = "Nạp tiền") {
        viewModelScope.launch {
            try {
                val wallet = _uiState.value.wallet
                if (wallet == null) {
                    _uiState.value = _uiState.value.copy(
                        errorMessage = "Không tìm thấy ví"
                    )
                    return@launch
                }

                val amountValidation = ValidationUtils.validateAmount(amount.toString())
                if (!amountValidation.isValid) {
                    _uiState.value = _uiState.value.copy(
                        errorMessage = amountValidation.errorMessage
                    )
                    return@launch
                }

                val sanitizedNote = ValidationUtils.sanitizeInput(note)
                val noteValidation = ValidationUtils.validateNote(sanitizedNote)
                if (!noteValidation.isValid) {
                    _uiState.value = _uiState.value.copy(
                        errorMessage = noteValidation.errorMessage
                    )
                    return@launch
                }

                walletRepository.addMoney(wallet.id, amount)
                transactionRepository.addIncome(
                    walletId = wallet.id,
                    category = "Nạp tiền",
                    amount = amount,
                    note = sanitizedNote.ifBlank { null }
                )
                hideAddMoneyDialog()

            } catch (e: Exception) {
                ErrorHandler.logError("WalletViewModel", "Error adding money", e)
                _uiState.value = _uiState.value.copy(
                    errorMessage = ErrorHandler.getErrorMessage(e)
                )
            }
        }
    }

    fun removeMoney(amount: Double, note: String = "Rút tiền") {
        viewModelScope.launch {
            try {
                val wallet = _uiState.value.wallet
                if (wallet == null) {
                    _uiState.value = _uiState.value.copy(
                        errorMessage = "Không tìm thấy ví"
                    )
                    return@launch
                }

                val amountValidation = ValidationUtils.validateAmount(amount.toString())
                if (!amountValidation.isValid) {
                    _uiState.value = _uiState.value.copy(
                        errorMessage = amountValidation.errorMessage
                    )
                    return@launch
                }

                val balanceValidation = ValidationUtils.validateExpenseAgainstBalance(amount, wallet.balance)
                if (!balanceValidation.isValid) {
                    _uiState.value = _uiState.value.copy(
                        errorMessage = balanceValidation.errorMessage
                    )
                    return@launch
                }

                val sanitizedNote = ValidationUtils.sanitizeInput(note)
                val noteValidation = ValidationUtils.validateNote(sanitizedNote)
                if (!noteValidation.isValid) {
                    _uiState.value = _uiState.value.copy(
                        errorMessage = noteValidation.errorMessage
                    )
                    return@launch
                }

                val success = walletRepository.removeMoney(wallet.id, amount)
                if (success) {
                    transactionRepository.addExpense(
                        walletId = wallet.id,
                        category = "Rút tiền",
                        amount = amount,
                        note = sanitizedNote.ifBlank { null }
                    )
                    hideRemoveMoneyDialog()
                } else {
                    _uiState.value = _uiState.value.copy(
                        errorMessage = ErrorHandler.getErrorMessage(
                            IllegalStateException("Không thể rút tiền từ ví")
                        )
                    )
                }

            } catch (e: Exception) {
                ErrorHandler.logError("WalletViewModel", "Error removing money", e)
                _uiState.value = _uiState.value.copy(
                    errorMessage = ErrorHandler.getErrorMessage(e)
                )
            }
        }
    }

    fun clearError() {
        _uiState.value = _uiState.value.copy(errorMessage = null)
    }
}