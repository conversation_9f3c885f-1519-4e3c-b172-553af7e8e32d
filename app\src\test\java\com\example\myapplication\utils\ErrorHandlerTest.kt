package com.example.myapplication.utils

import android.database.sqlite.SQLiteException
import org.junit.Test
import org.junit.Assert.*
import java.io.IOException
import java.net.SocketTimeoutException
import java.net.UnknownHostException

class ErrorHandlerTest {

    @Test
    fun getErrorMessage_sqliteException_returnsCorrectMessage() {
        val exception = SQLiteException("Database error")
        val result = ErrorHandler.getErrorMessage(exception)
        assertTrue(result.contains("Lỗi cơ sở dữ liệu"))
        assertTrue(result.contains("Database error"))
    }

    @Test
    fun getErrorMessage_ioException_returnsNetworkMessage() {
        val exception = IOException("Network error")
        val result = ErrorHandler.getErrorMessage(exception)
        assertEquals("Lỗi kết nối: Vui lòng kiểm tra kết nối mạng", result)
    }

    @Test
    fun getErrorMessage_socketTimeoutException_returnsTimeoutMessage() {
        val exception = SocketTimeoutException("Timeout")
        val result = ErrorHandler.getErrorMessage(exception)
        assertEquals("Kết nối quá chậm: Vui lòng thử lại", result)
    }

    @Test
    fun getErrorMessage_unknownHostException_returnsHostMessage() {
        val exception = UnknownHostException("Unknown host")
        val result = ErrorHandler.getErrorMessage(exception)
        assertEquals("Không thể kết nối: Vui lòng kiểm tra kết nối mạng", result)
    }

    @Test
    fun getErrorMessage_numberFormatException_returnsFormatMessage() {
        val exception = NumberFormatException("Invalid number")
        val result = ErrorHandler.getErrorMessage(exception)
        assertEquals("Định dạng số không hợp lệ", result)
    }

    @Test
    fun getErrorMessage_illegalArgumentException_returnsArgumentMessage() {
        val exception = IllegalArgumentException("Invalid argument")
        val result = ErrorHandler.getErrorMessage(exception)
        assertTrue(result.contains("Dữ liệu đầu vào không hợp lệ"))
        assertTrue(result.contains("Invalid argument"))
    }

    @Test
    fun getErrorMessage_illegalStateException_returnsStateMessage() {
        val exception = IllegalStateException("Invalid state")
        val result = ErrorHandler.getErrorMessage(exception)
        assertTrue(result.contains("Trạng thái ứng dụng không hợp lệ"))
        assertTrue(result.contains("Invalid state"))
    }

    @Test
    fun getErrorMessage_genericException_returnsGenericMessage() {
        val exception = RuntimeException("Generic error")
        val result = ErrorHandler.getErrorMessage(exception)
        assertTrue(result.contains("Đã xảy ra lỗi"))
        assertTrue(result.contains("Generic error"))
    }

    @Test
    fun getErrorMessage_exceptionWithoutMessage_returnsUnknownMessage() {
        val exception = RuntimeException()
        val result = ErrorHandler.getErrorMessage(exception)
        assertTrue(result.contains("Lỗi không xác định"))
    }
}
