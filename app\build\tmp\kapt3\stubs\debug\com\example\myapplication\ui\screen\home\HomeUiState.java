package com.example.myapplication.ui.screen.home;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000>\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0018\n\u0002\u0010\b\n\u0002\b\u0002\b\u0087\b\u0018\u00002\u00020\u0001B[\u0012\u000e\b\u0002\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u0012\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0006\u0012\u000e\b\u0002\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00060\u0003\u0012\b\b\u0002\u0010\b\u001a\u00020\t\u0012\b\b\u0002\u0010\n\u001a\u00020\u000b\u0012\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\r\u0012\b\b\u0002\u0010\u000e\u001a\u00020\u000f\u00a2\u0006\u0002\u0010\u0010J\u000f\u0010\u001d\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u00c6\u0003J\u000b\u0010\u001e\u001a\u0004\u0018\u00010\u0006H\u00c6\u0003J\u000f\u0010\u001f\u001a\b\u0012\u0004\u0012\u00020\u00060\u0003H\u00c6\u0003J\t\u0010 \u001a\u00020\tH\u00c6\u0003J\t\u0010!\u001a\u00020\u000bH\u00c6\u0003J\u000b\u0010\"\u001a\u0004\u0018\u00010\rH\u00c6\u0003J\t\u0010#\u001a\u00020\u000fH\u00c6\u0003J_\u0010$\u001a\u00020\u00002\u000e\b\u0002\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u00062\u000e\b\u0002\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00060\u00032\b\b\u0002\u0010\b\u001a\u00020\t2\b\b\u0002\u0010\n\u001a\u00020\u000b2\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\r2\b\b\u0002\u0010\u000e\u001a\u00020\u000fH\u00c6\u0001J\u0013\u0010%\u001a\u00020\u000b2\b\u0010&\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\'\u001a\u00020(H\u00d6\u0001J\t\u0010)\u001a\u00020\rH\u00d6\u0001R\u0017\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00060\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012R\u0011\u0010\b\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014R\u0013\u0010\f\u001a\u0004\u0018\u00010\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0016R\u0011\u0010\n\u001a\u00020\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u0017R\u0011\u0010\u000e\u001a\u00020\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0019R\u0017\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u0012R\u0013\u0010\u0005\u001a\u0004\u0018\u00010\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u001c\u00a8\u0006*"}, d2 = {"Lcom/example/myapplication/ui/screen/home/<USER>", "", "transactions", "", "Lcom/example/myapplication/data/local/entity/TransactionEntity;", "wallet", "Lcom/example/myapplication/data/local/entity/WalletEntity;", "allWallets", "analytics", "Lcom/example/myapplication/ui/screen/home/<USER>", "isLoading", "", "errorMessage", "", "selectedPeriod", "Lcom/example/myapplication/ui/screen/home/<USER>", "(Ljava/util/List;Lcom/example/myapplication/data/local/entity/WalletEntity;Ljava/util/List;Lcom/example/myapplication/ui/screen/home/<USER>/lang/String;Lcom/example/myapplication/ui/screen/home/<USER>", "getAllWallets", "()Ljava/util/List;", "getAnalytics", "()Lcom/example/myapplication/ui/screen/home/<USER>", "getErrorMessage", "()Ljava/lang/String;", "()Z", "getSelectedPeriod", "()Lcom/example/myapplication/ui/screen/home/<USER>", "getTransactions", "getWallet", "()Lcom/example/myapplication/data/local/entity/WalletEntity;", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "copy", "equals", "other", "hashCode", "", "toString", "app_debug"})
public final class HomeUiState {
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.example.myapplication.data.local.entity.TransactionEntity> transactions = null;
    @org.jetbrains.annotations.Nullable()
    private final com.example.myapplication.data.local.entity.WalletEntity wallet = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.example.myapplication.data.local.entity.WalletEntity> allWallets = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.myapplication.ui.screen.home.SpendingAnalytics analytics = null;
    private final boolean isLoading = false;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String errorMessage = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.myapplication.ui.screen.home.AnalyticsPeriod selectedPeriod = null;
    
    public HomeUiState(@org.jetbrains.annotations.NotNull()
    java.util.List<com.example.myapplication.data.local.entity.TransactionEntity> transactions, @org.jetbrains.annotations.Nullable()
    com.example.myapplication.data.local.entity.WalletEntity wallet, @org.jetbrains.annotations.NotNull()
    java.util.List<com.example.myapplication.data.local.entity.WalletEntity> allWallets, @org.jetbrains.annotations.NotNull()
    com.example.myapplication.ui.screen.home.SpendingAnalytics analytics, boolean isLoading, @org.jetbrains.annotations.Nullable()
    java.lang.String errorMessage, @org.jetbrains.annotations.NotNull()
    com.example.myapplication.ui.screen.home.AnalyticsPeriod selectedPeriod) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.myapplication.data.local.entity.TransactionEntity> getTransactions() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.example.myapplication.data.local.entity.WalletEntity getWallet() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.myapplication.data.local.entity.WalletEntity> getAllWallets() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.myapplication.ui.screen.home.SpendingAnalytics getAnalytics() {
        return null;
    }
    
    public final boolean isLoading() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getErrorMessage() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.myapplication.ui.screen.home.AnalyticsPeriod getSelectedPeriod() {
        return null;
    }
    
    public HomeUiState() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.myapplication.data.local.entity.TransactionEntity> component1() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.example.myapplication.data.local.entity.WalletEntity component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.myapplication.data.local.entity.WalletEntity> component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.myapplication.ui.screen.home.SpendingAnalytics component4() {
        return null;
    }
    
    public final boolean component5() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component6() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.myapplication.ui.screen.home.AnalyticsPeriod component7() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.myapplication.ui.screen.home.HomeUiState copy(@org.jetbrains.annotations.NotNull()
    java.util.List<com.example.myapplication.data.local.entity.TransactionEntity> transactions, @org.jetbrains.annotations.Nullable()
    com.example.myapplication.data.local.entity.WalletEntity wallet, @org.jetbrains.annotations.NotNull()
    java.util.List<com.example.myapplication.data.local.entity.WalletEntity> allWallets, @org.jetbrains.annotations.NotNull()
    com.example.myapplication.ui.screen.home.SpendingAnalytics analytics, boolean isLoading, @org.jetbrains.annotations.Nullable()
    java.lang.String errorMessage, @org.jetbrains.annotations.NotNull()
    com.example.myapplication.ui.screen.home.AnalyticsPeriod selectedPeriod) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}