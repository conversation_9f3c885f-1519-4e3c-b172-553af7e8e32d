package com.example.myapplication.data.local.dao;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00008\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0000\n\u0002\u0010\u0006\n\u0000\n\u0002\u0010\b\n\u0002\b\u0007\n\u0002\u0010\t\n\u0002\b\u0005\bg\u0018\u00002\u00020\u0001J\u0016\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006J\u0014\u0010\u0007\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\t0\bH\'J\u0018\u0010\n\u001a\u0004\u0018\u00010\u000b2\u0006\u0010\f\u001a\u00020\rH\u00a7@\u00a2\u0006\u0002\u0010\u000eJ\u0010\u0010\u000f\u001a\u0004\u0018\u00010\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0010J\u0010\u0010\u0011\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00050\bH\'J\u0018\u0010\u0012\u001a\u0004\u0018\u00010\u00052\u0006\u0010\u0013\u001a\u00020\rH\u00a7@\u00a2\u0006\u0002\u0010\u000eJ\u0016\u0010\u0014\u001a\u00020\u00152\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006J\u001e\u0010\u0016\u001a\u00020\u00032\u0006\u0010\f\u001a\u00020\r2\u0006\u0010\u0017\u001a\u00020\u000bH\u00a7@\u00a2\u0006\u0002\u0010\u0018J\u0016\u0010\u0019\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006\u00a8\u0006\u001a"}, d2 = {"Lcom/example/myapplication/data/local/dao/WalletDao;", "", "deleteWallet", "", "wallet", "Lcom/example/myapplication/data/local/entity/WalletEntity;", "(Lcom/example/myapplication/data/local/entity/WalletEntity;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAllWallets", "Lkotlinx/coroutines/flow/Flow;", "", "getBalance", "", "walletId", "", "(ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getFirstWallet", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getFirstWalletFlow", "getWalletById", "id", "insertWallet", "", "updateBalance", "amount", "(IDLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateWallet", "app_debug"})
@androidx.room.Dao()
public abstract interface WalletDao {
    
    @androidx.room.Query(value = "SELECT * FROM wallets")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.myapplication.data.local.entity.WalletEntity>> getAllWallets();
    
    @androidx.room.Query(value = "SELECT * FROM wallets WHERE id = :id")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getWalletById(int id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.example.myapplication.data.local.entity.WalletEntity> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM wallets LIMIT 1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getFirstWallet(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.example.myapplication.data.local.entity.WalletEntity> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM wallets LIMIT 1")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<com.example.myapplication.data.local.entity.WalletEntity> getFirstWalletFlow();
    
    @androidx.room.Insert()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertWallet(@org.jetbrains.annotations.NotNull()
    com.example.myapplication.data.local.entity.WalletEntity wallet, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion);
    
    @androidx.room.Update()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateWallet(@org.jetbrains.annotations.NotNull()
    com.example.myapplication.data.local.entity.WalletEntity wallet, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Delete()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteWallet(@org.jetbrains.annotations.NotNull()
    com.example.myapplication.data.local.entity.WalletEntity wallet, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE wallets SET balance = balance + :amount WHERE id = :walletId")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateBalance(int walletId, double amount, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "SELECT balance FROM wallets WHERE id = :walletId")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getBalance(int walletId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Double> $completion);
}