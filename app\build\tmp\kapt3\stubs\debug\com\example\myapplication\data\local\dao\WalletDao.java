package com.example.myapplication.data.local.dao;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000J\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\t\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\b\u0003\n\u0002\u0010\u0006\n\u0002\b\n\n\u0002\u0010\u000e\n\u0002\b\u0005\n\u0002\u0010\u000b\n\u0002\b\u0003\bg\u0018\u00002\u00020\u0001J \u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\bJ \u0010\t\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\bJ\u0016\u0010\n\u001a\u00020\u00032\u0006\u0010\u000b\u001a\u00020\fH\u00a7@\u00a2\u0006\u0002\u0010\rJ\u000e\u0010\u000e\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u000fJ\u0014\u0010\u0010\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\f0\u00120\u0011H\'J\u0014\u0010\u0013\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\f0\u00120\u0011H\'J\u0014\u0010\u0014\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\f0\u00120\u0011H\'J\u0018\u0010\u0015\u001a\u0004\u0018\u00010\u00162\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0017J\u0010\u0010\u0018\u001a\u0004\u0018\u00010\fH\u00a7@\u00a2\u0006\u0002\u0010\u000fJ\u0010\u0010\u0019\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\f0\u0011H\'J\u0010\u0010\u001a\u001a\u0004\u0018\u00010\fH\u00a7@\u00a2\u0006\u0002\u0010\u000fJ\u0010\u0010\u001b\u001a\u0004\u0018\u00010\u0016H\u00a7@\u00a2\u0006\u0002\u0010\u000fJ\u0018\u0010\u001c\u001a\u0004\u0018\u00010\f2\u0006\u0010\u001d\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0017J\u0016\u0010\u001e\u001a\u00020\u00072\u0006\u0010\u000b\u001a\u00020\fH\u00a7@\u00a2\u0006\u0002\u0010\rJ\u001c\u0010\u001f\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\f0\u00120\u00112\u0006\u0010 \u001a\u00020!H\'J(\u0010\"\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010#\u001a\u00020\u00162\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010$J(\u0010%\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010&\u001a\u00020\'2\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010(J\u0016\u0010)\u001a\u00020\u00032\u0006\u0010\u000b\u001a\u00020\fH\u00a7@\u00a2\u0006\u0002\u0010\r\u00a8\u0006*"}, d2 = {"Lcom/example/myapplication/data/local/dao/WalletDao;", "", "activateWallet", "", "walletId", "", "timestamp", "", "(IJLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deactivateWallet", "deleteWallet", "wallet", "Lcom/example/myapplication/data/local/entity/WalletEntity;", "(Lcom/example/myapplication/data/local/entity/WalletEntity;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getActiveWalletCount", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAllActiveWallets", "Lkotlinx/coroutines/flow/Flow;", "", "getAllWallets", "getAvailableWallets", "getBalance", "", "(ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getFirstActiveWallet", "getFirstActiveWalletFlow", "getFirstAvailableWallet", "getTotalBalance", "getWalletById", "id", "insertWallet", "searchWallets", "searchQuery", "", "updateBalance", "amount", "(IDJLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateFrozenStatus", "isFrozen", "", "(IZJLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateWallet", "app_debug"})
@androidx.room.Dao()
public abstract interface WalletDao {
    
    @androidx.room.Query(value = "SELECT * FROM wallets WHERE isActive = 1 ORDER BY name ASC")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.myapplication.data.local.entity.WalletEntity>> getAllActiveWallets();
    
    @androidx.room.Query(value = "SELECT * FROM wallets ORDER BY name ASC")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.myapplication.data.local.entity.WalletEntity>> getAllWallets();
    
    @androidx.room.Query(value = "SELECT * FROM wallets WHERE isActive = 1 AND isFrozen = 0 ORDER BY name ASC")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.myapplication.data.local.entity.WalletEntity>> getAvailableWallets();
    
    @androidx.room.Query(value = "SELECT * FROM wallets WHERE id = :id")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getWalletById(int id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.example.myapplication.data.local.entity.WalletEntity> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM wallets WHERE isActive = 1 LIMIT 1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getFirstActiveWallet(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.example.myapplication.data.local.entity.WalletEntity> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM wallets WHERE isActive = 1 LIMIT 1")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<com.example.myapplication.data.local.entity.WalletEntity> getFirstActiveWalletFlow();
    
    @androidx.room.Query(value = "SELECT * FROM wallets WHERE isActive = 1 AND isFrozen = 0 LIMIT 1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getFirstAvailableWallet(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.example.myapplication.data.local.entity.WalletEntity> $completion);
    
    @androidx.room.Insert()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertWallet(@org.jetbrains.annotations.NotNull()
    com.example.myapplication.data.local.entity.WalletEntity wallet, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion);
    
    @androidx.room.Update()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateWallet(@org.jetbrains.annotations.NotNull()
    com.example.myapplication.data.local.entity.WalletEntity wallet, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Delete()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteWallet(@org.jetbrains.annotations.NotNull()
    com.example.myapplication.data.local.entity.WalletEntity wallet, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE wallets SET balance = balance + :amount, updatedAt = :timestamp WHERE id = :walletId")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateBalance(int walletId, double amount, long timestamp, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "SELECT balance FROM wallets WHERE id = :walletId")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getBalance(int walletId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Double> $completion);
    
    @androidx.room.Query(value = "UPDATE wallets SET isFrozen = :isFrozen, updatedAt = :timestamp WHERE id = :walletId")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateFrozenStatus(int walletId, boolean isFrozen, long timestamp, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE wallets SET isActive = 0, updatedAt = :timestamp WHERE id = :walletId")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deactivateWallet(int walletId, long timestamp, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE wallets SET isActive = 1, updatedAt = :timestamp WHERE id = :walletId")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object activateWallet(int walletId, long timestamp, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "SELECT COUNT(*) FROM wallets WHERE isActive = 1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getActiveWalletCount(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    @androidx.room.Query(value = "SELECT SUM(balance) FROM wallets WHERE isActive = 1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getTotalBalance(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Double> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM wallets WHERE name LIKE \'%\' || :searchQuery || \'%\' AND isActive = 1")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.myapplication.data.local.entity.WalletEntity>> searchWallets(@org.jetbrains.annotations.NotNull()
    java.lang.String searchQuery);
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 3, xi = 48)
    public static final class DefaultImpls {
    }
}