package com.example.myapplication.ui.screen.home

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.example.myapplication.data.local.entity.TransactionEntity

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HomeScreen(transactions: List<TransactionEntity>) {
    Scaffold(
        topBar = {
            TopAppBar(title = { Text("Quản lý tài chính") })
        }
    ) { padding ->
        LazyColumn(
            modifier = Modifier
                .padding(padding)
                .fillMaxSize()
        ) {
            items(transactions) { transaction ->
                ListItem(
                    headlineContent = { Text(transaction.category) },
                    supportingContent = { Text("${transaction.amount} đ") }
                )
                HorizontalDivider()
            }
        }
    }
}
