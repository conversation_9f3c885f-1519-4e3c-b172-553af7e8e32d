package com.example.myapplication.ui.screen.home;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000n\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0006\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010\t\n\u0000\u001a,\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0012\u0010\u0006\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00010\u0007H\u0003\u001a\u0016\u0010\b\u001a\u00020\u00012\f\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u000b0\nH\u0003\u001a\u0010\u0010\f\u001a\u00020\u00012\u0006\u0010\r\u001a\u00020\u000bH\u0003\u001a\u0016\u0010\u000e\u001a\u00020\u00012\f\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00010\u0010H\u0003\u001a.\u0010\u0011\u001a\u00020\u00012\b\u0010\u0012\u001a\u0004\u0018\u00010\u00132\f\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u00130\n2\f\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u00010\u0010H\u0003\u001a<\u0010\u0016\u001a\u00020\u00012\u0006\u0010\u0017\u001a\u00020\u00182\u0006\u0010\u0019\u001a\u00020\u001a2\u0006\u0010\u001b\u001a\u00020\u001c2\u0006\u0010\u001d\u001a\u00020\u001e2\b\b\u0002\u0010\u001f\u001a\u00020 H\u0003\u00f8\u0001\u0000\u00a2\u0006\u0004\b!\u0010\"\u001a2\u0010#\u001a\u00020\u00012\u000e\b\u0002\u0010$\u001a\b\u0012\u0004\u0012\u00020\u00010\u00102\u000e\b\u0002\u0010%\u001a\b\u0012\u0004\u0012\u00020\u00010\u00102\b\b\u0002\u0010&\u001a\u00020\'H\u0007\u001a\u0010\u0010(\u001a\u00020\u00012\u0006\u0010)\u001a\u00020*H\u0003\u001a&\u0010+\u001a\u00020\u00012\u0006\u0010,\u001a\u00020\u001a2\u0006\u0010-\u001a\u00020\u00182\f\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u00010\u0010H\u0003\u001a\u0010\u0010.\u001a\u00020\u00182\u0006\u0010\u0019\u001a\u00020\u001aH\u0002\u001a\u0010\u0010/\u001a\u00020\u00182\u0006\u00100\u001a\u000201H\u0002\u0082\u0002\u0007\n\u0005\b\u00a1\u001e0\u0001\u00a8\u00062"}, d2 = {"AnalyticsOverviewSection", "", "analytics", "Lcom/example/myapplication/ui/screen/home/<USER>", "selectedPeriod", "Lcom/example/myapplication/ui/screen/home/<USER>", "onPeriodChange", "Lkotlin/Function1;", "CategoryBreakdownCard", "categories", "", "Lcom/example/myapplication/ui/screen/home/<USER>", "CategoryBreakdownItem", "category", "EmptyTransactionsCard", "onAddTransaction", "Lkotlin/Function0;", "EnhancedWalletBalanceCard", "wallet", "Lcom/example/myapplication/data/local/entity/WalletEntity;", "allWallets", "onWalletClick", "FinancialMetricCard", "title", "", "amount", "", "icon", "Landroidx/compose/ui/graphics/vector/ImageVector;", "color", "Landroidx/compose/ui/graphics/Color;", "modifier", "Landroidx/compose/ui/Modifier;", "FinancialMetricCard-42QJj7c", "(Ljava/lang/String;DLandroidx/compose/ui/graphics/vector/ImageVector;JLandroidx/compose/ui/Modifier;)V", "HomeScreen", "onNavigateToWallet", "onNavigateToAddTransaction", "viewModel", "Lcom/example/myapplication/ui/screen/home/<USER>", "TransactionItem", "transaction", "Lcom/example/myapplication/data/local/entity/TransactionEntity;", "WalletBalanceCard", "balance", "walletName", "formatCurrency", "formatDate", "timestamp", "", "app_debug"})
public final class HomeScreenKt {
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void HomeScreen(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateToWallet, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateToAddTransaction, @org.jetbrains.annotations.NotNull()
    com.example.myapplication.ui.screen.home.HomeViewModel viewModel) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void WalletBalanceCard(double balance, java.lang.String walletName, kotlin.jvm.functions.Function0<kotlin.Unit> onWalletClick) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void TransactionItem(com.example.myapplication.data.local.entity.TransactionEntity transaction) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void EmptyTransactionsCard(kotlin.jvm.functions.Function0<kotlin.Unit> onAddTransaction) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void AnalyticsOverviewSection(com.example.myapplication.ui.screen.home.SpendingAnalytics analytics, com.example.myapplication.ui.screen.home.AnalyticsPeriod selectedPeriod, kotlin.jvm.functions.Function1<? super com.example.myapplication.ui.screen.home.AnalyticsPeriod, kotlin.Unit> onPeriodChange) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void EnhancedWalletBalanceCard(com.example.myapplication.data.local.entity.WalletEntity wallet, java.util.List<com.example.myapplication.data.local.entity.WalletEntity> allWallets, kotlin.jvm.functions.Function0<kotlin.Unit> onWalletClick) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void CategoryBreakdownCard(java.util.List<com.example.myapplication.ui.screen.home.CategoryData> categories) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void CategoryBreakdownItem(com.example.myapplication.ui.screen.home.CategoryData category) {
    }
    
    private static final java.lang.String formatCurrency(double amount) {
        return null;
    }
    
    private static final java.lang.String formatDate(long timestamp) {
        return null;
    }
}