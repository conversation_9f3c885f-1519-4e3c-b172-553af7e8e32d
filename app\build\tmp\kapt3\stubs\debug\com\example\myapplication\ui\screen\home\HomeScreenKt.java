package com.example.myapplication.ui.screen.home;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u00004\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0006\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0005\n\u0002\u0010\t\n\u0000\u001a\u0016\u0010\u0000\u001a\u00020\u00012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00010\u0003H\u0003\u001a2\u0010\u0004\u001a\u00020\u00012\u000e\b\u0002\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\u000e\b\u0002\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\b\b\u0002\u0010\u0007\u001a\u00020\bH\u0007\u001a\u0010\u0010\t\u001a\u00020\u00012\u0006\u0010\n\u001a\u00020\u000bH\u0003\u001a&\u0010\f\u001a\u00020\u00012\u0006\u0010\r\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u00102\f\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00010\u0003H\u0003\u001a\u0010\u0010\u0012\u001a\u00020\u00102\u0006\u0010\u0013\u001a\u00020\u000eH\u0002\u001a\u0010\u0010\u0014\u001a\u00020\u00102\u0006\u0010\u0015\u001a\u00020\u0016H\u0002\u00a8\u0006\u0017"}, d2 = {"EmptyTransactionsCard", "", "onAddTransaction", "Lkotlin/Function0;", "HomeScreen", "onNavigateToWallet", "onNavigateToAddTransaction", "viewModel", "Lcom/example/myapplication/ui/screen/home/<USER>", "TransactionItem", "transaction", "Lcom/example/myapplication/data/local/entity/TransactionEntity;", "WalletBalanceCard", "balance", "", "walletName", "", "onWalletClick", "formatCurrency", "amount", "formatDate", "timestamp", "", "app_debug"})
public final class HomeScreenKt {
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void HomeScreen(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateToWallet, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateToAddTransaction, @org.jetbrains.annotations.NotNull()
    com.example.myapplication.ui.screen.home.HomeViewModel viewModel) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void WalletBalanceCard(double balance, java.lang.String walletName, kotlin.jvm.functions.Function0<kotlin.Unit> onWalletClick) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void TransactionItem(com.example.myapplication.data.local.entity.TransactionEntity transaction) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void EmptyTransactionsCard(kotlin.jvm.functions.Function0<kotlin.Unit> onAddTransaction) {
    }
    
    private static final java.lang.String formatCurrency(double amount) {
        return null;
    }
    
    private static final java.lang.String formatDate(long timestamp) {
        return null;
    }
}