&com.example.myapplication.MainActivity0com.example.myapplication.data.local.AppDatabase6com.example.myapplication.ui.screen.home.HomeViewModel3com.example.myapplication.ui.navigation.Screen.Home5com.example.myapplication.ui.navigation.Screen.Wallet=com.example.myapplication.ui.navigation.Screen.AddTransactionCcom.example.myapplication.ui.screen.add_transaction.TransactionTypeKcom.example.myapplication.ui.screen.add_transaction.AddTransactionViewModel:com.example.myapplication.ui.screen.wallet.WalletViewModelBcom.example.myapplication.utils.ErrorHandler.AppError.NetworkErrorCcom.example.myapplication.utils.ErrorHandler.AppError.DatabaseErrorEcom.example.myapplication.utils.ErrorHandler.AppError.ValidationErrorGcom.example.myapplication.utils.ErrorHandler.AppError.InsufficientFundsAcom.example.myapplication.utils.ErrorHandler.AppError.CustomError                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       