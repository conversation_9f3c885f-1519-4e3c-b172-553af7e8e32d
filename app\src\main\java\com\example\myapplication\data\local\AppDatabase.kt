package com.example.myapplication.data.local

import androidx.room.Database
import androidx.room.RoomDatabase
import com.example.myapplication.data.local.dao.TransactionDao
import com.example.myapplication.data.local.dao.WalletDao
import com.example.myapplication.data.local.entity.TransactionEntity
import com.example.myapplication.data.local.entity.WalletEntity

@Database(
    entities = [WalletEntity::class, TransactionEntity::class],
    version = 1,
    exportSchema = false
)
abstract class AppDatabase : RoomDatabase() {
    abstract fun walletDao(): WalletDao
    abstract fun transactionDao(): TransactionDao
}
