package com.example.myapplication.ui.screen.home

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.example.myapplication.data.local.entity.TransactionEntity
import com.example.myapplication.data.local.entity.WalletEntity
import com.example.myapplication.data.repository.RepositoryProvider
import com.example.myapplication.data.repository.TransactionRepository
import com.example.myapplication.data.repository.WalletRepository
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.launch

data class SpendingAnalytics(
    val totalIncome: Double = 0.0,
    val totalExpense: Double = 0.0,
    val monthlyTrend: List<MonthlyData> = emptyList(),
    val categoryBreakdown: List<CategoryData> = emptyList(),
    val recentTransactions: List<TransactionEntity> = emptyList(),
    val topCategories: List<CategoryData> = emptyList()
)

data class MonthlyData(
    val month: String,
    val income: Double,
    val expense: Double
)

data class CategoryData(
    val categoryName: String,
    val amount: Double,
    val percentage: Float,
    val color: String
)

enum class AnalyticsPeriod(val displayName: String) {
    THIS_WEEK("Tuần này"),
    THIS_MONTH("Tháng này"),
    THIS_YEAR("Năm này"),
    ALL_TIME("Tất cả")
}

data class HomeUiState(
    val transactions: List<TransactionEntity> = emptyList(),
    val wallet: WalletEntity? = null,
    val allWallets: List<WalletEntity> = emptyList(),
    val analytics: SpendingAnalytics = SpendingAnalytics(),
    val isLoading: Boolean = false,
    val errorMessage: String? = null,
    val selectedPeriod: AnalyticsPeriod = AnalyticsPeriod.THIS_MONTH
)

class HomeViewModel(application: Application) : AndroidViewModel(application) {

    private val walletRepository: WalletRepository = RepositoryProvider.provideWalletRepository(application)
    private val transactionRepository: TransactionRepository = RepositoryProvider.provideTransactionRepository(application)

    private val _uiState = MutableStateFlow(HomeUiState())
    val uiState: StateFlow<HomeUiState> = _uiState.asStateFlow()

    // Keep the old transactions flow for backward compatibility
    val transactions: StateFlow<List<TransactionEntity>> =
        MutableStateFlow(_uiState.value.transactions).asStateFlow()

    init {
        initializeData()
        observeData()
    }

    private fun initializeData() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true)
            try {
                walletRepository.createDefaultWalletsIfNeeded()
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    errorMessage = "Lỗi khởi tạo dữ liệu: ${e.message}"
                )
            }
        }
    }

    private fun observeData() {
        viewModelScope.launch {
            combine(
                transactionRepository.getAllTransactions(),
                walletRepository.getFirstActiveWallet(),
                walletRepository.getAllWallets()
            ) { transactions, wallet, allWallets ->
                _uiState.value = _uiState.value.copy(
                    transactions = transactions,
                    wallet = wallet,
                    allWallets = allWallets,
                    isLoading = false,
                    errorMessage = null
                )
                // Update analytics when data changes
                loadAnalytics()
            }.collect { }
        }
    }

    private fun loadAnalytics() {
        viewModelScope.launch {
            try {
                val currentState = _uiState.value
                val analytics = calculateAnalytics(currentState.transactions, currentState.selectedPeriod)
                _uiState.value = currentState.copy(analytics = analytics)
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    errorMessage = "Lỗi tải dữ liệu phân tích: ${e.message}"
                )
            }
        }
    }

    fun updatePeriod(period: AnalyticsPeriod) {
        _uiState.value = _uiState.value.copy(selectedPeriod = period)
        loadAnalytics()
    }

    fun refreshData() {
        observeData()
    }

    fun deleteTransaction(transaction: TransactionEntity) {
        viewModelScope.launch {
            try {
                // Restore the amount to the wallet if it's an expense
                if (transaction.type == "expense") {
                    walletRepository.addMoney(transaction.walletId, transaction.amount)
                } else if (transaction.type == "income") {
                    walletRepository.removeMoney(transaction.walletId, transaction.amount)
                }

                // Delete the transaction
                transactionRepository.deleteTransaction(transaction)

            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    errorMessage = "Lỗi xóa giao dịch: ${e.message}"
                )
            }
        }
    }

    fun clearError() {
        _uiState.value = _uiState.value.copy(errorMessage = null)
    }

    private fun calculateAnalytics(transactions: List<TransactionEntity>, period: AnalyticsPeriod): SpendingAnalytics {
        val filteredTransactions = filterTransactionsByPeriod(transactions, period)

        val totalIncome = filteredTransactions.filter { it.type == "income" }.sumOf { it.amount }
        val totalExpense = filteredTransactions.filter { it.type == "expense" }.sumOf { it.amount }

        val categoryBreakdown = calculateCategoryBreakdown(filteredTransactions)
        val monthlyTrend = calculateMonthlyTrend(filteredTransactions)
        val topCategories = categoryBreakdown.sortedByDescending { it.amount }.take(5)
        val recentTransactions = transactions.sortedByDescending { it.date }.take(5)

        return SpendingAnalytics(
            totalIncome = totalIncome,
            totalExpense = totalExpense,
            monthlyTrend = monthlyTrend,
            categoryBreakdown = categoryBreakdown,
            recentTransactions = recentTransactions,
            topCategories = topCategories
        )
    }

    private fun filterTransactionsByPeriod(transactions: List<TransactionEntity>, period: AnalyticsPeriod): List<TransactionEntity> {
        val now = System.currentTimeMillis()
        val calendar = java.util.Calendar.getInstance()

        return when (period) {
            AnalyticsPeriod.THIS_WEEK -> {
                calendar.timeInMillis = now
                calendar.set(java.util.Calendar.DAY_OF_WEEK, calendar.firstDayOfWeek)
                calendar.set(java.util.Calendar.HOUR_OF_DAY, 0)
                calendar.set(java.util.Calendar.MINUTE, 0)
                calendar.set(java.util.Calendar.SECOND, 0)
                val weekStart = calendar.timeInMillis
                transactions.filter { it.date >= weekStart }
            }
            AnalyticsPeriod.THIS_MONTH -> {
                calendar.timeInMillis = now
                calendar.set(java.util.Calendar.DAY_OF_MONTH, 1)
                calendar.set(java.util.Calendar.HOUR_OF_DAY, 0)
                calendar.set(java.util.Calendar.MINUTE, 0)
                calendar.set(java.util.Calendar.SECOND, 0)
                val monthStart = calendar.timeInMillis
                transactions.filter { it.date >= monthStart }
            }
            AnalyticsPeriod.THIS_YEAR -> {
                calendar.timeInMillis = now
                calendar.set(java.util.Calendar.DAY_OF_YEAR, 1)
                calendar.set(java.util.Calendar.HOUR_OF_DAY, 0)
                calendar.set(java.util.Calendar.MINUTE, 0)
                calendar.set(java.util.Calendar.SECOND, 0)
                val yearStart = calendar.timeInMillis
                transactions.filter { it.date >= yearStart }
            }
            AnalyticsPeriod.ALL_TIME -> transactions
        }
    }

    private fun calculateCategoryBreakdown(transactions: List<TransactionEntity>): List<CategoryData> {
        val expenseTransactions = transactions.filter { it.type == "expense" }
        val totalExpense = expenseTransactions.sumOf { it.amount }

        if (totalExpense == 0.0) return emptyList()

        val categoryColors = listOf(
            "#1976D2", "#388E3C", "#F57C00", "#7B1FA2", "#C62828",
            "#00796B", "#5D4037", "#455A64", "#E91E63", "#FF5722"
        )

        return expenseTransactions
            .groupBy { it.category }
            .map { (category, categoryTransactions) ->
                val amount = categoryTransactions.sumOf { it.amount }
                val percentage = (amount / totalExpense * 100).toFloat()
                val colorIndex = category.hashCode().mod(categoryColors.size)
                CategoryData(
                    categoryName = category,
                    amount = amount,
                    percentage = percentage,
                    color = categoryColors[colorIndex]
                )
            }
            .sortedByDescending { it.amount }
    }

    private fun calculateMonthlyTrend(transactions: List<TransactionEntity>): List<MonthlyData> {
        val calendar = java.util.Calendar.getInstance()
        val monthFormat = java.text.SimpleDateFormat("MM/yyyy", java.util.Locale.getDefault())

        return transactions
            .groupBy { transaction ->
                calendar.timeInMillis = transaction.date
                monthFormat.format(calendar.time)
            }
            .map { (month, monthTransactions) ->
                val income = monthTransactions.filter { it.type == "income" }.sumOf { it.amount }
                val expense = monthTransactions.filter { it.type == "expense" }.sumOf { it.amount }
                MonthlyData(month, income, expense)
            }
            .sortedBy { it.month }
            .takeLast(6) // Last 6 months
    }
}
