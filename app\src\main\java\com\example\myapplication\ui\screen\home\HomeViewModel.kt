package com.example.myapplication.ui.screen.home

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.example.myapplication.data.local.entity.TransactionEntity
import com.example.myapplication.data.local.entity.WalletEntity
import com.example.myapplication.data.repository.RepositoryProvider
import com.example.myapplication.data.repository.TransactionRepository
import com.example.myapplication.data.repository.WalletRepository
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.launch

data class HomeUiState(
    val transactions: List<TransactionEntity> = emptyList(),
    val wallet: WalletEntity? = null,
    val isLoading: Boolean = false,
    val errorMessage: String? = null
)

class HomeViewModel(application: Application) : AndroidViewModel(application) {

    private val walletRepository: WalletRepository = RepositoryProvider.provideWalletRepository(application)
    private val transactionRepository: TransactionRepository = RepositoryProvider.provideTransactionRepository(application)

    private val _uiState = MutableStateFlow(HomeUiState())
    val uiState: StateFlow<HomeUiState> = _uiState.asStateFlow()

    // Keep the old transactions flow for backward compatibility
    val transactions: StateFlow<List<TransactionEntity>> =
        MutableStateFlow(_uiState.value.transactions).asStateFlow()

    init {
        initializeData()
        observeData()
    }

    private fun initializeData() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true)
            try {
                walletRepository.createDefaultWalletsIfNeeded()
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    errorMessage = "Lỗi khởi tạo dữ liệu: ${e.message}"
                )
            }
        }
    }

    private fun observeData() {
        viewModelScope.launch {
            combine(
                transactionRepository.getAllTransactions(),
                walletRepository.getFirstActiveWallet()
            ) { transactions, wallet ->
                _uiState.value = _uiState.value.copy(
                    transactions = transactions,
                    wallet = wallet,
                    isLoading = false,
                    errorMessage = null
                )
            }.collect { }
        }
    }

    fun refreshData() {
        observeData()
    }

    fun deleteTransaction(transaction: TransactionEntity) {
        viewModelScope.launch {
            try {
                // Restore the amount to the wallet if it's an expense
                if (transaction.type == "expense") {
                    walletRepository.addMoney(transaction.walletId, transaction.amount)
                } else if (transaction.type == "income") {
                    walletRepository.removeMoney(transaction.walletId, transaction.amount)
                }

                // Delete the transaction
                transactionRepository.deleteTransaction(transaction)

            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    errorMessage = "Lỗi xóa giao dịch: ${e.message}"
                )
            }
        }
    }

    fun clearError() {
        _uiState.value = _uiState.value.copy(errorMessage = null)
    }
}
