package com.example.myapplication.ui.screen.home

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.myapplication.data.local.entity.TransactionEntity
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

class HomeViewModel : ViewModel() {
    private val _transactions = MutableStateFlow<List<TransactionEntity>>(
        // Sample data for now - you can replace this with actual repository calls later
        listOf(
            TransactionEntity(
                id = 1,
                walletId = 1,
                type = "expense",
                category = "Ăn uống",
                amount = 50000.0,
                note = "Cơm trưa"
            ),
            TransactionEntity(
                id = 2,
                walletId = 1,
                type = "income",
                category = "Lương",
                amount = 10000000.0,
                note = "Lương tháng 8"
            ),
            TransactionEntity(
                id = 3,
                walletId = 1,
                type = "expense",
                category = "Xăng xe",
                amount = 200000.0,
                note = "Đổ xăng"
            )
        )
    )
    val transactions: StateFlow<List<TransactionEntity>> = _transactions.asStateFlow()
}
