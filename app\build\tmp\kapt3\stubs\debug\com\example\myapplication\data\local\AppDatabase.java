package com.example.myapplication.data.local;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\b\'\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\b\u0010\u0003\u001a\u00020\u0004H&J\b\u0010\u0005\u001a\u00020\u0006H&J\b\u0010\u0007\u001a\u00020\bH&\u00a8\u0006\t"}, d2 = {"Lcom/example/myapplication/data/local/AppDatabase;", "Landroidx/room/RoomDatabase;", "()V", "categoryDao", "Lcom/example/myapplication/data/local/dao/CategoryDao;", "transactionDao", "Lcom/example/myapplication/data/local/dao/TransactionDao;", "walletDao", "Lcom/example/myapplication/data/local/dao/WalletDao;", "app_debug"})
@androidx.room.Database(entities = {com.example.myapplication.data.local.entity.WalletEntity.class, com.example.myapplication.data.local.entity.TransactionEntity.class, com.example.myapplication.data.local.entity.CategoryEntity.class}, version = 2, exportSchema = false)
public abstract class AppDatabase extends androidx.room.RoomDatabase {
    
    public AppDatabase() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public abstract com.example.myapplication.data.local.dao.WalletDao walletDao();
    
    @org.jetbrains.annotations.NotNull()
    public abstract com.example.myapplication.data.local.dao.TransactionDao transactionDao();
    
    @org.jetbrains.annotations.NotNull()
    public abstract com.example.myapplication.data.local.dao.CategoryDao categoryDao();
}