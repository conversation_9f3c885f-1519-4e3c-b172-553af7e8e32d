package com.example.myapplication.ui.screen.home;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000X\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\t\b\u0007\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u001e\u0010\u0014\u001a\u00020\u00152\f\u0010\n\u001a\b\u0012\u0004\u0012\u00020\r0\f2\u0006\u0010\u0016\u001a\u00020\u0017H\u0002J\u001c\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\u00190\f2\f\u0010\n\u001a\b\u0012\u0004\u0012\u00020\r0\fH\u0002J\u001c\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\u001b0\f2\f\u0010\n\u001a\b\u0012\u0004\u0012\u00020\r0\fH\u0002J\u0006\u0010\u001c\u001a\u00020\u001dJ\u000e\u0010\u001e\u001a\u00020\u001d2\u0006\u0010\u001f\u001a\u00020\rJ$\u0010 \u001a\b\u0012\u0004\u0012\u00020\r0\f2\f\u0010\n\u001a\b\u0012\u0004\u0012\u00020\r0\f2\u0006\u0010\u0016\u001a\u00020\u0017H\u0002J\b\u0010!\u001a\u00020\u001dH\u0002J\b\u0010\"\u001a\u00020\u001dH\u0002J\b\u0010#\u001a\u00020\u001dH\u0002J\u0006\u0010$\u001a\u00020\u001dJ\u000e\u0010%\u001a\u00020\u001d2\u0006\u0010\u0016\u001a\u00020\u0017R\u0014\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001d\u0010\n\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\r0\f0\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u0017\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00070\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u000fR\u000e\u0010\u0012\u001a\u00020\u0013X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006&"}, d2 = {"Lcom/example/myapplication/ui/screen/home/<USER>", "Landroidx/lifecycle/AndroidViewModel;", "application", "Landroid/app/Application;", "(Landroid/app/Application;)V", "_uiState", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/example/myapplication/ui/screen/home/<USER>", "transactionRepository", "Lcom/example/myapplication/data/repository/TransactionRepository;", "transactions", "Lkotlinx/coroutines/flow/StateFlow;", "", "Lcom/example/myapplication/data/local/entity/TransactionEntity;", "getTransactions", "()Lkotlinx/coroutines/flow/StateFlow;", "uiState", "getUiState", "walletRepository", "Lcom/example/myapplication/data/repository/WalletRepository;", "calculateAnalytics", "Lcom/example/myapplication/ui/screen/home/<USER>", "period", "Lcom/example/myapplication/ui/screen/home/<USER>", "calculateCategoryBreakdown", "Lcom/example/myapplication/ui/screen/home/<USER>", "calculateMonthlyTrend", "Lcom/example/myapplication/ui/screen/home/<USER>", "clearError", "", "deleteTransaction", "transaction", "filterTransactionsByPeriod", "initializeData", "loadAnalytics", "observeData", "refreshData", "updatePeriod", "app_debug"})
public final class HomeViewModel extends androidx.lifecycle.AndroidViewModel {
    @org.jetbrains.annotations.NotNull()
    private final com.example.myapplication.data.repository.WalletRepository walletRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.myapplication.data.repository.TransactionRepository transactionRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.example.myapplication.ui.screen.home.HomeUiState> _uiState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.example.myapplication.ui.screen.home.HomeUiState> uiState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.util.List<com.example.myapplication.data.local.entity.TransactionEntity>> transactions = null;
    
    public HomeViewModel(@org.jetbrains.annotations.NotNull()
    android.app.Application application) {
        super(null);
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.example.myapplication.ui.screen.home.HomeUiState> getUiState() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.util.List<com.example.myapplication.data.local.entity.TransactionEntity>> getTransactions() {
        return null;
    }
    
    private final void initializeData() {
    }
    
    private final void observeData() {
    }
    
    private final void loadAnalytics() {
    }
    
    public final void updatePeriod(@org.jetbrains.annotations.NotNull()
    com.example.myapplication.ui.screen.home.AnalyticsPeriod period) {
    }
    
    public final void refreshData() {
    }
    
    public final void deleteTransaction(@org.jetbrains.annotations.NotNull()
    com.example.myapplication.data.local.entity.TransactionEntity transaction) {
    }
    
    public final void clearError() {
    }
    
    private final com.example.myapplication.ui.screen.home.SpendingAnalytics calculateAnalytics(java.util.List<com.example.myapplication.data.local.entity.TransactionEntity> transactions, com.example.myapplication.ui.screen.home.AnalyticsPeriod period) {
        return null;
    }
    
    private final java.util.List<com.example.myapplication.data.local.entity.TransactionEntity> filterTransactionsByPeriod(java.util.List<com.example.myapplication.data.local.entity.TransactionEntity> transactions, com.example.myapplication.ui.screen.home.AnalyticsPeriod period) {
        return null;
    }
    
    private final java.util.List<com.example.myapplication.ui.screen.home.CategoryData> calculateCategoryBreakdown(java.util.List<com.example.myapplication.data.local.entity.TransactionEntity> transactions) {
        return null;
    }
    
    private final java.util.List<com.example.myapplication.ui.screen.home.MonthlyData> calculateMonthlyTrend(java.util.List<com.example.myapplication.data.local.entity.TransactionEntity> transactions) {
        return null;
    }
}