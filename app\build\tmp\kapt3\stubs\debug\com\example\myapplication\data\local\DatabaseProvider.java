package com.example.myapplication.data.local;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0006\u0010\t\u001a\u00020\nJ\u000e\u0010\u000b\u001a\u00020\u00042\u0006\u0010\f\u001a\u00020\rJ\u0010\u0010\u000e\u001a\u00020\n2\u0006\u0010\u000f\u001a\u00020\u0010H\u0002R\u0012\u0010\u0003\u001a\u0004\u0018\u00010\u0004X\u0082\u000e\u00a2\u0006\u0004\n\u0002\b\u0005R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0011"}, d2 = {"Lcom/example/myapplication/data/local/DatabaseProvider;", "", "()V", "INSTANCE", "Lcom/example/myapplication/data/local/AppDatabase;", "INSTANCE$1", "MIGRATION_1_2", "Landroidx/room/migration/Migration;", "MIGRATION_2_3", "clearDatabase", "", "getDatabase", "context", "Landroid/content/Context;", "insertDefaultCategories", "database", "Landroidx/sqlite/db/SupportSQLiteDatabase;", "app_debug"})
public final class DatabaseProvider {
    @kotlin.jvm.Volatile()
    @org.jetbrains.annotations.Nullable()
    private static volatile com.example.myapplication.data.local.AppDatabase INSTANCE$1;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.room.migration.Migration MIGRATION_1_2 = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.room.migration.Migration MIGRATION_2_3 = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.myapplication.data.local.DatabaseProvider INSTANCE = null;
    
    private DatabaseProvider() {
        super();
    }
    
    private final void insertDefaultCategories(androidx.sqlite.db.SupportSQLiteDatabase database) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.myapplication.data.local.AppDatabase getDatabase(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        return null;
    }
    
    public final void clearDatabase() {
    }
}