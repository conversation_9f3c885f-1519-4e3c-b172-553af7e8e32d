package com.example.myapplication.ui.screen.category_management;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\\\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0010\u000e\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u0002\n\u0002\b\b\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\b\n\u0002\u0010\u000b\n\u0002\b\u0005\b\u0007\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0006\u0010\u0019\u001a\u00020\u001aJ\u0006\u0010\u001b\u001a\u00020\u001aJ\u0006\u0010\u001c\u001a\u00020\u001aJ\u0006\u0010\u001d\u001a\u00020\u001aJ\u0006\u0010\u001e\u001a\u00020\u001aJ\u0006\u0010\u001f\u001a\u00020\u001aJ\b\u0010 \u001a\u00020\u001aH\u0002J\u000e\u0010!\u001a\u00020\u001a2\u0006\u0010\"\u001a\u00020#J\u0006\u0010$\u001a\u00020\u001aJ\u000e\u0010%\u001a\u00020\u001a2\u0006\u0010&\u001a\u00020\'J\u000e\u0010(\u001a\u00020\u001a2\u0006\u0010&\u001a\u00020\'J\u0006\u0010)\u001a\u00020\u001aJ\u000e\u0010*\u001a\u00020\u001a2\u0006\u0010+\u001a\u00020\fJ\u000e\u0010,\u001a\u00020\u001a2\u0006\u0010-\u001a\u00020\fJ\u000e\u0010.\u001a\u00020\u001a2\u0006\u0010/\u001a\u000200J\u000e\u00101\u001a\u00020\u001a2\u0006\u00102\u001a\u00020\fJ\u000e\u00103\u001a\u00020\u001a2\u0006\u00104\u001a\u00020\fR\u0014\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\b\u001a\b\u0012\u0004\u0012\u00020\t0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\n\u001a\b\u0012\u0004\u0012\u00020\f0\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000eR\u0017\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\f0\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u000eR\u000e\u0010\u0011\u001a\u00020\u0012X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\u00070\u0014\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0016R\u0017\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\t0\u0014\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0016\u00a8\u00065"}, d2 = {"Lcom/example/myapplication/ui/screen/category_management/CategoryManagementViewModel;", "Landroidx/lifecycle/AndroidViewModel;", "application", "Landroid/app/Application;", "(Landroid/app/Application;)V", "_createFormState", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/example/myapplication/ui/screen/category_management/CreateCategoryFormState;", "_uiState", "Lcom/example/myapplication/ui/screen/category_management/CategoryManagementUiState;", "availableColors", "", "", "getAvailableColors", "()Ljava/util/List;", "availableIcons", "getAvailableIcons", "categoryRepository", "Lcom/example/myapplication/data/repository/CategoryRepository;", "createFormState", "Lkotlinx/coroutines/flow/StateFlow;", "getCreateFormState", "()Lkotlinx/coroutines/flow/StateFlow;", "uiState", "getUiState", "clearMessages", "", "createCategory", "deleteCategory", "hideCreateCategoryDialog", "hideDeleteConfirmDialog", "hideEditCategoryDialog", "loadCategories", "selectTab", "tab", "Lcom/example/myapplication/ui/screen/category_management/CategoryType;", "showCreateCategoryDialog", "showDeleteConfirmDialog", "category", "Lcom/example/myapplication/data/local/entity/CategoryEntity;", "showEditCategoryDialog", "updateCategory", "updateFormBudgetLimit", "budgetLimit", "updateFormColor", "color", "updateFormHasBudgetLimit", "hasBudgetLimit", "", "updateFormIcon", "icon", "updateFormName", "name", "app_debug"})
public final class CategoryManagementViewModel extends androidx.lifecycle.AndroidViewModel {
    @org.jetbrains.annotations.NotNull()
    private final com.example.myapplication.data.repository.CategoryRepository categoryRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.example.myapplication.ui.screen.category_management.CategoryManagementUiState> _uiState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.example.myapplication.ui.screen.category_management.CategoryManagementUiState> uiState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.example.myapplication.ui.screen.category_management.CreateCategoryFormState> _createFormState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.example.myapplication.ui.screen.category_management.CreateCategoryFormState> createFormState = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<java.lang.String> availableIcons = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<java.lang.String> availableColors = null;
    
    public CategoryManagementViewModel(@org.jetbrains.annotations.NotNull()
    android.app.Application application) {
        super(null);
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.example.myapplication.ui.screen.category_management.CategoryManagementUiState> getUiState() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.example.myapplication.ui.screen.category_management.CreateCategoryFormState> getCreateFormState() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> getAvailableIcons() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> getAvailableColors() {
        return null;
    }
    
    private final void loadCategories() {
    }
    
    public final void selectTab(@org.jetbrains.annotations.NotNull()
    com.example.myapplication.ui.screen.category_management.CategoryType tab) {
    }
    
    public final void showCreateCategoryDialog() {
    }
    
    public final void hideCreateCategoryDialog() {
    }
    
    public final void showEditCategoryDialog(@org.jetbrains.annotations.NotNull()
    com.example.myapplication.data.local.entity.CategoryEntity category) {
    }
    
    public final void hideEditCategoryDialog() {
    }
    
    public final void showDeleteConfirmDialog(@org.jetbrains.annotations.NotNull()
    com.example.myapplication.data.local.entity.CategoryEntity category) {
    }
    
    public final void hideDeleteConfirmDialog() {
    }
    
    public final void updateFormName(@org.jetbrains.annotations.NotNull()
    java.lang.String name) {
    }
    
    public final void updateFormIcon(@org.jetbrains.annotations.NotNull()
    java.lang.String icon) {
    }
    
    public final void updateFormColor(@org.jetbrains.annotations.NotNull()
    java.lang.String color) {
    }
    
    public final void updateFormBudgetLimit(@org.jetbrains.annotations.NotNull()
    java.lang.String budgetLimit) {
    }
    
    public final void updateFormHasBudgetLimit(boolean hasBudgetLimit) {
    }
    
    public final void createCategory() {
    }
    
    public final void updateCategory() {
    }
    
    public final void deleteCategory() {
    }
    
    public final void clearMessages() {
    }
}