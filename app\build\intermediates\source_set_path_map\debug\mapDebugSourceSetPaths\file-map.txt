com.example.myapplication.app-room-ktx-2.6.1-0 C:\Users\<USER>\.gradle\caches\8.13\transforms\0867812d4e0aa8530afb6615d0ca0c7e\transformed\room-ktx-2.6.1\res
com.example.myapplication.app-graphics-path-1.0.1-1 C:\Users\<USER>\.gradle\caches\8.13\transforms\1026dfad8305588e2591181058bcc77f\transformed\graphics-path-1.0.1\res
com.example.myapplication.app-lifecycle-viewmodel-savedstate-2.8.3-2 C:\Users\<USER>\.gradle\caches\8.13\transforms\147163278766c7c649a9837254da51a6\transformed\lifecycle-viewmodel-savedstate-2.8.3\res
com.example.myapplication.app-lifecycle-viewmodel-ktx-2.8.3-3 C:\Users\<USER>\.gradle\caches\8.13\transforms\193c537bd3c020804a5ab81fa9ebdaea\transformed\lifecycle-viewmodel-ktx-2.8.3\res
com.example.myapplication.app-ui-release-4 C:\Users\<USER>\.gradle\caches\8.13\transforms\26eebbcdc50d214896ef44b17b2bbc06\transformed\ui-release\res
com.example.myapplication.app-navigation-runtime-2.7.5-5 C:\Users\<USER>\.gradle\caches\8.13\transforms\28ae45f51674f6dbb0ad6f3e2511a0ac\transformed\navigation-runtime-2.7.5\res
com.example.myapplication.app-activity-compose-1.8.2-6 C:\Users\<USER>\.gradle\caches\8.13\transforms\28b7027cecf2d1060b72f0475695a893\transformed\activity-compose-1.8.2\res
com.example.myapplication.app-ui-text-release-7 C:\Users\<USER>\.gradle\caches\8.13\transforms\2be05d334ca3e6ed8c27ea7d4a5d7794\transformed\ui-text-release\res
com.example.myapplication.app-room-runtime-2.6.1-8 C:\Users\<USER>\.gradle\caches\8.13\transforms\2c50770cc4a726c011ac2ec64c5cbb94\transformed\room-runtime-2.6.1\res
com.example.myapplication.app-core-ktx-1.13.1-9 C:\Users\<USER>\.gradle\caches\8.13\transforms\31bc785cdd2988276e37ffcb037f323c\transformed\core-ktx-1.13.1\res
com.example.myapplication.app-ui-unit-release-10 C:\Users\<USER>\.gradle\caches\8.13\transforms\39cb37e040178fedb8de734fc757c636\transformed\ui-unit-release\res
com.example.myapplication.app-sqlite-2.4.0-11 C:\Users\<USER>\.gradle\caches\8.13\transforms\404e51cfe3e0a1882fecbe77479c8bad\transformed\sqlite-2.4.0\res
com.example.myapplication.app-lifecycle-viewmodel-compose-release-12 C:\Users\<USER>\.gradle\caches\8.13\transforms\42b64895ea1101a51859a89a1e7a2180\transformed\lifecycle-viewmodel-compose-release\res
com.example.myapplication.app-animation-release-13 C:\Users\<USER>\.gradle\caches\8.13\transforms\432ada7dcf0b658b1f6ee077a99ba580\transformed\animation-release\res
com.example.myapplication.app-lifecycle-runtime-release-14 C:\Users\<USER>\.gradle\caches\8.13\transforms\4bec5a9c2b80d27ace899d72d2ed9dda\transformed\lifecycle-runtime-release\res
com.example.myapplication.app-material3-release-15 C:\Users\<USER>\.gradle\caches\8.13\transforms\4d46e445b81c8f129aed0f2227fc7b91\transformed\material3-release\res
com.example.myapplication.app-lifecycle-process-2.8.3-16 C:\Users\<USER>\.gradle\caches\8.13\transforms\4f4ec2d7a3243f5c26bdbadb2bfbb96a\transformed\lifecycle-process-2.8.3\res
com.example.myapplication.app-navigation-compose-2.7.5-17 C:\Users\<USER>\.gradle\caches\8.13\transforms\51e654d808348192c29d81cd4a1fb3fd\transformed\navigation-compose-2.7.5\res
com.example.myapplication.app-navigation-runtime-ktx-2.7.5-18 C:\Users\<USER>\.gradle\caches\8.13\transforms\574aed56d34f84b8aa7c542ed4664077\transformed\navigation-runtime-ktx-2.7.5\res
com.example.myapplication.app-runtime-saveable-release-19 C:\Users\<USER>\.gradle\caches\8.13\transforms\626396d30b324875b132c5d9ab0db05c\transformed\runtime-saveable-release\res
com.example.myapplication.app-activity-1.8.2-20 C:\Users\<USER>\.gradle\caches\8.13\transforms\6b180c1018d7cdab2d0dec8e17734a2f\transformed\activity-1.8.2\res
com.example.myapplication.app-lifecycle-runtime-ktx-release-21 C:\Users\<USER>\.gradle\caches\8.13\transforms\7a18a7088b1ef8860ac9b4e128eb7eea\transformed\lifecycle-runtime-ktx-release\res
com.example.myapplication.app-material-ripple-release-22 C:\Users\<USER>\.gradle\caches\8.13\transforms\809ba3429ef999f977d01ae757d67a6f\transformed\material-ripple-release\res
com.example.myapplication.app-ui-tooling-data-release-23 C:\Users\<USER>\.gradle\caches\8.13\transforms\83f482ec1d57342d36924f2007e69f92\transformed\ui-tooling-data-release\res
com.example.myapplication.app-ui-graphics-release-24 C:\Users\<USER>\.gradle\caches\8.13\transforms\870c159c80d473f70ff3ee1f05111b1a\transformed\ui-graphics-release\res
com.example.myapplication.app-activity-ktx-1.8.2-25 C:\Users\<USER>\.gradle\caches\8.13\transforms\8eb7148dad3292186f35045439680746\transformed\activity-ktx-1.8.2\res
com.example.myapplication.app-startup-runtime-1.1.1-26 C:\Users\<USER>\.gradle\caches\8.13\transforms\9072ad6d0c09a55e2785fec65bf8e2b6\transformed\startup-runtime-1.1.1\res
com.example.myapplication.app-savedstate-1.2.1-27 C:\Users\<USER>\.gradle\caches\8.13\transforms\9096e8ba57401023e17dd710faf4e484\transformed\savedstate-1.2.1\res
com.example.myapplication.app-foundation-layout-release-28 C:\Users\<USER>\.gradle\caches\8.13\transforms\94e122cb6c7525f77fd67c32076b7fa2\transformed\foundation-layout-release\res
com.example.myapplication.app-ui-test-manifest-1.7.0-29 C:\Users\<USER>\.gradle\caches\8.13\transforms\95868c8a686a3a032ad8c267d34bccc8\transformed\ui-test-manifest-1.7.0\res
com.example.myapplication.app-material-release-30 C:\Users\<USER>\.gradle\caches\8.13\transforms\9aa85de5c6eb5b44bf809076a11f77b5\transformed\material-release\res
com.example.myapplication.app-navigation-common-ktx-2.7.5-31 C:\Users\<USER>\.gradle\caches\8.13\transforms\a0fa00574a1ad40c4f2e4b72e39d2da1\transformed\navigation-common-ktx-2.7.5\res
com.example.myapplication.app-emoji2-1.3.0-32 C:\Users\<USER>\.gradle\caches\8.13\transforms\a511498e51ca6fddfc2c08ea5511bda9\transformed\emoji2-1.3.0\res
com.example.myapplication.app-ui-tooling-preview-release-33 C:\Users\<USER>\.gradle\caches\8.13\transforms\a595cd694b407463cda97c062b101c70\transformed\ui-tooling-preview-release\res
com.example.myapplication.app-animation-core-release-34 C:\Users\<USER>\.gradle\caches\8.13\transforms\acc15c247ae0e0fde5336a31568d261f\transformed\animation-core-release\res
com.example.myapplication.app-lifecycle-viewmodel-release-35 C:\Users\<USER>\.gradle\caches\8.13\transforms\ad6b1b1276416534966059221833e5be\transformed\lifecycle-viewmodel-release\res
com.example.myapplication.app-navigation-common-2.7.5-36 C:\Users\<USER>\.gradle\caches\8.13\transforms\ad878ab306a66d3ccfdb82182906189a\transformed\navigation-common-2.7.5\res
com.example.myapplication.app-sqlite-framework-2.4.0-37 C:\Users\<USER>\.gradle\caches\8.13\transforms\b020bed62e2ecfbf28155fb0db83b92d\transformed\sqlite-framework-2.4.0\res
com.example.myapplication.app-core-1.13.1-38 C:\Users\<USER>\.gradle\caches\8.13\transforms\ba5970ab7047a5364a7daf3d65e8c0dd\transformed\core-1.13.1\res
com.example.myapplication.app-ui-util-release-39 C:\Users\<USER>\.gradle\caches\8.13\transforms\ba59b7a115eb9b408968994f1416a20b\transformed\ui-util-release\res
com.example.myapplication.app-ui-geometry-release-40 C:\Users\<USER>\.gradle\caches\8.13\transforms\ca394f7f71d9a34fb0d4fc521a4e41fc\transformed\ui-geometry-release\res
com.example.myapplication.app-customview-poolingcontainer-1.0.0-41 C:\Users\<USER>\.gradle\caches\8.13\transforms\cdd5919cfdf99fb7e7ee3aaff8d1e460\transformed\customview-poolingcontainer-1.0.0\res
com.example.myapplication.app-savedstate-ktx-1.2.1-42 C:\Users\<USER>\.gradle\caches\8.13\transforms\cdecf90bca375d663d031abab92f87b5\transformed\savedstate-ktx-1.2.1\res
com.example.myapplication.app-annotation-experimental-1.4.0-43 C:\Users\<USER>\.gradle\caches\8.13\transforms\d915cd64347a90123d88865ba879e002\transformed\annotation-experimental-1.4.0\res
com.example.myapplication.app-core-runtime-2.2.0-44 C:\Users\<USER>\.gradle\caches\8.13\transforms\dee5f33e982ea9634c5ba21e47d19f41\transformed\core-runtime-2.2.0\res
com.example.myapplication.app-lifecycle-runtime-compose-release-45 C:\Users\<USER>\.gradle\caches\8.13\transforms\e25ffcf9b79136870248d18358d776db\transformed\lifecycle-runtime-compose-release\res
com.example.myapplication.app-material-icons-core-release-46 C:\Users\<USER>\.gradle\caches\8.13\transforms\e4df75657d99d401f406bf60a74a0895\transformed\material-icons-core-release\res
com.example.myapplication.app-profileinstaller-1.3.1-47 C:\Users\<USER>\.gradle\caches\8.13\transforms\e6be7216d7b4c46900b8deed99b35237\transformed\profileinstaller-1.3.1\res
com.example.myapplication.app-lifecycle-livedata-core-2.8.3-48 C:\Users\<USER>\.gradle\caches\8.13\transforms\f4e467baf68f493acf9de8bfcffd7eb7\transformed\lifecycle-livedata-core-2.8.3\res
com.example.myapplication.app-foundation-release-49 C:\Users\<USER>\.gradle\caches\8.13\transforms\f697d2cda204ad860889257d58a63850\transformed\foundation-release\res
com.example.myapplication.app-ui-tooling-release-50 C:\Users\<USER>\.gradle\caches\8.13\transforms\f8b82bf74cb0994cbb19ff17592f6518\transformed\ui-tooling-release\res
com.example.myapplication.app-runtime-release-51 C:\Users\<USER>\.gradle\caches\8.13\transforms\fad6174f7ae4675005ba6c43e6f845c5\transformed\runtime-release\res
com.example.myapplication.app-pngs-52 C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\build\generated\res\pngs\debug
com.example.myapplication.app-resValues-53 C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\build\generated\res\resValues\debug
com.example.myapplication.app-packageDebugResources-54 C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\build\intermediates\incremental\debug\packageDebugResources\merged.dir
com.example.myapplication.app-packageDebugResources-55 C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\build\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.example.myapplication.app-debug-56 C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\build\intermediates\merged_res\debug\mergeDebugResources
com.example.myapplication.app-debug-57 C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\debug\res
com.example.myapplication.app-main-58 C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\res
