package com.example.myapplication.data.local.entity

import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "categories")
data class CategoryEntity(
    @PrimaryKey(autoGenerate = true) val id: Int = 0,
    val name: String,
    val type: String, // "income" or "expense"
    val icon: String = "category", // Icon identifier
    val color: String = "#757575", // Default gray color
    val isDefault: Boolean = false, // Whether it's a system default category
    val isActive: Boolean = true,
    val budgetLimit: Double? = null, // Optional budget limit for expense categories
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis()
)
