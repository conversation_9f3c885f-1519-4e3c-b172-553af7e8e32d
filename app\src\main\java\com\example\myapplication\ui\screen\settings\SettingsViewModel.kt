package com.example.myapplication.ui.screen.settings

import android.app.Application
import android.content.Context
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.example.myapplication.data.repository.RepositoryProvider
import com.example.myapplication.data.repository.WalletRepository
import com.example.myapplication.data.repository.TransactionRepository
import com.example.myapplication.utils.ErrorHandler
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

data class SettingsUiState(
    val isLoading: Boolean = false,
    val errorMessage: String? = null,
    val successMessage: String? = null,
    val showClearDataDialog: Boolean = false,
    val showExportDataDialog: Boolean = false,
    val totalWallets: Int = 0,
    val totalTransactions: Int = 0,
    val totalBalance: Double = 0.0,
    val appVersion: String = "1.0.0",
    val defaultCurrency: String = "VND",
    val isDarkTheme: Boolean = false,
    val isNotificationsEnabled: Boolean = true
)

class SettingsViewModel(application: Application) : AndroidViewModel(application) {
    
    private val walletRepository: WalletRepository = RepositoryProvider.provideWalletRepository(application)
    private val transactionRepository: TransactionRepository = RepositoryProvider.provideTransactionRepository(application)
    private val sharedPreferences = application.getSharedPreferences("app_settings", Context.MODE_PRIVATE)
    
    private val _uiState = MutableStateFlow(SettingsUiState())
    val uiState: StateFlow<SettingsUiState> = _uiState.asStateFlow()
    
    init {
        loadSettings()
        loadStatistics()
    }
    
    private fun loadSettings() {
        val defaultCurrency = sharedPreferences.getString("default_currency", "VND") ?: "VND"
        val isDarkTheme = sharedPreferences.getBoolean("dark_theme", false)
        val isNotificationsEnabled = sharedPreferences.getBoolean("notifications_enabled", true)
        
        _uiState.value = _uiState.value.copy(
            defaultCurrency = defaultCurrency,
            isDarkTheme = isDarkTheme,
            isNotificationsEnabled = isNotificationsEnabled
        )
    }
    
    private fun loadStatistics() {
        viewModelScope.launch {
            try {
                val totalBalance = walletRepository.getTotalBalance()
                val totalWallets = walletRepository.getActiveWalletCount()
                
                // Get total transactions count (you'll need to add this method to TransactionRepository)
                val totalTransactions = 0 // Placeholder
                
                _uiState.value = _uiState.value.copy(
                    totalBalance = totalBalance,
                    totalWallets = totalWallets,
                    totalTransactions = totalTransactions
                )
            } catch (e: Exception) {
                ErrorHandler.logError("SettingsViewModel", "Error loading statistics", e)
            }
        }
    }
    
    fun updateDefaultCurrency(currency: String) {
        sharedPreferences.edit().putString("default_currency", currency).apply()
        _uiState.value = _uiState.value.copy(defaultCurrency = currency)
    }
    
    fun updateDarkTheme(isDark: Boolean) {
        sharedPreferences.edit().putBoolean("dark_theme", isDark).apply()
        _uiState.value = _uiState.value.copy(isDarkTheme = isDark)
    }
    
    fun updateNotifications(enabled: Boolean) {
        sharedPreferences.edit().putBoolean("notifications_enabled", enabled).apply()
        _uiState.value = _uiState.value.copy(isNotificationsEnabled = enabled)
    }
    
    fun showClearDataDialog() {
        _uiState.value = _uiState.value.copy(showClearDataDialog = true)
    }
    
    fun hideClearDataDialog() {
        _uiState.value = _uiState.value.copy(showClearDataDialog = false)
    }
    
    fun showExportDataDialog() {
        _uiState.value = _uiState.value.copy(showExportDataDialog = true)
    }
    
    fun hideExportDataDialog() {
        _uiState.value = _uiState.value.copy(showExportDataDialog = false)
    }
    
    fun clearAllData() {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true)
                
                // Clear all data (you'll need to implement these methods)
                // walletRepository.clearAllWallets()
                // transactionRepository.clearAllTransactions()
                
                // Reset to default wallets
                walletRepository.createDefaultWalletsIfNeeded()
                
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    showClearDataDialog = false,
                    successMessage = "Dữ liệu đã được xóa thành công"
                )
                
                loadStatistics()
                
            } catch (e: Exception) {
                ErrorHandler.logError("SettingsViewModel", "Error clearing data", e)
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    errorMessage = ErrorHandler.getErrorMessage(e)
                )
            }
        }
    }
    
    fun exportData() {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true)
                
                // Implement data export functionality
                // This would typically create a JSON or CSV file with all data
                
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    showExportDataDialog = false,
                    successMessage = "Dữ liệu đã được xuất thành công"
                )
                
            } catch (e: Exception) {
                ErrorHandler.logError("SettingsViewModel", "Error exporting data", e)
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    errorMessage = ErrorHandler.getErrorMessage(e)
                )
            }
        }
    }
    
    fun clearMessages() {
        _uiState.value = _uiState.value.copy(
            errorMessage = null,
            successMessage = null
        )
    }
}
