package com.example.myapplication.data.repository

import com.example.myapplication.data.local.dao.TransactionDao
import com.example.myapplication.data.local.entity.TransactionEntity
import kotlinx.coroutines.flow.Flow

class TransactionRepository(private val transactionDao: TransactionDao) {

    fun getAllTransactions(): Flow<List<TransactionEntity>> = transactionDao.getAllTransactions()

    fun getTransactionsByWallet(walletId: Int): Flow<List<TransactionEntity>> =
        transactionDao.getTransactionsByWallet(walletId)

    fun getTransactionsByType(type: String): Flow<List<TransactionEntity>> =
        transactionDao.getTransactionsByType(type)

    suspend fun getTotalExpenses(walletId: Int): Double =
        transactionDao.getTotalExpenses(walletId) ?: 0.0

    suspend fun getTotalIncome(walletId: Int): Double =
        transactionDao.getTotalIncome(walletId) ?: 0.0

    suspend fun insertTransaction(transaction: TransactionEntity): Long =
        transactionDao.insertTransaction(transaction)

    suspend fun updateTransaction(transaction: TransactionEntity) =
        transactionDao.updateTransaction(transaction)

    suspend fun deleteTransaction(transaction: TransactionEntity) =
        transactionDao.deleteTransaction(transaction)

    suspend fun addExpense(
        walletId: Int,
        category: String,
        amount: Double,
        note: String? = null,
        categoryId: Int? = null
    ): TransactionEntity {
        val transaction = TransactionEntity(
            walletId = walletId,
            categoryId = categoryId,
            type = "expense",
            category = category,
            amount = amount,
            note = note,
            date = System.currentTimeMillis()
        )
        insertTransaction(transaction)
        return transaction
    }

    suspend fun addIncome(
        walletId: Int,
        category: String,
        amount: Double,
        note: String? = null,
        categoryId: Int? = null
    ): TransactionEntity {
        val transaction = TransactionEntity(
            walletId = walletId,
            categoryId = categoryId,
            type = "income",
            category = category,
            amount = amount,
            note = note,
            date = System.currentTimeMillis()
        )
        insertTransaction(transaction)
        return transaction
    }

    suspend fun clearAllTransactions() {
        val allTransactions = transactionDao.getAllTransactionsSync()
        allTransactions.forEach { transaction ->
            transactionDao.deleteTransaction(transaction)
        }
    }
}
