package com.example.myapplication.data.repository

import com.example.myapplication.data.local.dao.TransactionDao
import com.example.myapplication.data.local.entity.TransactionEntity

class TransactionRepository(private val transactionDao: TransactionDao) {
    fun getTransactions() = transactionDao.getAllTransactions()
    suspend fun insertTransaction(transaction: TransactionEntity) = transactionDao.insertTransaction(transaction)
}
