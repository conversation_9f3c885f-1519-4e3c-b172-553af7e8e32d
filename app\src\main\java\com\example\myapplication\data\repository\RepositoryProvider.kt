package com.example.myapplication.data.repository

import android.content.Context
import com.example.myapplication.data.local.DatabaseProvider

object RepositoryProvider {
    
    fun provideWalletRepository(context: Context): WalletRepository {
        val database = DatabaseProvider.getDatabase(context)
        return WalletRepository(database.walletDao())
    }
    
    fun provideTransactionRepository(context: Context): TransactionRepository {
        val database = DatabaseProvider.getDatabase(context)
        return TransactionRepository(database.transactionDao())
    }
}
