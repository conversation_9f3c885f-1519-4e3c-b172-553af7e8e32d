package com.example.myapplication.ui.screen.wallet_management

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.example.myapplication.data.local.entity.WalletEntity
import com.example.myapplication.data.repository.RepositoryProvider
import com.example.myapplication.data.repository.WalletRepository
import com.example.myapplication.utils.ErrorHandler
import com.example.myapplication.utils.ValidationUtils
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

data class WalletManagementUiState(
    val wallets: List<WalletEntity> = emptyList(),
    val isLoading: Boolean = false,
    val errorMessage: String? = null,
    val successMessage: String? = null,
    val showCreateWalletDialog: Boolean = false,
    val showEditWalletDialog: Boolean = false,
    val showDeleteConfirmDialog: Boolean = false,
    val selectedWallet: WalletEntity? = null,
    val totalBalance: Double = 0.0,
    val activeWalletCount: Int = 0
)

data class CreateWalletFormState(
    val name: String = "",
    val initialBalance: String = "0",
    val currency: String = "VND",
    val description: String = "",
    val selectedColor: String = "#1976D2",
    val selectedIcon: String = "wallet"
)

class WalletManagementViewModel(application: Application) : AndroidViewModel(application) {
    
    private val walletRepository: WalletRepository = RepositoryProvider.provideWalletRepository(application)
    
    private val _uiState = MutableStateFlow(WalletManagementUiState())
    val uiState: StateFlow<WalletManagementUiState> = _uiState.asStateFlow()
    
    private val _createFormState = MutableStateFlow(CreateWalletFormState())
    val createFormState: StateFlow<CreateWalletFormState> = _createFormState.asStateFlow()
    
    // Available colors for wallets
    val availableColors = listOf(
        "#1976D2", "#388E3C", "#F57C00", "#7B1FA2",
        "#C62828", "#00796B", "#5D4037", "#455A64",
        "#E91E63", "#FF5722", "#607D8B", "#795548"
    )
    
    // Available icons for wallets
    val availableIcons = listOf(
        "wallet", "account_balance", "credit_card", "savings",
        "account_balance_wallet", "payment", "money", "local_atm"
    )
    
    init {
        loadWallets()
        loadStatistics()
    }
    
    private fun loadWallets() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true)
            try {
                walletRepository.getAllWallets().collect { wallets ->
                    _uiState.value = _uiState.value.copy(
                        wallets = wallets,
                        isLoading = false,
                        errorMessage = null
                    )
                }
            } catch (e: Exception) {
                ErrorHandler.logError("WalletManagementViewModel", "Error loading wallets", e)
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    errorMessage = ErrorHandler.getErrorMessage(e)
                )
            }
        }
    }
    
    private fun loadStatistics() {
        viewModelScope.launch {
            try {
                val totalBalance = walletRepository.getTotalBalance()
                val activeCount = walletRepository.getActiveWalletCount()
                
                _uiState.value = _uiState.value.copy(
                    totalBalance = totalBalance,
                    activeWalletCount = activeCount
                )
            } catch (e: Exception) {
                ErrorHandler.logError("WalletManagementViewModel", "Error loading statistics", e)
            }
        }
    }
    
    fun showCreateWalletDialog() {
        _uiState.value = _uiState.value.copy(showCreateWalletDialog = true)
        _createFormState.value = CreateWalletFormState()
    }
    
    fun hideCreateWalletDialog() {
        _uiState.value = _uiState.value.copy(showCreateWalletDialog = false)
    }
    
    fun showEditWalletDialog(wallet: WalletEntity) {
        _uiState.value = _uiState.value.copy(
            showEditWalletDialog = true,
            selectedWallet = wallet
        )
        _createFormState.value = CreateWalletFormState(
            name = wallet.name,
            initialBalance = wallet.balance.toString(),
            currency = wallet.currency,
            description = wallet.description ?: "",
            selectedColor = wallet.color,
            selectedIcon = wallet.icon
        )
    }
    
    fun hideEditWalletDialog() {
        _uiState.value = _uiState.value.copy(
            showEditWalletDialog = false,
            selectedWallet = null
        )
    }
    
    fun showDeleteConfirmDialog(wallet: WalletEntity) {
        _uiState.value = _uiState.value.copy(
            showDeleteConfirmDialog = true,
            selectedWallet = wallet
        )
    }
    
    fun hideDeleteConfirmDialog() {
        _uiState.value = _uiState.value.copy(
            showDeleteConfirmDialog = false,
            selectedWallet = null
        )
    }
    
    fun updateFormName(name: String) {
        _createFormState.value = _createFormState.value.copy(name = ValidationUtils.sanitizeInput(name))
    }
    
    fun updateFormBalance(balance: String) {
        _createFormState.value = _createFormState.value.copy(initialBalance = balance)
    }
    
    fun updateFormCurrency(currency: String) {
        _createFormState.value = _createFormState.value.copy(currency = currency)
    }
    
    fun updateFormDescription(description: String) {
        _createFormState.value = _createFormState.value.copy(description = ValidationUtils.sanitizeInput(description))
    }
    
    fun updateFormColor(color: String) {
        _createFormState.value = _createFormState.value.copy(selectedColor = color)
    }
    
    fun updateFormIcon(icon: String) {
        _createFormState.value = _createFormState.value.copy(selectedIcon = icon)
    }
    
    fun createWallet() {
        viewModelScope.launch {
            val formState = _createFormState.value

            // Validation
            val nameValidation = ValidationUtils.validateWalletName(formState.name)
            if (!nameValidation.isValid) {
                _uiState.value = _uiState.value.copy(errorMessage = nameValidation.errorMessage)
                return@launch
            }

            val balanceValidation = ValidationUtils.validateAmount(formState.initialBalance)
            if (!balanceValidation.isValid) {
                _uiState.value = _uiState.value.copy(errorMessage = balanceValidation.errorMessage)
                return@launch
            }

            try {
                _uiState.value = _uiState.value.copy(isLoading = true)

                walletRepository.createWallet(
                    name = formState.name,
                    initialBalance = formState.initialBalance.toDouble(),
                    currency = formState.currency,
                    description = formState.description.ifBlank { null },
                    color = formState.selectedColor,
                    icon = formState.selectedIcon
                )

                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    showCreateWalletDialog = false,
                    successMessage = "Ví đã được tạo thành công"
                )

                loadStatistics()

            } catch (e: Exception) {
                ErrorHandler.logError("WalletManagementViewModel", "Error creating wallet", e)
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    errorMessage = ErrorHandler.getErrorMessage(e)
                )
            }
        }
    }

    fun updateWallet() {
        viewModelScope.launch {
            val formState = _createFormState.value
            val selectedWallet = _uiState.value.selectedWallet ?: return@launch

            // Validation
            val nameValidation = ValidationUtils.validateWalletName(formState.name)
            if (!nameValidation.isValid) {
                _uiState.value = _uiState.value.copy(errorMessage = nameValidation.errorMessage)
                return@launch
            }

            try {
                _uiState.value = _uiState.value.copy(isLoading = true)

                val updatedWallet = selectedWallet.copy(
                    name = formState.name,
                    currency = formState.currency,
                    description = formState.description.ifBlank { null },
                    color = formState.selectedColor,
                    icon = formState.selectedIcon,
                    updatedAt = System.currentTimeMillis()
                )

                walletRepository.updateWallet(updatedWallet)

                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    showEditWalletDialog = false,
                    selectedWallet = null,
                    successMessage = "Ví đã được cập nhật thành công"
                )

                loadStatistics()

            } catch (e: Exception) {
                ErrorHandler.logError("WalletManagementViewModel", "Error updating wallet", e)
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    errorMessage = ErrorHandler.getErrorMessage(e)
                )
            }
        }
    }

    fun deleteWallet() {
        viewModelScope.launch {
            val selectedWallet = _uiState.value.selectedWallet ?: return@launch

            try {
                _uiState.value = _uiState.value.copy(isLoading = true)

                val canDelete = walletRepository.deactivateWallet(selectedWallet.id)
                if (!canDelete) {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        errorMessage = "Không thể xóa ví cuối cùng"
                    )
                    return@launch
                }

                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    showDeleteConfirmDialog = false,
                    selectedWallet = null,
                    successMessage = "Ví đã được xóa thành công"
                )

                loadStatistics()

            } catch (e: Exception) {
                ErrorHandler.logError("WalletManagementViewModel", "Error deleting wallet", e)
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    errorMessage = ErrorHandler.getErrorMessage(e)
                )
            }
        }
    }

    fun toggleWalletStatus(wallet: WalletEntity) {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true)

                if (wallet.isActive) {
                    val canDeactivate = walletRepository.deactivateWallet(wallet.id)
                    if (!canDeactivate) {
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            errorMessage = "Không thể vô hiệu hóa ví cuối cùng"
                        )
                        return@launch
                    }
                } else {
                    walletRepository.activateWallet(wallet.id)
                }

                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    successMessage = if (wallet.isActive) "Ví đã được vô hiệu hóa" else "Ví đã được kích hoạt"
                )

                loadStatistics()

            } catch (e: Exception) {
                ErrorHandler.logError("WalletManagementViewModel", "Error toggling wallet status", e)
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    errorMessage = ErrorHandler.getErrorMessage(e)
                )
            }
        }
    }

    fun toggleWalletFrozenStatus(wallet: WalletEntity) {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true)

                walletRepository.updateFrozenStatus(wallet.id, !wallet.isFrozen)

                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    successMessage = if (wallet.isFrozen) "Ví đã được mở khóa" else "Ví đã được đóng băng"
                )

            } catch (e: Exception) {
                ErrorHandler.logError("WalletManagementViewModel", "Error toggling wallet frozen status", e)
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    errorMessage = ErrorHandler.getErrorMessage(e)
                )
            }
        }
    }

    fun clearMessages() {
        _uiState.value = _uiState.value.copy(
            errorMessage = null,
            successMessage = null
        )
    }
    

}
