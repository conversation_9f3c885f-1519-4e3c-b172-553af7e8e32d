package com.example.myapplication

import androidx.room.Room
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.example.myapplication.data.local.AppDatabase
import com.example.myapplication.data.local.dao.TransactionDao
import com.example.myapplication.data.local.dao.WalletDao
import com.example.myapplication.data.local.entity.TransactionEntity
import com.example.myapplication.data.local.entity.WalletEntity
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.runBlocking
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.Assert.*

@RunWith(AndroidJUnit4::class)
class DatabaseTest {
    
    private lateinit var database: AppDatabase
    private lateinit var walletDao: WalletDao
    private lateinit var transactionDao: TransactionDao
    
    @Before
    fun createDb() {
        database = Room.inMemoryDatabaseBuilder(
            ApplicationProvider.getApplicationContext(),
            AppDatabase::class.java
        ).build()
        walletDao = database.walletDao()
        transactionDao = database.transactionDao()
    }
    
    @After
    fun closeDb() {
        database.close()
    }
    
    @Test
    fun insertAndGetWallet() = runBlocking {
        val wallet = WalletEntity(name = "Test Wallet", balance = 1000.0)
        val walletId = walletDao.insertWallet(wallet)
        
        val retrievedWallet = walletDao.getWalletById(walletId.toInt())
        assertNotNull(retrievedWallet)
        assertEquals("Test Wallet", retrievedWallet?.name)
        assertEquals(1000.0, retrievedWallet?.balance, 0.01)
    }
    
    @Test
    fun updateWalletBalance() = runBlocking {
        val wallet = WalletEntity(name = "Test Wallet", balance = 1000.0)
        val walletId = walletDao.insertWallet(wallet)
        
        walletDao.updateBalance(walletId.toInt(), 500.0)
        
        val updatedWallet = walletDao.getWalletById(walletId.toInt())
        assertEquals(1500.0, updatedWallet?.balance, 0.01)
    }
    
    @Test
    fun insertAndGetTransaction() = runBlocking {
        val wallet = WalletEntity(name = "Test Wallet", balance = 1000.0)
        val walletId = walletDao.insertWallet(wallet)
        
        val transaction = TransactionEntity(
            walletId = walletId.toInt(),
            type = "expense",
            category = "Food",
            amount = 50.0,
            note = "Lunch"
        )
        
        transactionDao.insertTransaction(transaction)
        
        val transactions = transactionDao.getAllTransactions().first()
        assertEquals(1, transactions.size)
        assertEquals("Food", transactions[0].category)
        assertEquals(50.0, transactions[0].amount, 0.01)
    }
    
    @Test
    fun getTransactionsByWallet() = runBlocking {
        val wallet1 = WalletEntity(name = "Wallet 1", balance = 1000.0)
        val wallet2 = WalletEntity(name = "Wallet 2", balance = 2000.0)
        val walletId1 = walletDao.insertWallet(wallet1)
        val walletId2 = walletDao.insertWallet(wallet2)
        
        val transaction1 = TransactionEntity(
            walletId = walletId1.toInt(),
            type = "expense",
            category = "Food",
            amount = 50.0
        )
        val transaction2 = TransactionEntity(
            walletId = walletId2.toInt(),
            type = "income",
            category = "Salary",
            amount = 1000.0
        )
        
        transactionDao.insertTransaction(transaction1)
        transactionDao.insertTransaction(transaction2)
        
        val wallet1Transactions = transactionDao.getTransactionsByWallet(walletId1.toInt()).first()
        val wallet2Transactions = transactionDao.getTransactionsByWallet(walletId2.toInt()).first()
        
        assertEquals(1, wallet1Transactions.size)
        assertEquals(1, wallet2Transactions.size)
        assertEquals("Food", wallet1Transactions[0].category)
        assertEquals("Salary", wallet2Transactions[0].category)
    }
    
    @Test
    fun getTotalExpensesAndIncome() = runBlocking {
        val wallet = WalletEntity(name = "Test Wallet", balance = 1000.0)
        val walletId = walletDao.insertWallet(wallet)
        
        val expense1 = TransactionEntity(
            walletId = walletId.toInt(),
            type = "expense",
            category = "Food",
            amount = 50.0
        )
        val expense2 = TransactionEntity(
            walletId = walletId.toInt(),
            type = "expense",
            category = "Transport",
            amount = 30.0
        )
        val income = TransactionEntity(
            walletId = walletId.toInt(),
            type = "income",
            category = "Salary",
            amount = 1000.0
        )
        
        transactionDao.insertTransaction(expense1)
        transactionDao.insertTransaction(expense2)
        transactionDao.insertTransaction(income)
        
        val totalExpenses = transactionDao.getTotalExpenses(walletId.toInt())
        val totalIncome = transactionDao.getTotalIncome(walletId.toInt())
        
        assertEquals(80.0, totalExpenses ?: 0.0, 0.01)
        assertEquals(1000.0, totalIncome ?: 0.0, 0.01)
    }
}
