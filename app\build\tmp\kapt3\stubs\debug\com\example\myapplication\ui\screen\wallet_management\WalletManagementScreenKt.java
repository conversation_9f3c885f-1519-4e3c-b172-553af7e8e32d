package com.example.myapplication.ui.screen.wallet_management;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000L\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0006\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0000\u001a\u00c8\u0001\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\f\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\b0\u00072\f\u0010\t\u001a\b\u0012\u0004\u0012\u00020\b0\u00072\u0012\u0010\n\u001a\u000e\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\u00010\u000b2\u0012\u0010\f\u001a\u000e\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\u00010\u000b2\u0012\u0010\r\u001a\u000e\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\u00010\u000b2\u0012\u0010\u000e\u001a\u000e\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\u00010\u000b2\u0012\u0010\u000f\u001a\u000e\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\u00010\u000b2\u0012\u0010\u0010\u001a\u000e\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\u00010\u000b2\f\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00010\u00122\f\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\u00010\u0012H\u0003\u001a\u0018\u0010\u0014\u001a\u00020\u00012\u0006\u0010\u0015\u001a\u00020\u00162\u0006\u0010\u0017\u001a\u00020\u0018H\u0003\u001a:\u0010\u0019\u001a\u00020\u00012\u0006\u0010\u001a\u001a\u00020\u001b2\f\u0010\u001c\u001a\b\u0012\u0004\u0012\u00020\u00010\u00122\f\u0010\u001d\u001a\b\u0012\u0004\u0012\u00020\u00010\u00122\f\u0010\u001e\u001a\b\u0012\u0004\u0012\u00020\u00010\u0012H\u0003\u001a\"\u0010\u001f\u001a\u00020\u00012\u000e\b\u0002\u0010 \u001a\b\u0012\u0004\u0012\u00020\u00010\u00122\b\b\u0002\u0010!\u001a\u00020\"H\u0007\u00a8\u0006#"}, d2 = {"CreateEditWalletDialog", "", "isEdit", "", "formState", "Lcom/example/myapplication/ui/screen/wallet_management/CreateWalletFormState;", "availableColors", "", "", "availableIcons", "onNameChange", "Lkotlin/Function1;", "onBalanceChange", "onCurrencyChange", "onDescriptionChange", "onColorChange", "onIconChange", "onConfirm", "Lkotlin/Function0;", "onDismiss", "StatisticsSection", "totalBalance", "", "activeWalletCount", "", "WalletItem", "wallet", "Lcom/example/myapplication/data/local/entity/WalletEntity;", "onEditClick", "onDeleteClick", "onToggleFrozen", "WalletManagementScreen", "onNavigateBack", "viewModel", "Lcom/example/myapplication/ui/screen/wallet_management/WalletManagementViewModel;", "app_debug"})
public final class WalletManagementScreenKt {
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void WalletManagementScreen(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateBack, @org.jetbrains.annotations.NotNull()
    com.example.myapplication.ui.screen.wallet_management.WalletManagementViewModel viewModel) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void StatisticsSection(double totalBalance, int activeWalletCount) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void WalletItem(com.example.myapplication.data.local.entity.WalletEntity wallet, kotlin.jvm.functions.Function0<kotlin.Unit> onEditClick, kotlin.jvm.functions.Function0<kotlin.Unit> onDeleteClick, kotlin.jvm.functions.Function0<kotlin.Unit> onToggleFrozen) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void CreateEditWalletDialog(boolean isEdit, com.example.myapplication.ui.screen.wallet_management.CreateWalletFormState formState, java.util.List<java.lang.String> availableColors, java.util.List<java.lang.String> availableIcons, kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onNameChange, kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onBalanceChange, kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onCurrencyChange, kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onDescriptionChange, kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onColorChange, kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onIconChange, kotlin.jvm.functions.Function0<kotlin.Unit> onConfirm, kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss) {
    }
}