package com.example.myapplication.data.local.entity

import androidx.room.Entity
import androidx.room.ForeignKey
import androidx.room.PrimaryKey

@Entity(
    tableName = "transactions",
    foreignKeys = [ForeignKey(
        entity = WalletEntity::class,
        parentColumns = ["id"],
        childColumns = ["walletId"],
        onDelete = ForeignKey.CASCADE
    )]
)
data class TransactionEntity(
    @PrimaryKey(autoGenerate = true) val id: Int = 0,
    val walletId: Int,
    val type: String, // "income" or "expense"
    val category: String,
    val amount: Double,
    val note: String? = null,
    val date: Long = System.currentTimeMillis()
)
