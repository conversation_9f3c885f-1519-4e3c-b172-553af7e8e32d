package com.example.myapplication.ui.screen.category_management

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.example.myapplication.data.local.entity.CategoryEntity
import com.example.myapplication.utils.ValidationUtils

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CategoryManagementScreen(
    onNavigateBack: () -> Unit = {},
    viewModel: CategoryManagementViewModel = viewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    val formState by viewModel.createFormState.collectAsState()
    
    LaunchedEffect(uiState.successMessage) {
        if (uiState.successMessage != null) {
            kotlinx.coroutines.delay(2000)
            viewModel.clearMessages()
        }
    }
    
    LaunchedEffect(uiState.errorMessage) {
        if (uiState.errorMessage != null) {
            kotlinx.coroutines.delay(3000)
            viewModel.clearMessages()
        }
    }
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { 
                    Text(
                        "Quản lý danh mục",
                        fontWeight = FontWeight.Bold
                    ) 
                },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "Quay lại"
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = MaterialTheme.colorScheme.primaryContainer,
                    titleContentColor = MaterialTheme.colorScheme.onPrimaryContainer
                )
            )
        },
        floatingActionButton = {
            FloatingActionButton(
                onClick = { viewModel.showCreateCategoryDialog() },
                containerColor = MaterialTheme.colorScheme.primary
            ) {
                Icon(
                    imageVector = Icons.Default.Add,
                    contentDescription = "Tạo danh mục mới"
                )
            }
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // Tab selector
            TabRow(
                selectedTabIndex = if (uiState.selectedTab == CategoryType.EXPENSE) 0 else 1,
                containerColor = MaterialTheme.colorScheme.surfaceVariant
            ) {
                CategoryType.values().forEachIndexed { index, type ->
                    Tab(
                        selected = uiState.selectedTab == type,
                        onClick = { viewModel.selectTab(type) },
                        text = { Text(type.displayName) }
                    )
                }
            }
            
            // Success/Error messages
            uiState.successMessage?.let { message ->
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = Color(0xFF4CAF50).copy(alpha = 0.1f)
                    )
                ) {
                    Text(
                        text = message,
                        modifier = Modifier.padding(16.dp),
                        color = Color(0xFF2E7D32)
                    )
                }
            }
            
            uiState.errorMessage?.let { error ->
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.errorContainer
                    )
                ) {
                    Text(
                        text = error,
                        modifier = Modifier.padding(16.dp),
                        color = MaterialTheme.colorScheme.onErrorContainer
                    )
                }
            }
            
            // Categories list
            if (uiState.isLoading) {
                Box(
                    modifier = Modifier.fillMaxWidth(),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator()
                }
            } else {
                val categories = if (uiState.selectedTab == CategoryType.EXPENSE) {
                    uiState.expenseCategories
                } else {
                    uiState.incomeCategories
                }
                
                Text(
                    text = "${uiState.selectedTab.displayName} (${categories.size})",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
                
                LazyColumn(
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    items(categories) { category ->
                        CategoryItem(
                            category = category,
                            onEditClick = { viewModel.showEditCategoryDialog(category) },
                            onDeleteClick = { viewModel.showDeleteConfirmDialog(category) }
                        )
                    }
                    
                    item {
                        Spacer(modifier = Modifier.height(80.dp)) // Space for FAB
                    }
                }
            }
        }
    }
    
    // Create/Edit Category Dialog
    if (uiState.showCreateCategoryDialog || uiState.showEditCategoryDialog) {
        CreateEditCategoryDialog(
            isEdit = uiState.showEditCategoryDialog,
            categoryType = uiState.selectedTab,
            formState = formState,
            availableIcons = viewModel.availableIcons,
            availableColors = viewModel.availableColors,
            onNameChange = viewModel::updateFormName,
            onIconChange = viewModel::updateFormIcon,
            onColorChange = viewModel::updateFormColor,
            onBudgetLimitChange = viewModel::updateFormBudgetLimit,
            onHasBudgetLimitChange = viewModel::updateFormHasBudgetLimit,
            onConfirm = {
                if (uiState.showEditCategoryDialog) {
                    viewModel.updateCategory()
                } else {
                    viewModel.createCategory()
                }
            },
            onDismiss = {
                if (uiState.showEditCategoryDialog) {
                    viewModel.hideEditCategoryDialog()
                } else {
                    viewModel.hideCreateCategoryDialog()
                }
            }
        )
    }
    
    // Delete Confirmation Dialog
    if (uiState.showDeleteConfirmDialog) {
        AlertDialog(
            onDismissRequest = { viewModel.hideDeleteConfirmDialog() },
            title = {
                Text(
                    text = "Xác nhận xóa danh mục",
                    fontWeight = FontWeight.Bold
                )
            },
            text = {
                Text("Bạn có chắc chắn muốn xóa danh mục \"${uiState.selectedCategory?.name}\"? Hành động này không thể hoàn tác.")
            },
            confirmButton = {
                Button(
                    onClick = { viewModel.deleteCategory() },
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.colorScheme.error
                    )
                ) {
                    Text("Xóa")
                }
            },
            dismissButton = {
                TextButton(onClick = { viewModel.hideDeleteConfirmDialog() }) {
                    Text("Hủy")
                }
            }
        )
    }
}

@Composable
private fun CategoryItem(
    category: CategoryEntity,
    onEditClick: () -> Unit,
    onDeleteClick: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Category icon with color
            Box(
                modifier = Modifier
                    .size(48.dp)
                    .clip(CircleShape)
                    .background(Color(android.graphics.Color.parseColor(category.color))),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = Icons.Default.Category, // You can map category.icon to actual icons
                    contentDescription = null,
                    tint = Color.White,
                    modifier = Modifier.size(24.dp)
                )
            }
            
            Spacer(modifier = Modifier.width(16.dp))
            
            // Category details
            Column(modifier = Modifier.weight(1f)) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = category.name,
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Medium
                    )
                    
                    if (category.isDefault) {
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = "Mặc định",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.primary,
                            modifier = Modifier
                                .background(
                                    MaterialTheme.colorScheme.primaryContainer,
                                    RoundedCornerShape(4.dp)
                                )
                                .padding(horizontal = 6.dp, vertical = 2.dp)
                        )
                    }
                }
                
                category.budgetLimit?.let { limit ->
                    Text(
                        text = "Ngân sách: ${ValidationUtils.formatCurrency(limit)}",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
            
            // Action buttons
            Row {
                IconButton(onClick = onEditClick) {
                    Icon(
                        imageVector = Icons.Default.Edit,
                        contentDescription = "Chỉnh sửa danh mục",
                        tint = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
                
                if (!category.isDefault) {
                    IconButton(onClick = onDeleteClick) {
                        Icon(
                            imageVector = Icons.Default.Delete,
                            contentDescription = "Xóa danh mục",
                            tint = MaterialTheme.colorScheme.error
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun CreateEditCategoryDialog(
    isEdit: Boolean,
    categoryType: CategoryType,
    formState: CreateCategoryFormState,
    availableIcons: List<String>,
    availableColors: List<String>,
    onNameChange: (String) -> Unit,
    onIconChange: (String) -> Unit,
    onColorChange: (String) -> Unit,
    onBudgetLimitChange: (String) -> Unit,
    onHasBudgetLimitChange: (Boolean) -> Unit,
    onConfirm: () -> Unit,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = if (isEdit) "Chỉnh sửa danh mục" else "Tạo danh mục mới",
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Bold
            )
        },
        text = {
            Column(
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // Category name
                OutlinedTextField(
                    value = formState.name,
                    onValueChange = onNameChange,
                    label = { Text("Tên danh mục") },
                    placeholder = { Text("Nhập tên danh mục") },
                    singleLine = true,
                    modifier = Modifier.fillMaxWidth()
                )

                // Color selection
                Text(
                    text = "Chọn màu:",
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Medium
                )

                LazyRow(
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    items(availableColors) { color ->
                        Box(
                            modifier = Modifier
                                .size(40.dp)
                                .clip(CircleShape)
                                .background(Color(android.graphics.Color.parseColor(color)))
                                .clickable { onColorChange(color) }
                                .then(
                                    if (color == formState.selectedColor) {
                                        Modifier.padding(2.dp)
                                    } else Modifier
                                ),
                            contentAlignment = Alignment.Center
                        ) {
                            if (color == formState.selectedColor) {
                                Icon(
                                    imageVector = Icons.Default.Check,
                                    contentDescription = "Đã chọn",
                                    tint = Color.White,
                                    modifier = Modifier.size(20.dp)
                                )
                            }
                        }
                    }
                }

                // Budget limit for expense categories
                if (categoryType == CategoryType.EXPENSE) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Checkbox(
                            checked = formState.hasBudgetLimit,
                            onCheckedChange = onHasBudgetLimitChange
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = "Đặt giới hạn ngân sách",
                            style = MaterialTheme.typography.bodyMedium
                        )
                    }

                    if (formState.hasBudgetLimit) {
                        OutlinedTextField(
                            value = formState.budgetLimit,
                            onValueChange = onBudgetLimitChange,
                            label = { Text("Giới hạn ngân sách") },
                            placeholder = { Text("Nhập số tiền") },
                            suffix = { Text("đ") },
                            singleLine = true,
                            modifier = Modifier.fillMaxWidth()
                        )
                    }
                }
            }
        },
        confirmButton = {
            Button(
                onClick = onConfirm,
                enabled = formState.name.isNotBlank()
            ) {
                Text(if (isEdit) "Cập nhật" else "Tạo")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("Hủy")
            }
        }
    )
}
