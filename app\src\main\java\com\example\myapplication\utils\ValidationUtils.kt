package com.example.myapplication.utils

object ValidationUtils {
    
    data class ValidationResult(
        val isValid: <PERSON><PERSON>an,
        val errorMessage: String? = null
    )
    
    fun validateAmount(amountText: String): ValidationResult {
        if (amountText.isBlank()) {
            return ValidationResult(false, "Vui lòng nhập số tiền")
        }
        
        val amount = amountText.toDoubleOrNull()
        return when {
            amount == null -> ValidationResult(false, "Số tiền không hợp lệ")
            amount <= 0 -> ValidationResult(false, "Số tiền phải lớn hơn 0")
            amount > 999_999_999 -> ValidationResult(false, "Số tiền quá lớn")
            else -> ValidationResult(true)
        }
    }
    
    fun validateCategory(category: String): ValidationResult {
        return when {
            category.isBlank() -> ValidationResult(false, "Vui lòng chọn danh mục")
            category.length > 50 -> ValidationResult(false, "Tên danh mục quá dài")
            else -> ValidationResult(true)
        }
    }
    
    fun validateNote(note: String): ValidationResult {
        return when {
            note.length > 200 -> ValidationResult(false, "Ghi chú quá dài (tối đa 200 ký tự)")
            else -> ValidationResult(true)
        }
    }
    
    fun validateWalletName(name: String): ValidationResult {
        return when {
            name.isBlank() -> ValidationResult(false, "Vui lòng nhập tên ví")
            name.length < 2 -> ValidationResult(false, "Tên ví phải có ít nhất 2 ký tự")
            name.length > 30 -> ValidationResult(false, "Tên ví quá dài (tối đa 30 ký tự)")
            else -> ValidationResult(true)
        }
    }
    
    fun validateExpenseAgainstBalance(amount: Double, balance: Double): ValidationResult {
        return when {
            amount > balance -> ValidationResult(false, "Số dư không đủ để thực hiện giao dịch này")
            else -> ValidationResult(true)
        }
    }
    
    fun formatCurrency(amount: Double): String {
        return try {
            val formatter = java.text.NumberFormat.getCurrencyInstance(java.util.Locale("vi", "VN"))
            formatter.format(amount).replace("₫", "đ")
        } catch (e: Exception) {
            "${String.format("%.0f", amount)} đ"
        }
    }
    
    fun formatDate(timestamp: Long): String {
        return try {
            val formatter = java.text.SimpleDateFormat("dd/MM/yyyy HH:mm", java.util.Locale("vi", "VN"))
            formatter.format(java.util.Date(timestamp))
        } catch (e: Exception) {
            "N/A"
        }
    }
    
    fun sanitizeInput(input: String): String {
        return input.trim().replace(Regex("\\s+"), " ")
    }
}
