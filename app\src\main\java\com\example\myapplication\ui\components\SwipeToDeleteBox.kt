package com.example.myapplication.ui.components

import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.Orientation
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import kotlin.math.roundToInt

@OptIn(ExperimentalMaterialApi::class)
@Composable
fun SwipeToDeleteBox(
    onDelete: () -> Unit,
    modifier: Modifier = Modifier,
    content: @Composable () -> Unit
) {
    var isRevealed by remember { mutableStateOf(false) }
    val cardElevation by animateFloatAsState(
        targetValue = if (isRevealed) 8f else 2f,
        label = "cardElevation"
    )
    val cardScale by animateFloatAsState(
        targetValue = if (isRevealed) 0.95f else 1f,
        label = "cardScale"
    )
    val backgroundColor by animateColorAsState(
        targetValue = if (isRevealed) Color.Red.copy(alpha = 0.1f) else Color.Transparent,
        label = "backgroundColor"
    )

    val density = LocalDensity.current
    val swipeableState = rememberSwipeableState(
        initialValue = 0,
        confirmStateChange = { newValue ->
            if (newValue == 1) {
                onDelete()
                false // Don't actually change state, let parent handle removal
            } else {
                true
            }
        }
    )

    val sizePx = with(density) { 80.dp.toPx() }
    val anchors = mapOf(0f to 0, -sizePx to 1)

    LaunchedEffect(swipeableState.targetValue) {
        isRevealed = swipeableState.targetValue == 1
    }

    Box(
        modifier = modifier
            .swipeable(
                state = swipeableState,
                anchors = anchors,
                thresholds = { _, _ -> FractionalThreshold(0.3f) },
                orientation = Orientation.Horizontal
            )
    ) {
        // Background (delete action)
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(
                    color = Color.Red,
                    shape = RoundedCornerShape(12.dp)
                )
                .padding(horizontal = 16.dp),
            contentAlignment = Alignment.CenterEnd
        ) {
            Icon(
                imageVector = Icons.Default.Delete,
                contentDescription = "Xóa",
                tint = Color.White,
                modifier = Modifier.size(24.dp)
            )
        }

        // Foreground content
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .scale(cardScale)
                .offset { IntOffset(swipeableState.offset.value.roundToInt(), 0) },
            elevation = CardDefaults.cardElevation(defaultElevation = cardElevation.dp),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surface
            )
        ) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(backgroundColor)
            ) {
                content()
            }
        }
    }
}
