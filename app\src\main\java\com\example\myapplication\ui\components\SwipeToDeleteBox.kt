package com.example.myapplication.ui.components

import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SwipeToDeleteBox(
    onDelete: () -> Unit,
    modifier: Modifier = Modifier,
    content: @Composable () -> Unit
) {
    var isRevealed by remember { mutableStateOf(false) }
    val cardElevation by animateFloatAsState(
        targetValue = if (isRevealed) 8f else 2f,
        label = "cardElevation"
    )
    val cardScale by animateFloatAsState(
        targetValue = if (isRevealed) 0.95f else 1f,
        label = "cardScale"
    )
    val backgroundColor by animateColorAsState(
        targetValue = if (isRevealed) Color.Red.copy(alpha = 0.1f) else Color.Transparent,
        label = "backgroundColor"
    )

    val dismissState = rememberDismissState(
        confirmValueChange = { dismissValue ->
            when (dismissValue) {
                DismissValue.DismissedToStart -> {
                    onDelete()
                    true
                }
                DismissValue.DismissedToEnd -> {
                    onDelete()
                    true
                }
                else -> false
            }
        }
    )

    LaunchedEffect(dismissState.targetValue) {
        isRevealed = dismissState.targetValue != DismissValue.Default
    }

    SwipeToDismiss(
        state = dismissState,
        modifier = modifier,
        background = {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(
                        color = Color.Red,
                        shape = RoundedCornerShape(12.dp)
                    )
                    .padding(horizontal = 16.dp),
                contentAlignment = Alignment.CenterEnd
            ) {
                Icon(
                    imageVector = Icons.Default.Delete,
                    contentDescription = "Xóa",
                    tint = Color.White,
                    modifier = Modifier.size(24.dp)
                )
            }
        },
        dismissContent = {
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .scale(cardScale),
                elevation = CardDefaults.cardElevation(defaultElevation = cardElevation.dp),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surface
                )
            ) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(backgroundColor)
                ) {
                    content()
                }
            }
        },
        directions = setOf(DismissDirection.EndToStart)
    )
}
