package com.example.myapplication.ui.screen.add_transaction;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000J\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0010\u000e\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\n\n\u0002\u0018\u0002\n\u0000\b\u0007\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0006\u0010\u0017\u001a\u00020\u0018J\u0006\u0010\u0019\u001a\u00020\u0018J\b\u0010\u001a\u001a\u00020\u0018H\u0002J\u000e\u0010\u001b\u001a\u00020\u00182\u0006\u0010\u001c\u001a\u00020\nJ\u000e\u0010\u001d\u001a\u00020\u00182\u0006\u0010\u001e\u001a\u00020\nJ\u000e\u0010\u001f\u001a\u00020\u00182\u0006\u0010 \u001a\u00020\nJ\u000e\u0010!\u001a\u00020\u00182\u0006\u0010\"\u001a\u00020#R\u0014\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\b\u001a\b\u0012\u0004\u0012\u00020\n0\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\fR\u0017\u0010\r\u001a\b\u0012\u0004\u0012\u00020\n0\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\fR\u000e\u0010\u000f\u001a\u00020\u0010X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00070\u0012\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014R\u000e\u0010\u0015\u001a\u00020\u0016X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006$"}, d2 = {"Lcom/example/myapplication/ui/screen/add_transaction/AddTransactionViewModel;", "Landroidx/lifecycle/AndroidViewModel;", "application", "Landroid/app/Application;", "(Landroid/app/Application;)V", "_uiState", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/example/myapplication/ui/screen/add_transaction/AddTransactionUiState;", "expenseCategories", "", "", "getExpenseCategories", "()Ljava/util/List;", "incomeCategories", "getIncomeCategories", "transactionRepository", "Lcom/example/myapplication/data/repository/TransactionRepository;", "uiState", "Lkotlinx/coroutines/flow/StateFlow;", "getUiState", "()Lkotlinx/coroutines/flow/StateFlow;", "walletRepository", "Lcom/example/myapplication/data/repository/WalletRepository;", "addTransaction", "", "clearMessages", "loadWalletBalance", "updateAmount", "amount", "updateCategory", "category", "updateNote", "note", "updateTransactionType", "type", "Lcom/example/myapplication/ui/screen/add_transaction/TransactionType;", "app_debug"})
public final class AddTransactionViewModel extends androidx.lifecycle.AndroidViewModel {
    @org.jetbrains.annotations.NotNull()
    private final com.example.myapplication.data.repository.WalletRepository walletRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.myapplication.data.repository.TransactionRepository transactionRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.example.myapplication.ui.screen.add_transaction.AddTransactionUiState> _uiState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.example.myapplication.ui.screen.add_transaction.AddTransactionUiState> uiState = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<java.lang.String> expenseCategories = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<java.lang.String> incomeCategories = null;
    
    public AddTransactionViewModel(@org.jetbrains.annotations.NotNull()
    android.app.Application application) {
        super(null);
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.example.myapplication.ui.screen.add_transaction.AddTransactionUiState> getUiState() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> getExpenseCategories() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> getIncomeCategories() {
        return null;
    }
    
    private final void loadWalletBalance() {
    }
    
    public final void updateAmount(@org.jetbrains.annotations.NotNull()
    java.lang.String amount) {
    }
    
    public final void updateCategory(@org.jetbrains.annotations.NotNull()
    java.lang.String category) {
    }
    
    public final void updateNote(@org.jetbrains.annotations.NotNull()
    java.lang.String note) {
    }
    
    public final void updateTransactionType(@org.jetbrains.annotations.NotNull()
    com.example.myapplication.ui.screen.add_transaction.TransactionType type) {
    }
    
    public final void addTransaction() {
    }
    
    public final void clearMessages() {
    }
}