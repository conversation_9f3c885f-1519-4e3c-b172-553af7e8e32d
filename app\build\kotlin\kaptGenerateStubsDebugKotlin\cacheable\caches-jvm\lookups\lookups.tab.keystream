  Activity android.app  Application android.app  Bundle android.app.Activity  getSharedPreferences android.app.Application  Context android.content  SharedPreferences android.content  Bundle android.content.Context  MODE_PRIVATE android.content.Context  getSharedPreferences android.content.Context  Bundle android.content.ContextWrapper  getSharedPreferences android.content.ContextWrapper  SQLiteException android.database.sqlite  Build 
android.os  Bundle 
android.os  Bundle  android.view.ContextThemeWrapper  ComponentActivity androidx.activity  enableEdgeToEdge androidx.activity  Bundle #androidx.activity.ComponentActivity  
setContent androidx.activity.compose  
Composable androidx.compose.animation.core  ExperimentalFoundationApi androidx.compose.foundation  
background androidx.compose.foundation  	clickable androidx.compose.foundation  combinedClickable androidx.compose.foundation  isSystemInDarkTheme androidx.compose.foundation  rememberScrollState androidx.compose.foundation  verticalScroll androidx.compose.foundation  
Composable "androidx.compose.foundation.layout  ExperimentalFoundationApi "androidx.compose.foundation.layout  ExperimentalMaterial3Api "androidx.compose.foundation.layout  com "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  
LazyColumn  androidx.compose.foundation.lazy  LazyRow  androidx.compose.foundation.lazy  items  androidx.compose.foundation.lazy  CircleShape !androidx.compose.foundation.shape  RoundedCornerShape !androidx.compose.foundation.shape  Icons androidx.compose.material.icons  Default %androidx.compose.material.icons.Icons  Add ,androidx.compose.material.icons.Icons.Filled  Category ,androidx.compose.material.icons.Icons.Filled  Home ,androidx.compose.material.icons.Icons.Filled  Settings ,androidx.compose.material.icons.Icons.Filled  Wallet ,androidx.compose.material.icons.Icons.Filled  Add &androidx.compose.material.icons.filled  	ArrowBack &androidx.compose.material.icons.filled  
ArrowDownward &androidx.compose.material.icons.filled  ArrowUpward &androidx.compose.material.icons.filled  Category &androidx.compose.material.icons.filled  Check &androidx.compose.material.icons.filled  
Composable &androidx.compose.material.icons.filled  Delete &androidx.compose.material.icons.filled  ExperimentalMaterial3Api &androidx.compose.material.icons.filled  Home &androidx.compose.material.icons.filled  KeyboardArrowDown &androidx.compose.material.icons.filled  Refresh &androidx.compose.material.icons.filled  Settings &androidx.compose.material.icons.filled  Wallet &androidx.compose.material.icons.filled  Card androidx.compose.material3  CardDefaults androidx.compose.material3  ColorScheme androidx.compose.material3  
Composable androidx.compose.material3  ExperimentalFoundationApi androidx.compose.material3  ExperimentalMaterial3Api androidx.compose.material3  
MaterialTheme androidx.compose.material3  Surface androidx.compose.material3  
Typography androidx.compose.material3  com androidx.compose.material3  darkColorScheme androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  lightColorScheme androidx.compose.material3  
Composable androidx.compose.runtime  ExperimentalFoundationApi androidx.compose.runtime  ExperimentalMaterial3Api androidx.compose.runtime  com androidx.compose.runtime  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  composed androidx.compose.ui  clip androidx.compose.ui.draw  Offset androidx.compose.ui.geometry  Brush androidx.compose.ui.graphics  Color androidx.compose.ui.graphics  copy "androidx.compose.ui.graphics.Color  invoke ,androidx.compose.ui.graphics.Color.Companion  ImageVector #androidx.compose.ui.graphics.vector  HapticFeedbackType "androidx.compose.ui.hapticfeedback  onGloballyPositioned androidx.compose.ui.layout  LocalContext androidx.compose.ui.platform  LocalHapticFeedback androidx.compose.ui.platform  contentDescription androidx.compose.ui.semantics  	semantics androidx.compose.ui.semantics  	TextStyle androidx.compose.ui.text  invoke ,androidx.compose.ui.text.TextStyle.Companion  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  Default (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  Bold (androidx.compose.ui.text.font.FontWeight  Medium (androidx.compose.ui.text.font.FontWeight  Normal (androidx.compose.ui.text.font.FontWeight  SemiBold (androidx.compose.ui.text.font.FontWeight  Bold 2androidx.compose.ui.text.font.FontWeight.Companion  Medium 2androidx.compose.ui.text.font.FontWeight.Companion  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  SemiBold 2androidx.compose.ui.text.font.FontWeight.Companion  	TextAlign androidx.compose.ui.text.style  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  sp androidx.compose.ui.unit  Bundle #androidx.core.app.ComponentActivity  AndroidViewModel androidx.lifecycle  viewModelScope androidx.lifecycle  AddTransactionUiState #androidx.lifecycle.AndroidViewModel  Application #androidx.lifecycle.AndroidViewModel  Boolean #androidx.lifecycle.AndroidViewModel  CategoryEntity #androidx.lifecycle.AndroidViewModel  CategoryManagementUiState #androidx.lifecycle.AndroidViewModel  CategoryRepository #androidx.lifecycle.AndroidViewModel  CategoryType #androidx.lifecycle.AndroidViewModel  Context #androidx.lifecycle.AndroidViewModel  CreateCategoryFormState #androidx.lifecycle.AndroidViewModel  CreateWalletFormState #androidx.lifecycle.AndroidViewModel  Double #androidx.lifecycle.AndroidViewModel  HomeUiState #androidx.lifecycle.AndroidViewModel  List #androidx.lifecycle.AndroidViewModel  MutableStateFlow #androidx.lifecycle.AndroidViewModel  RepositoryProvider #androidx.lifecycle.AndroidViewModel  SettingsUiState #androidx.lifecycle.AndroidViewModel  	StateFlow #androidx.lifecycle.AndroidViewModel  String #androidx.lifecycle.AndroidViewModel  TransactionEntity #androidx.lifecycle.AndroidViewModel  TransactionRepository #androidx.lifecycle.AndroidViewModel  TransactionType #androidx.lifecycle.AndroidViewModel  WalletEntity #androidx.lifecycle.AndroidViewModel  WalletManagementUiState #androidx.lifecycle.AndroidViewModel  WalletRepository #androidx.lifecycle.AndroidViewModel  
WalletUiState #androidx.lifecycle.AndroidViewModel  asStateFlow #androidx.lifecycle.AndroidViewModel  listOf #androidx.lifecycle.AndroidViewModel  AddTransactionUiState androidx.lifecycle.ViewModel  Application androidx.lifecycle.ViewModel  Boolean androidx.lifecycle.ViewModel  CategoryEntity androidx.lifecycle.ViewModel  CategoryManagementUiState androidx.lifecycle.ViewModel  CategoryRepository androidx.lifecycle.ViewModel  CategoryType androidx.lifecycle.ViewModel  Context androidx.lifecycle.ViewModel  CreateCategoryFormState androidx.lifecycle.ViewModel  CreateWalletFormState androidx.lifecycle.ViewModel  Double androidx.lifecycle.ViewModel  HomeUiState androidx.lifecycle.ViewModel  List androidx.lifecycle.ViewModel  MutableStateFlow androidx.lifecycle.ViewModel  RepositoryProvider androidx.lifecycle.ViewModel  SettingsUiState androidx.lifecycle.ViewModel  	StateFlow androidx.lifecycle.ViewModel  String androidx.lifecycle.ViewModel  TransactionEntity androidx.lifecycle.ViewModel  TransactionRepository androidx.lifecycle.ViewModel  TransactionType androidx.lifecycle.ViewModel  WalletEntity androidx.lifecycle.ViewModel  WalletManagementUiState androidx.lifecycle.ViewModel  WalletRepository androidx.lifecycle.ViewModel  
WalletUiState androidx.lifecycle.ViewModel  asStateFlow androidx.lifecycle.ViewModel  listOf androidx.lifecycle.ViewModel  	viewModel $androidx.lifecycle.viewmodel.compose  NavDestination androidx.navigation  NavHostController androidx.navigation  	Companion "androidx.navigation.NavDestination  	hierarchy ,androidx.navigation.NavDestination.Companion  NavHost androidx.navigation.compose  
composable androidx.navigation.compose  currentBackStackEntryAsState androidx.navigation.compose  rememberNavController androidx.navigation.compose  Dao 
androidx.room  Database 
androidx.room  Delete 
androidx.room  Entity 
androidx.room  
ForeignKey 
androidx.room  Insert 
androidx.room  
PrimaryKey 
androidx.room  Query 
androidx.room  Room 
androidx.room  RoomDatabase 
androidx.room  Update 
androidx.room  CASCADE androidx.room.ForeignKey  SET_NULL androidx.room.ForeignKey  CASCADE "androidx.room.ForeignKey.Companion  SET_NULL "androidx.room.ForeignKey.Companion  invoke "androidx.room.ForeignKey.Companion  CategoryDao androidx.room.RoomDatabase  TransactionDao androidx.room.RoomDatabase  	WalletDao androidx.room.RoomDatabase  	Migration androidx.room.migration  SupportSQLiteDatabase !androidx.room.migration.Migration  System !androidx.room.migration.Migration  insertDefaultCategories !androidx.room.migration.Migration  
trimIndent !androidx.room.migration.Migration  SupportSQLiteDatabase androidx.sqlite.db  execSQL (androidx.sqlite.db.SupportSQLiteDatabase  MainActivity com.example.myapplication  Bundle &com.example.myapplication.MainActivity  AppDatabase $com.example.myapplication.data.local  CategoryEntity $com.example.myapplication.data.local  DatabaseProvider $com.example.myapplication.data.local  System $com.example.myapplication.data.local  TransactionEntity $com.example.myapplication.data.local  Volatile $com.example.myapplication.data.local  WalletEntity $com.example.myapplication.data.local  insertDefaultCategories $com.example.myapplication.data.local  
trimIndent $com.example.myapplication.data.local  CategoryDao 0com.example.myapplication.data.local.AppDatabase  TransactionDao 0com.example.myapplication.data.local.AppDatabase  	WalletDao 0com.example.myapplication.data.local.AppDatabase  AppDatabase 5com.example.myapplication.data.local.DatabaseProvider  Context 5com.example.myapplication.data.local.DatabaseProvider  	Migration 5com.example.myapplication.data.local.DatabaseProvider  SupportSQLiteDatabase 5com.example.myapplication.data.local.DatabaseProvider  System 5com.example.myapplication.data.local.DatabaseProvider  Volatile 5com.example.myapplication.data.local.DatabaseProvider  
getTRIMIndent 5com.example.myapplication.data.local.DatabaseProvider  
getTrimIndent 5com.example.myapplication.data.local.DatabaseProvider  insertDefaultCategories 5com.example.myapplication.data.local.DatabaseProvider  
trimIndent 5com.example.myapplication.data.local.DatabaseProvider  getINSERTDefaultCategories Vcom.example.myapplication.data.local.DatabaseProvider.MIGRATION_1_2.<no name provided>  getInsertDefaultCategories Vcom.example.myapplication.data.local.DatabaseProvider.MIGRATION_1_2.<no name provided>  
getTRIMIndent Vcom.example.myapplication.data.local.DatabaseProvider.MIGRATION_1_2.<no name provided>  
getTrimIndent Vcom.example.myapplication.data.local.DatabaseProvider.MIGRATION_1_2.<no name provided>  Boolean (com.example.myapplication.data.local.dao  CategoryDao (com.example.myapplication.data.local.dao  Dao (com.example.myapplication.data.local.dao  Delete (com.example.myapplication.data.local.dao  Double (com.example.myapplication.data.local.dao  Insert (com.example.myapplication.data.local.dao  Int (com.example.myapplication.data.local.dao  List (com.example.myapplication.data.local.dao  Long (com.example.myapplication.data.local.dao  Query (com.example.myapplication.data.local.dao  String (com.example.myapplication.data.local.dao  TransactionDao (com.example.myapplication.data.local.dao  Update (com.example.myapplication.data.local.dao  	WalletDao (com.example.myapplication.data.local.dao  CategoryEntity 4com.example.myapplication.data.local.dao.CategoryDao  Delete 4com.example.myapplication.data.local.dao.CategoryDao  Flow 4com.example.myapplication.data.local.dao.CategoryDao  Insert 4com.example.myapplication.data.local.dao.CategoryDao  Int 4com.example.myapplication.data.local.dao.CategoryDao  List 4com.example.myapplication.data.local.dao.CategoryDao  Long 4com.example.myapplication.data.local.dao.CategoryDao  Query 4com.example.myapplication.data.local.dao.CategoryDao  String 4com.example.myapplication.data.local.dao.CategoryDao  Update 4com.example.myapplication.data.local.dao.CategoryDao  activateCategory 4com.example.myapplication.data.local.dao.CategoryDao  deactivateCategory 4com.example.myapplication.data.local.dao.CategoryDao  deleteCategory 4com.example.myapplication.data.local.dao.CategoryDao  insertCategories 4com.example.myapplication.data.local.dao.CategoryDao  updateCategory 4com.example.myapplication.data.local.dao.CategoryDao  Delete 7com.example.myapplication.data.local.dao.TransactionDao  Double 7com.example.myapplication.data.local.dao.TransactionDao  Flow 7com.example.myapplication.data.local.dao.TransactionDao  Insert 7com.example.myapplication.data.local.dao.TransactionDao  Int 7com.example.myapplication.data.local.dao.TransactionDao  List 7com.example.myapplication.data.local.dao.TransactionDao  Long 7com.example.myapplication.data.local.dao.TransactionDao  Query 7com.example.myapplication.data.local.dao.TransactionDao  String 7com.example.myapplication.data.local.dao.TransactionDao  TransactionEntity 7com.example.myapplication.data.local.dao.TransactionDao  Update 7com.example.myapplication.data.local.dao.TransactionDao  deleteTransaction 7com.example.myapplication.data.local.dao.TransactionDao  updateTransaction 7com.example.myapplication.data.local.dao.TransactionDao  Boolean 2com.example.myapplication.data.local.dao.WalletDao  Delete 2com.example.myapplication.data.local.dao.WalletDao  Double 2com.example.myapplication.data.local.dao.WalletDao  Flow 2com.example.myapplication.data.local.dao.WalletDao  Insert 2com.example.myapplication.data.local.dao.WalletDao  Int 2com.example.myapplication.data.local.dao.WalletDao  List 2com.example.myapplication.data.local.dao.WalletDao  Long 2com.example.myapplication.data.local.dao.WalletDao  Query 2com.example.myapplication.data.local.dao.WalletDao  String 2com.example.myapplication.data.local.dao.WalletDao  Update 2com.example.myapplication.data.local.dao.WalletDao  WalletEntity 2com.example.myapplication.data.local.dao.WalletDao  deleteWallet 2com.example.myapplication.data.local.dao.WalletDao  
updateBalance 2com.example.myapplication.data.local.dao.WalletDao  updateWallet 2com.example.myapplication.data.local.dao.WalletDao  Boolean +com.example.myapplication.data.local.entity  CategoryEntity +com.example.myapplication.data.local.entity  Double +com.example.myapplication.data.local.entity  Int +com.example.myapplication.data.local.entity  Long +com.example.myapplication.data.local.entity  String +com.example.myapplication.data.local.entity  TransactionEntity +com.example.myapplication.data.local.entity  WalletEntity +com.example.myapplication.data.local.entity  Boolean :com.example.myapplication.data.local.entity.CategoryEntity  Double :com.example.myapplication.data.local.entity.CategoryEntity  Int :com.example.myapplication.data.local.entity.CategoryEntity  Long :com.example.myapplication.data.local.entity.CategoryEntity  
PrimaryKey :com.example.myapplication.data.local.entity.CategoryEntity  String :com.example.myapplication.data.local.entity.CategoryEntity  Double =com.example.myapplication.data.local.entity.TransactionEntity  Int =com.example.myapplication.data.local.entity.TransactionEntity  Long =com.example.myapplication.data.local.entity.TransactionEntity  
PrimaryKey =com.example.myapplication.data.local.entity.TransactionEntity  String =com.example.myapplication.data.local.entity.TransactionEntity  Boolean 8com.example.myapplication.data.local.entity.WalletEntity  Double 8com.example.myapplication.data.local.entity.WalletEntity  Int 8com.example.myapplication.data.local.entity.WalletEntity  Long 8com.example.myapplication.data.local.entity.WalletEntity  
PrimaryKey 8com.example.myapplication.data.local.entity.WalletEntity  String 8com.example.myapplication.data.local.entity.WalletEntity  Boolean )com.example.myapplication.data.repository  CategoryRepository )com.example.myapplication.data.repository  Double )com.example.myapplication.data.repository  Int )com.example.myapplication.data.repository  List )com.example.myapplication.data.repository  Long )com.example.myapplication.data.repository  RepositoryProvider )com.example.myapplication.data.repository  String )com.example.myapplication.data.repository  System )com.example.myapplication.data.repository  TransactionRepository )com.example.myapplication.data.repository  WalletRepository )com.example.myapplication.data.repository  CategoryDao <com.example.myapplication.data.repository.CategoryRepository  CategoryEntity <com.example.myapplication.data.repository.CategoryRepository  Double <com.example.myapplication.data.repository.CategoryRepository  Flow <com.example.myapplication.data.repository.CategoryRepository  Int <com.example.myapplication.data.repository.CategoryRepository  List <com.example.myapplication.data.repository.CategoryRepository  Long <com.example.myapplication.data.repository.CategoryRepository  String <com.example.myapplication.data.repository.CategoryRepository  categoryDao <com.example.myapplication.data.repository.CategoryRepository  CategoryRepository <com.example.myapplication.data.repository.RepositoryProvider  Context <com.example.myapplication.data.repository.RepositoryProvider  TransactionRepository <com.example.myapplication.data.repository.RepositoryProvider  WalletRepository <com.example.myapplication.data.repository.RepositoryProvider  provideCategoryRepository <com.example.myapplication.data.repository.RepositoryProvider  provideTransactionRepository <com.example.myapplication.data.repository.RepositoryProvider  provideWalletRepository <com.example.myapplication.data.repository.RepositoryProvider  Double ?com.example.myapplication.data.repository.TransactionRepository  Flow ?com.example.myapplication.data.repository.TransactionRepository  Int ?com.example.myapplication.data.repository.TransactionRepository  List ?com.example.myapplication.data.repository.TransactionRepository  Long ?com.example.myapplication.data.repository.TransactionRepository  String ?com.example.myapplication.data.repository.TransactionRepository  TransactionDao ?com.example.myapplication.data.repository.TransactionRepository  TransactionEntity ?com.example.myapplication.data.repository.TransactionRepository  transactionDao ?com.example.myapplication.data.repository.TransactionRepository  Boolean :com.example.myapplication.data.repository.WalletRepository  Double :com.example.myapplication.data.repository.WalletRepository  Flow :com.example.myapplication.data.repository.WalletRepository  Int :com.example.myapplication.data.repository.WalletRepository  List :com.example.myapplication.data.repository.WalletRepository  Long :com.example.myapplication.data.repository.WalletRepository  String :com.example.myapplication.data.repository.WalletRepository  System :com.example.myapplication.data.repository.WalletRepository  	WalletDao :com.example.myapplication.data.repository.WalletRepository  WalletEntity :com.example.myapplication.data.repository.WalletRepository  	walletDao :com.example.myapplication.data.repository.WalletRepository  Transaction &com.example.myapplication.domain.model  Wallet &com.example.myapplication.domain.model  AddTransactionUseCase (com.example.myapplication.domain.usecase  GetTransactionsUseCase (com.example.myapplication.domain.usecase  UpdateWalletBalanceUseCase (com.example.myapplication.domain.usecase  
Composable 'com.example.myapplication.ui.components  ExperimentalFoundationApi 'com.example.myapplication.ui.components  OptIn 'com.example.myapplication.ui.components  ShimmerTransactionItem 'com.example.myapplication.ui.components  ShimmerWalletCard 'com.example.myapplication.ui.components  SwipeToDeleteBox 'com.example.myapplication.ui.components  Unit 'com.example.myapplication.ui.components  
shimmerEffect 'com.example.myapplication.ui.components  
AppNavHost 'com.example.myapplication.ui.navigation  
Composable 'com.example.myapplication.ui.navigation  Screen 'com.example.myapplication.ui.navigation  String 'com.example.myapplication.ui.navigation  Add .com.example.myapplication.ui.navigation.Screen  Category .com.example.myapplication.ui.navigation.Screen  Home .com.example.myapplication.ui.navigation.Screen  Icons .com.example.myapplication.ui.navigation.Screen  ImageVector .com.example.myapplication.ui.navigation.Screen  Screen .com.example.myapplication.ui.navigation.Screen  Settings .com.example.myapplication.ui.navigation.Screen  String .com.example.myapplication.ui.navigation.Screen  Wallet .com.example.myapplication.ui.navigation.Screen  Add =com.example.myapplication.ui.navigation.Screen.AddTransaction  Icons =com.example.myapplication.ui.navigation.Screen.AddTransaction  Category Acom.example.myapplication.ui.navigation.Screen.CategoryManagement  Icons Acom.example.myapplication.ui.navigation.Screen.CategoryManagement  Home 3com.example.myapplication.ui.navigation.Screen.Home  Icons 3com.example.myapplication.ui.navigation.Screen.Home  Icons 7com.example.myapplication.ui.navigation.Screen.Settings  Settings 7com.example.myapplication.ui.navigation.Screen.Settings  Icons 5com.example.myapplication.ui.navigation.Screen.Wallet  Wallet 5com.example.myapplication.ui.navigation.Screen.Wallet  Icons ?com.example.myapplication.ui.navigation.Screen.WalletManagement  Settings ?com.example.myapplication.ui.navigation.Screen.WalletManagement  AddTransactionScreen 3com.example.myapplication.ui.screen.add_transaction  AddTransactionUiState 3com.example.myapplication.ui.screen.add_transaction  AddTransactionViewModel 3com.example.myapplication.ui.screen.add_transaction  Boolean 3com.example.myapplication.ui.screen.add_transaction  CategorySelectionCard 3com.example.myapplication.ui.screen.add_transaction  CategorySelectorDialog 3com.example.myapplication.ui.screen.add_transaction  
Composable 3com.example.myapplication.ui.screen.add_transaction  Double 3com.example.myapplication.ui.screen.add_transaction  ExperimentalMaterial3Api 3com.example.myapplication.ui.screen.add_transaction  List 3com.example.myapplication.ui.screen.add_transaction  MutableStateFlow 3com.example.myapplication.ui.screen.add_transaction  OptIn 3com.example.myapplication.ui.screen.add_transaction  RepositoryProvider 3com.example.myapplication.ui.screen.add_transaction  String 3com.example.myapplication.ui.screen.add_transaction  TransactionType 3com.example.myapplication.ui.screen.add_transaction  TransactionTypeSelector 3com.example.myapplication.ui.screen.add_transaction  Unit 3com.example.myapplication.ui.screen.add_transaction  WalletSelectionCard 3com.example.myapplication.ui.screen.add_transaction  WalletSelectorDialog 3com.example.myapplication.ui.screen.add_transaction  asStateFlow 3com.example.myapplication.ui.screen.add_transaction  com 3com.example.myapplication.ui.screen.add_transaction  formatCurrency 3com.example.myapplication.ui.screen.add_transaction  Boolean Icom.example.myapplication.ui.screen.add_transaction.AddTransactionUiState  CategoryEntity Icom.example.myapplication.ui.screen.add_transaction.AddTransactionUiState  List Icom.example.myapplication.ui.screen.add_transaction.AddTransactionUiState  String Icom.example.myapplication.ui.screen.add_transaction.AddTransactionUiState  TransactionType Icom.example.myapplication.ui.screen.add_transaction.AddTransactionUiState  WalletEntity Icom.example.myapplication.ui.screen.add_transaction.AddTransactionUiState  AddTransactionUiState Kcom.example.myapplication.ui.screen.add_transaction.AddTransactionViewModel  Application Kcom.example.myapplication.ui.screen.add_transaction.AddTransactionViewModel  CategoryEntity Kcom.example.myapplication.ui.screen.add_transaction.AddTransactionViewModel  CategoryRepository Kcom.example.myapplication.ui.screen.add_transaction.AddTransactionViewModel  MutableStateFlow Kcom.example.myapplication.ui.screen.add_transaction.AddTransactionViewModel  RepositoryProvider Kcom.example.myapplication.ui.screen.add_transaction.AddTransactionViewModel  	StateFlow Kcom.example.myapplication.ui.screen.add_transaction.AddTransactionViewModel  String Kcom.example.myapplication.ui.screen.add_transaction.AddTransactionViewModel  TransactionRepository Kcom.example.myapplication.ui.screen.add_transaction.AddTransactionViewModel  TransactionType Kcom.example.myapplication.ui.screen.add_transaction.AddTransactionViewModel  WalletEntity Kcom.example.myapplication.ui.screen.add_transaction.AddTransactionViewModel  WalletRepository Kcom.example.myapplication.ui.screen.add_transaction.AddTransactionViewModel  _uiState Kcom.example.myapplication.ui.screen.add_transaction.AddTransactionViewModel  asStateFlow Kcom.example.myapplication.ui.screen.add_transaction.AddTransactionViewModel  getASStateFlow Kcom.example.myapplication.ui.screen.add_transaction.AddTransactionViewModel  getAsStateFlow Kcom.example.myapplication.ui.screen.add_transaction.AddTransactionViewModel  String Ccom.example.myapplication.ui.screen.add_transaction.TransactionType  TransactionType Ccom.example.myapplication.ui.screen.add_transaction.TransactionType  Boolean 7com.example.myapplication.ui.screen.category_management  CategoryItem 7com.example.myapplication.ui.screen.category_management  CategoryManagementScreen 7com.example.myapplication.ui.screen.category_management  CategoryManagementUiState 7com.example.myapplication.ui.screen.category_management  CategoryManagementViewModel 7com.example.myapplication.ui.screen.category_management  CategoryType 7com.example.myapplication.ui.screen.category_management  
Composable 7com.example.myapplication.ui.screen.category_management  CreateCategoryFormState 7com.example.myapplication.ui.screen.category_management  CreateEditCategoryDialog 7com.example.myapplication.ui.screen.category_management  ExperimentalMaterial3Api 7com.example.myapplication.ui.screen.category_management  List 7com.example.myapplication.ui.screen.category_management  MutableStateFlow 7com.example.myapplication.ui.screen.category_management  OptIn 7com.example.myapplication.ui.screen.category_management  RepositoryProvider 7com.example.myapplication.ui.screen.category_management  String 7com.example.myapplication.ui.screen.category_management  Unit 7com.example.myapplication.ui.screen.category_management  asStateFlow 7com.example.myapplication.ui.screen.category_management  listOf 7com.example.myapplication.ui.screen.category_management  Boolean Qcom.example.myapplication.ui.screen.category_management.CategoryManagementUiState  CategoryEntity Qcom.example.myapplication.ui.screen.category_management.CategoryManagementUiState  CategoryType Qcom.example.myapplication.ui.screen.category_management.CategoryManagementUiState  List Qcom.example.myapplication.ui.screen.category_management.CategoryManagementUiState  String Qcom.example.myapplication.ui.screen.category_management.CategoryManagementUiState  Application Scom.example.myapplication.ui.screen.category_management.CategoryManagementViewModel  Boolean Scom.example.myapplication.ui.screen.category_management.CategoryManagementViewModel  CategoryEntity Scom.example.myapplication.ui.screen.category_management.CategoryManagementViewModel  CategoryManagementUiState Scom.example.myapplication.ui.screen.category_management.CategoryManagementViewModel  CategoryRepository Scom.example.myapplication.ui.screen.category_management.CategoryManagementViewModel  CategoryType Scom.example.myapplication.ui.screen.category_management.CategoryManagementViewModel  CreateCategoryFormState Scom.example.myapplication.ui.screen.category_management.CategoryManagementViewModel  MutableStateFlow Scom.example.myapplication.ui.screen.category_management.CategoryManagementViewModel  RepositoryProvider Scom.example.myapplication.ui.screen.category_management.CategoryManagementViewModel  	StateFlow Scom.example.myapplication.ui.screen.category_management.CategoryManagementViewModel  String Scom.example.myapplication.ui.screen.category_management.CategoryManagementViewModel  _createFormState Scom.example.myapplication.ui.screen.category_management.CategoryManagementViewModel  _uiState Scom.example.myapplication.ui.screen.category_management.CategoryManagementViewModel  asStateFlow Scom.example.myapplication.ui.screen.category_management.CategoryManagementViewModel  getASStateFlow Scom.example.myapplication.ui.screen.category_management.CategoryManagementViewModel  getAsStateFlow Scom.example.myapplication.ui.screen.category_management.CategoryManagementViewModel  	getLISTOf Scom.example.myapplication.ui.screen.category_management.CategoryManagementViewModel  	getListOf Scom.example.myapplication.ui.screen.category_management.CategoryManagementViewModel  listOf Scom.example.myapplication.ui.screen.category_management.CategoryManagementViewModel  CategoryType Dcom.example.myapplication.ui.screen.category_management.CategoryType  String Dcom.example.myapplication.ui.screen.category_management.CategoryType  Boolean Ocom.example.myapplication.ui.screen.category_management.CreateCategoryFormState  String Ocom.example.myapplication.ui.screen.category_management.CreateCategoryFormState  Boolean (com.example.myapplication.ui.screen.home  
Composable (com.example.myapplication.ui.screen.home  Double (com.example.myapplication.ui.screen.home  EmptyTransactionsCard (com.example.myapplication.ui.screen.home  ExperimentalMaterial3Api (com.example.myapplication.ui.screen.home  
HomeScreen (com.example.myapplication.ui.screen.home  HomeUiState (com.example.myapplication.ui.screen.home  
HomeViewModel (com.example.myapplication.ui.screen.home  List (com.example.myapplication.ui.screen.home  Long (com.example.myapplication.ui.screen.home  MutableStateFlow (com.example.myapplication.ui.screen.home  OptIn (com.example.myapplication.ui.screen.home  RepositoryProvider (com.example.myapplication.ui.screen.home  String (com.example.myapplication.ui.screen.home  TransactionItem (com.example.myapplication.ui.screen.home  Unit (com.example.myapplication.ui.screen.home  WalletBalanceCard (com.example.myapplication.ui.screen.home  asStateFlow (com.example.myapplication.ui.screen.home  formatCurrency (com.example.myapplication.ui.screen.home  
formatDate (com.example.myapplication.ui.screen.home  Boolean 4com.example.myapplication.ui.screen.home.HomeUiState  List 4com.example.myapplication.ui.screen.home.HomeUiState  String 4com.example.myapplication.ui.screen.home.HomeUiState  TransactionEntity 4com.example.myapplication.ui.screen.home.HomeUiState  WalletEntity 4com.example.myapplication.ui.screen.home.HomeUiState  transactions 4com.example.myapplication.ui.screen.home.HomeUiState  Application 6com.example.myapplication.ui.screen.home.HomeViewModel  HomeUiState 6com.example.myapplication.ui.screen.home.HomeViewModel  List 6com.example.myapplication.ui.screen.home.HomeViewModel  MutableStateFlow 6com.example.myapplication.ui.screen.home.HomeViewModel  RepositoryProvider 6com.example.myapplication.ui.screen.home.HomeViewModel  	StateFlow 6com.example.myapplication.ui.screen.home.HomeViewModel  TransactionEntity 6com.example.myapplication.ui.screen.home.HomeViewModel  TransactionRepository 6com.example.myapplication.ui.screen.home.HomeViewModel  WalletRepository 6com.example.myapplication.ui.screen.home.HomeViewModel  _uiState 6com.example.myapplication.ui.screen.home.HomeViewModel  asStateFlow 6com.example.myapplication.ui.screen.home.HomeViewModel  getASStateFlow 6com.example.myapplication.ui.screen.home.HomeViewModel  getAsStateFlow 6com.example.myapplication.ui.screen.home.HomeViewModel  Boolean ,com.example.myapplication.ui.screen.settings  
Composable ,com.example.myapplication.ui.screen.settings  Context ,com.example.myapplication.ui.screen.settings  Double ,com.example.myapplication.ui.screen.settings  ExperimentalMaterial3Api ,com.example.myapplication.ui.screen.settings  Int ,com.example.myapplication.ui.screen.settings  MutableStateFlow ,com.example.myapplication.ui.screen.settings  OptIn ,com.example.myapplication.ui.screen.settings  RepositoryProvider ,com.example.myapplication.ui.screen.settings  SettingsItem ,com.example.myapplication.ui.screen.settings  SettingsScreen ,com.example.myapplication.ui.screen.settings  SettingsSection ,com.example.myapplication.ui.screen.settings  SettingsUiState ,com.example.myapplication.ui.screen.settings  SettingsViewModel ,com.example.myapplication.ui.screen.settings  
StatisticItem ,com.example.myapplication.ui.screen.settings  StatisticsSection ,com.example.myapplication.ui.screen.settings  String ,com.example.myapplication.ui.screen.settings  Unit ,com.example.myapplication.ui.screen.settings  asStateFlow ,com.example.myapplication.ui.screen.settings  Boolean <com.example.myapplication.ui.screen.settings.SettingsUiState  Double <com.example.myapplication.ui.screen.settings.SettingsUiState  Int <com.example.myapplication.ui.screen.settings.SettingsUiState  String <com.example.myapplication.ui.screen.settings.SettingsUiState  Application >com.example.myapplication.ui.screen.settings.SettingsViewModel  Boolean >com.example.myapplication.ui.screen.settings.SettingsViewModel  Context >com.example.myapplication.ui.screen.settings.SettingsViewModel  MutableStateFlow >com.example.myapplication.ui.screen.settings.SettingsViewModel  RepositoryProvider >com.example.myapplication.ui.screen.settings.SettingsViewModel  SettingsUiState >com.example.myapplication.ui.screen.settings.SettingsViewModel  	StateFlow >com.example.myapplication.ui.screen.settings.SettingsViewModel  String >com.example.myapplication.ui.screen.settings.SettingsViewModel  TransactionRepository >com.example.myapplication.ui.screen.settings.SettingsViewModel  WalletRepository >com.example.myapplication.ui.screen.settings.SettingsViewModel  _uiState >com.example.myapplication.ui.screen.settings.SettingsViewModel  asStateFlow >com.example.myapplication.ui.screen.settings.SettingsViewModel  getASStateFlow >com.example.myapplication.ui.screen.settings.SettingsViewModel  getAsStateFlow >com.example.myapplication.ui.screen.settings.SettingsViewModel  Boolean *com.example.myapplication.ui.screen.wallet  
Composable *com.example.myapplication.ui.screen.wallet  Double *com.example.myapplication.ui.screen.wallet  ExperimentalMaterial3Api *com.example.myapplication.ui.screen.wallet  MoneyDialog *com.example.myapplication.ui.screen.wallet  MutableStateFlow *com.example.myapplication.ui.screen.wallet  OptIn *com.example.myapplication.ui.screen.wallet  RepositoryProvider *com.example.myapplication.ui.screen.wallet  
StatisticCard *com.example.myapplication.ui.screen.wallet  String *com.example.myapplication.ui.screen.wallet  Unit *com.example.myapplication.ui.screen.wallet  WalletBalanceCard *com.example.myapplication.ui.screen.wallet  WalletScreen *com.example.myapplication.ui.screen.wallet  
WalletUiState *com.example.myapplication.ui.screen.wallet  WalletViewModel *com.example.myapplication.ui.screen.wallet  asStateFlow *com.example.myapplication.ui.screen.wallet  formatCurrency *com.example.myapplication.ui.screen.wallet  Boolean 8com.example.myapplication.ui.screen.wallet.WalletUiState  Double 8com.example.myapplication.ui.screen.wallet.WalletUiState  String 8com.example.myapplication.ui.screen.wallet.WalletUiState  WalletEntity 8com.example.myapplication.ui.screen.wallet.WalletUiState  Application :com.example.myapplication.ui.screen.wallet.WalletViewModel  Double :com.example.myapplication.ui.screen.wallet.WalletViewModel  MutableStateFlow :com.example.myapplication.ui.screen.wallet.WalletViewModel  RepositoryProvider :com.example.myapplication.ui.screen.wallet.WalletViewModel  	StateFlow :com.example.myapplication.ui.screen.wallet.WalletViewModel  String :com.example.myapplication.ui.screen.wallet.WalletViewModel  TransactionRepository :com.example.myapplication.ui.screen.wallet.WalletViewModel  WalletRepository :com.example.myapplication.ui.screen.wallet.WalletViewModel  
WalletUiState :com.example.myapplication.ui.screen.wallet.WalletViewModel  _uiState :com.example.myapplication.ui.screen.wallet.WalletViewModel  asStateFlow :com.example.myapplication.ui.screen.wallet.WalletViewModel  getASStateFlow :com.example.myapplication.ui.screen.wallet.WalletViewModel  getAsStateFlow :com.example.myapplication.ui.screen.wallet.WalletViewModel  Boolean 5com.example.myapplication.ui.screen.wallet_management  
Composable 5com.example.myapplication.ui.screen.wallet_management  CreateEditWalletDialog 5com.example.myapplication.ui.screen.wallet_management  CreateWalletFormState 5com.example.myapplication.ui.screen.wallet_management  Double 5com.example.myapplication.ui.screen.wallet_management  ExperimentalMaterial3Api 5com.example.myapplication.ui.screen.wallet_management  Int 5com.example.myapplication.ui.screen.wallet_management  List 5com.example.myapplication.ui.screen.wallet_management  MutableStateFlow 5com.example.myapplication.ui.screen.wallet_management  OptIn 5com.example.myapplication.ui.screen.wallet_management  RepositoryProvider 5com.example.myapplication.ui.screen.wallet_management  StatisticsSection 5com.example.myapplication.ui.screen.wallet_management  String 5com.example.myapplication.ui.screen.wallet_management  Unit 5com.example.myapplication.ui.screen.wallet_management  
WalletItem 5com.example.myapplication.ui.screen.wallet_management  WalletManagementScreen 5com.example.myapplication.ui.screen.wallet_management  WalletManagementUiState 5com.example.myapplication.ui.screen.wallet_management  WalletManagementViewModel 5com.example.myapplication.ui.screen.wallet_management  asStateFlow 5com.example.myapplication.ui.screen.wallet_management  listOf 5com.example.myapplication.ui.screen.wallet_management  String Kcom.example.myapplication.ui.screen.wallet_management.CreateWalletFormState  Boolean Mcom.example.myapplication.ui.screen.wallet_management.WalletManagementUiState  Double Mcom.example.myapplication.ui.screen.wallet_management.WalletManagementUiState  Int Mcom.example.myapplication.ui.screen.wallet_management.WalletManagementUiState  List Mcom.example.myapplication.ui.screen.wallet_management.WalletManagementUiState  String Mcom.example.myapplication.ui.screen.wallet_management.WalletManagementUiState  WalletEntity Mcom.example.myapplication.ui.screen.wallet_management.WalletManagementUiState  Application Ocom.example.myapplication.ui.screen.wallet_management.WalletManagementViewModel  CreateWalletFormState Ocom.example.myapplication.ui.screen.wallet_management.WalletManagementViewModel  MutableStateFlow Ocom.example.myapplication.ui.screen.wallet_management.WalletManagementViewModel  RepositoryProvider Ocom.example.myapplication.ui.screen.wallet_management.WalletManagementViewModel  	StateFlow Ocom.example.myapplication.ui.screen.wallet_management.WalletManagementViewModel  String Ocom.example.myapplication.ui.screen.wallet_management.WalletManagementViewModel  WalletEntity Ocom.example.myapplication.ui.screen.wallet_management.WalletManagementViewModel  WalletManagementUiState Ocom.example.myapplication.ui.screen.wallet_management.WalletManagementViewModel  WalletRepository Ocom.example.myapplication.ui.screen.wallet_management.WalletManagementViewModel  _createFormState Ocom.example.myapplication.ui.screen.wallet_management.WalletManagementViewModel  _uiState Ocom.example.myapplication.ui.screen.wallet_management.WalletManagementViewModel  asStateFlow Ocom.example.myapplication.ui.screen.wallet_management.WalletManagementViewModel  getASStateFlow Ocom.example.myapplication.ui.screen.wallet_management.WalletManagementViewModel  getAsStateFlow Ocom.example.myapplication.ui.screen.wallet_management.WalletManagementViewModel  	getLISTOf Ocom.example.myapplication.ui.screen.wallet_management.WalletManagementViewModel  	getListOf Ocom.example.myapplication.ui.screen.wallet_management.WalletManagementViewModel  listOf Ocom.example.myapplication.ui.screen.wallet_management.WalletManagementViewModel  
Background "com.example.myapplication.ui.theme  Boolean "com.example.myapplication.ui.theme  DarkColorScheme "com.example.myapplication.ui.theme  Error "com.example.myapplication.ui.theme  Info "com.example.myapplication.ui.theme  LightColorScheme "com.example.myapplication.ui.theme  MyApplicationTheme "com.example.myapplication.ui.theme  OnBackground "com.example.myapplication.ui.theme  	OnPrimary "com.example.myapplication.ui.theme  OnSecondary "com.example.myapplication.ui.theme  	OnSurface "com.example.myapplication.ui.theme  OnSurfaceVariant "com.example.myapplication.ui.theme  Pink40 "com.example.myapplication.ui.theme  Pink80 "com.example.myapplication.ui.theme  Primary "com.example.myapplication.ui.theme  PrimaryVariant "com.example.myapplication.ui.theme  Purple40 "com.example.myapplication.ui.theme  Purple80 "com.example.myapplication.ui.theme  PurpleGrey40 "com.example.myapplication.ui.theme  PurpleGrey80 "com.example.myapplication.ui.theme  	Secondary "com.example.myapplication.ui.theme  SecondaryVariant "com.example.myapplication.ui.theme  Success "com.example.myapplication.ui.theme  Surface "com.example.myapplication.ui.theme  SurfaceVariant "com.example.myapplication.ui.theme  
Typography "com.example.myapplication.ui.theme  Unit "com.example.myapplication.ui.theme  Warning "com.example.myapplication.ui.theme  Boolean com.example.myapplication.utils  Double com.example.myapplication.utils  ErrorHandler com.example.myapplication.utils  Long com.example.myapplication.utils  String com.example.myapplication.utils  	Throwable com.example.myapplication.utils  ValidationUtils com.example.myapplication.utils  AppError ,com.example.myapplication.utils.ErrorHandler  String ,com.example.myapplication.utils.ErrorHandler  	Throwable ,com.example.myapplication.utils.ErrorHandler  AppError 5com.example.myapplication.utils.ErrorHandler.AppError  String 5com.example.myapplication.utils.ErrorHandler.AppError  String Acom.example.myapplication.utils.ErrorHandler.AppError.CustomError  Boolean /com.example.myapplication.utils.ValidationUtils  Double /com.example.myapplication.utils.ValidationUtils  Long /com.example.myapplication.utils.ValidationUtils  String /com.example.myapplication.utils.ValidationUtils  ValidationResult /com.example.myapplication.utils.ValidationUtils  Boolean @com.example.myapplication.utils.ValidationUtils.ValidationResult  String @com.example.myapplication.utils.ValidationUtils.ValidationResult  IOException java.io  AddTransactionUiState 	java.lang  CategoryEntity 	java.lang  CategoryManagementUiState 	java.lang  Context 	java.lang  CreateCategoryFormState 	java.lang  CreateWalletFormState 	java.lang  ExperimentalFoundationApi 	java.lang  ExperimentalMaterial3Api 	java.lang  HomeUiState 	java.lang  MutableStateFlow 	java.lang  RepositoryProvider 	java.lang  SettingsUiState 	java.lang  System 	java.lang  TransactionEntity 	java.lang  WalletEntity 	java.lang  WalletManagementUiState 	java.lang  
WalletUiState 	java.lang  asStateFlow 	java.lang  com 	java.lang  insertDefaultCategories 	java.lang  listOf 	java.lang  
trimIndent 	java.lang  currentTimeMillis java.lang.System  SocketTimeoutException java.net  UnknownHostException java.net  AddTransactionUiState kotlin  Array kotlin  Boolean kotlin  CategoryEntity kotlin  CategoryManagementUiState kotlin  Context kotlin  CreateCategoryFormState kotlin  CreateWalletFormState kotlin  Double kotlin  ExperimentalFoundationApi kotlin  ExperimentalMaterial3Api kotlin  Float kotlin  HomeUiState kotlin  Int kotlin  Long kotlin  MutableStateFlow kotlin  Nothing kotlin  OptIn kotlin  RepositoryProvider kotlin  SettingsUiState kotlin  String kotlin  System kotlin  	Throwable kotlin  TransactionEntity kotlin  Unit kotlin  Volatile kotlin  WalletEntity kotlin  WalletManagementUiState kotlin  
WalletUiState kotlin  arrayOf kotlin  asStateFlow kotlin  com kotlin  insertDefaultCategories kotlin  listOf kotlin  
trimIndent kotlin  getSP 
kotlin.Double  getSp 
kotlin.Double  getSP 
kotlin.Int  getSp 
kotlin.Int  
getTRIMIndent 
kotlin.String  
getTrimIndent 
kotlin.String  AddTransactionUiState kotlin.annotation  CategoryEntity kotlin.annotation  CategoryManagementUiState kotlin.annotation  Context kotlin.annotation  CreateCategoryFormState kotlin.annotation  CreateWalletFormState kotlin.annotation  ExperimentalFoundationApi kotlin.annotation  ExperimentalMaterial3Api kotlin.annotation  HomeUiState kotlin.annotation  MutableStateFlow kotlin.annotation  RepositoryProvider kotlin.annotation  SettingsUiState kotlin.annotation  System kotlin.annotation  TransactionEntity kotlin.annotation  Volatile kotlin.annotation  WalletEntity kotlin.annotation  WalletManagementUiState kotlin.annotation  
WalletUiState kotlin.annotation  asStateFlow kotlin.annotation  com kotlin.annotation  insertDefaultCategories kotlin.annotation  listOf kotlin.annotation  
trimIndent kotlin.annotation  AddTransactionUiState kotlin.collections  CategoryEntity kotlin.collections  CategoryManagementUiState kotlin.collections  Context kotlin.collections  CreateCategoryFormState kotlin.collections  CreateWalletFormState kotlin.collections  ExperimentalFoundationApi kotlin.collections  ExperimentalMaterial3Api kotlin.collections  HomeUiState kotlin.collections  List kotlin.collections  MutableStateFlow kotlin.collections  RepositoryProvider kotlin.collections  SettingsUiState kotlin.collections  System kotlin.collections  TransactionEntity kotlin.collections  Volatile kotlin.collections  WalletEntity kotlin.collections  WalletManagementUiState kotlin.collections  
WalletUiState kotlin.collections  asStateFlow kotlin.collections  com kotlin.collections  insertDefaultCategories kotlin.collections  listOf kotlin.collections  
trimIndent kotlin.collections  AddTransactionUiState kotlin.comparisons  CategoryEntity kotlin.comparisons  CategoryManagementUiState kotlin.comparisons  Context kotlin.comparisons  CreateCategoryFormState kotlin.comparisons  CreateWalletFormState kotlin.comparisons  ExperimentalFoundationApi kotlin.comparisons  ExperimentalMaterial3Api kotlin.comparisons  HomeUiState kotlin.comparisons  MutableStateFlow kotlin.comparisons  RepositoryProvider kotlin.comparisons  SettingsUiState kotlin.comparisons  System kotlin.comparisons  TransactionEntity kotlin.comparisons  Volatile kotlin.comparisons  WalletEntity kotlin.comparisons  WalletManagementUiState kotlin.comparisons  
WalletUiState kotlin.comparisons  asStateFlow kotlin.comparisons  com kotlin.comparisons  insertDefaultCategories kotlin.comparisons  listOf kotlin.comparisons  
trimIndent kotlin.comparisons  AddTransactionUiState 	kotlin.io  CategoryEntity 	kotlin.io  CategoryManagementUiState 	kotlin.io  Context 	kotlin.io  CreateCategoryFormState 	kotlin.io  CreateWalletFormState 	kotlin.io  ExperimentalFoundationApi 	kotlin.io  ExperimentalMaterial3Api 	kotlin.io  HomeUiState 	kotlin.io  MutableStateFlow 	kotlin.io  RepositoryProvider 	kotlin.io  SettingsUiState 	kotlin.io  System 	kotlin.io  TransactionEntity 	kotlin.io  Volatile 	kotlin.io  WalletEntity 	kotlin.io  WalletManagementUiState 	kotlin.io  
WalletUiState 	kotlin.io  asStateFlow 	kotlin.io  com 	kotlin.io  insertDefaultCategories 	kotlin.io  listOf 	kotlin.io  
trimIndent 	kotlin.io  AddTransactionUiState 
kotlin.jvm  CategoryEntity 
kotlin.jvm  CategoryManagementUiState 
kotlin.jvm  Context 
kotlin.jvm  CreateCategoryFormState 
kotlin.jvm  CreateWalletFormState 
kotlin.jvm  ExperimentalFoundationApi 
kotlin.jvm  ExperimentalMaterial3Api 
kotlin.jvm  HomeUiState 
kotlin.jvm  MutableStateFlow 
kotlin.jvm  RepositoryProvider 
kotlin.jvm  SettingsUiState 
kotlin.jvm  System 
kotlin.jvm  TransactionEntity 
kotlin.jvm  Volatile 
kotlin.jvm  WalletEntity 
kotlin.jvm  WalletManagementUiState 
kotlin.jvm  
WalletUiState 
kotlin.jvm  asStateFlow 
kotlin.jvm  com 
kotlin.jvm  insertDefaultCategories 
kotlin.jvm  listOf 
kotlin.jvm  
trimIndent 
kotlin.jvm  AddTransactionUiState 
kotlin.ranges  CategoryEntity 
kotlin.ranges  CategoryManagementUiState 
kotlin.ranges  Context 
kotlin.ranges  CreateCategoryFormState 
kotlin.ranges  CreateWalletFormState 
kotlin.ranges  ExperimentalFoundationApi 
kotlin.ranges  ExperimentalMaterial3Api 
kotlin.ranges  HomeUiState 
kotlin.ranges  MutableStateFlow 
kotlin.ranges  RepositoryProvider 
kotlin.ranges  SettingsUiState 
kotlin.ranges  System 
kotlin.ranges  TransactionEntity 
kotlin.ranges  Volatile 
kotlin.ranges  WalletEntity 
kotlin.ranges  WalletManagementUiState 
kotlin.ranges  
WalletUiState 
kotlin.ranges  asStateFlow 
kotlin.ranges  com 
kotlin.ranges  insertDefaultCategories 
kotlin.ranges  listOf 
kotlin.ranges  
trimIndent 
kotlin.ranges  KClass kotlin.reflect  AddTransactionUiState kotlin.sequences  CategoryEntity kotlin.sequences  CategoryManagementUiState kotlin.sequences  Context kotlin.sequences  CreateCategoryFormState kotlin.sequences  CreateWalletFormState kotlin.sequences  ExperimentalFoundationApi kotlin.sequences  ExperimentalMaterial3Api kotlin.sequences  HomeUiState kotlin.sequences  MutableStateFlow kotlin.sequences  RepositoryProvider kotlin.sequences  SettingsUiState kotlin.sequences  System kotlin.sequences  TransactionEntity kotlin.sequences  Volatile kotlin.sequences  WalletEntity kotlin.sequences  WalletManagementUiState kotlin.sequences  
WalletUiState kotlin.sequences  asStateFlow kotlin.sequences  com kotlin.sequences  insertDefaultCategories kotlin.sequences  listOf kotlin.sequences  
trimIndent kotlin.sequences  AddTransactionUiState kotlin.text  CategoryEntity kotlin.text  CategoryManagementUiState kotlin.text  Context kotlin.text  CreateCategoryFormState kotlin.text  CreateWalletFormState kotlin.text  ExperimentalFoundationApi kotlin.text  ExperimentalMaterial3Api kotlin.text  HomeUiState kotlin.text  MutableStateFlow kotlin.text  RepositoryProvider kotlin.text  SettingsUiState kotlin.text  System kotlin.text  TransactionEntity kotlin.text  Volatile kotlin.text  WalletEntity kotlin.text  WalletManagementUiState kotlin.text  
WalletUiState kotlin.text  asStateFlow kotlin.text  com kotlin.text  insertDefaultCategories kotlin.text  listOf kotlin.text  
trimIndent kotlin.text  launch kotlinx.coroutines  Flow kotlinx.coroutines.flow  MutableStateFlow kotlinx.coroutines.flow  	StateFlow kotlinx.coroutines.flow  asStateFlow kotlinx.coroutines.flow  combine kotlinx.coroutines.flow  asStateFlow (kotlinx.coroutines.flow.MutableStateFlow  getASStateFlow (kotlinx.coroutines.flow.MutableStateFlow  getAsStateFlow (kotlinx.coroutines.flow.MutableStateFlow  value (kotlinx.coroutines.flow.MutableStateFlow  lifecycleScope androidx.lifecycle  
getTRIMIndent Vcom.example.myapplication.data.local.DatabaseProvider.MIGRATION_2_3.<no name provided>  
getTrimIndent Vcom.example.myapplication.data.local.DatabaseProvider.MIGRATION_2_3.<no name provided>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 