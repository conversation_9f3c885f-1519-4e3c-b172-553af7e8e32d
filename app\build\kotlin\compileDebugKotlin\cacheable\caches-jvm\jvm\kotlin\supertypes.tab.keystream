&com.example.myapplication.MainActivity0com.example.myapplication.data.local.AppDatabase6com.example.myapplication.ui.screen.home.HomeViewModel3com.example.myapplication.ui.navigation.Screen.Home5com.example.myapplication.ui.navigation.Screen.Wallet=com.example.myapplication.ui.navigation.Screen.AddTransactionCcom.example.myapplication.ui.screen.add_transaction.TransactionTypeKcom.example.myapplication.ui.screen.add_transaction.AddTransactionViewModel:com.example.myapplication.ui.screen.wallet.WalletViewModelBcom.example.myapplication.utils.ErrorHandler.AppError.NetworkErrorCcom.example.myapplication.utils.ErrorHandler.AppError.DatabaseErrorEcom.example.myapplication.utils.ErrorHandler.AppError.ValidationErrorGcom.example.myapplication.utils.ErrorHandler.AppError.InsufficientFundsAcom.example.myapplication.utils.ErrorHandler.AppError.CustomError7com.example.myapplication.ui.navigation.Screen.Settings?com.example.myapplication.ui.navigation.Screen.WalletManagementAcom.example.myapplication.ui.navigation.Screen.CategoryManagementDcom.example.myapplication.ui.screen.category_management.CategoryTypeScom.example.myapplication.ui.screen.category_management.CategoryManagementViewModel>com.example.myapplication.ui.screen.settings.SettingsViewModelOcom.example.myapplication.ui.screen.wallet_management.WalletManagementViewModel                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     