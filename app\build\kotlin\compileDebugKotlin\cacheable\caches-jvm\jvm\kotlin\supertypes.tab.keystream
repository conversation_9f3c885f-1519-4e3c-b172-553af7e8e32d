&com.example.myapplication.MainActivity0com.example.myapplication.data.local.AppDatabase3com.example.myapplication.ui.navigation.Screen.Home5com.example.myapplication.ui.navigation.Screen.Wallet=com.example.myapplication.ui.navigation.Screen.AddTransaction7com.example.myapplication.ui.navigation.Screen.Settings?com.example.myapplication.ui.navigation.Screen.WalletManagementAcom.example.myapplication.ui.navigation.Screen.CategoryManagementCcom.example.myapplication.ui.screen.add_transaction.TransactionTypeKcom.example.myapplication.ui.screen.add_transaction.AddTransactionViewModelDcom.example.myapplication.ui.screen.category_management.CategoryTypeScom.example.myapplication.ui.screen.category_management.CategoryManagementViewModel6com.example.myapplication.ui.screen.home.HomeViewModel>com.example.myapplication.ui.screen.settings.SettingsViewModel:com.example.myapplication.ui.screen.wallet.WalletViewModelOcom.example.myapplication.ui.screen.wallet_management.WalletManagementViewModelBcom.example.myapplication.utils.ErrorHandler.AppError.NetworkErrorCcom.example.myapplication.utils.ErrorHandler.AppError.DatabaseErrorEcom.example.myapplication.utils.ErrorHandler.AppError.ValidationErrorGcom.example.myapplication.utils.ErrorHandler.AppError.InsufficientFundsAcom.example.myapplication.utils.ErrorHandler.AppError.CustomError                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     