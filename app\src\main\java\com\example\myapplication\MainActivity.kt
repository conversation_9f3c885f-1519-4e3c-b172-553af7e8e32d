package com.example.myapplication

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.ui.Modifier
import androidx.lifecycle.lifecycleScope
import androidx.navigation.compose.rememberNavController
import com.example.myapplication.data.local.DatabaseProvider
import com.example.myapplication.data.repository.RepositoryProvider
import com.example.myapplication.ui.navigation.AppNavHost
import com.example.myapplication.ui.theme.MyApplicationTheme
import kotlinx.coroutines.launch

class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()

        // Initialize default data
        initializeDefaultData()

        setContent {
            MyApplicationTheme {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    val navController = rememberNavController()
                    AppNavHost(navController = navController)
                }
            }
        }
    }

    private fun initializeDefaultData() {
        lifecycleScope.launch {
            try {
                // Check database health first
                val isHealthy = DatabaseProvider.checkDatabaseHealth(this@MainActivity)
                if (!isHealthy) {
                    android.util.Log.w("MainActivity", "Database health check failed, attempting reset...")

                    // For development: reset database if health check fails
                    try {
                        DatabaseProvider.resetDatabase(this@MainActivity)
                        android.util.Log.d("MainActivity", "Database reset successful")
                    } catch (resetException: Exception) {
                        android.util.Log.e("MainActivity", "Database reset failed", resetException)
                        return@launch
                    }
                }

                // Initialize default wallets
                val walletRepository = RepositoryProvider.provideWalletRepository(this@MainActivity)
                walletRepository.createDefaultWalletsIfNeeded()
                android.util.Log.d("MainActivity", "Default data initialized successfully")
            } catch (e: Exception) {
                android.util.Log.e("MainActivity", "Error initializing default data", e)

                // Last resort: reset database and try again
                try {
                    android.util.Log.w("MainActivity", "Attempting database reset as last resort...")
                    DatabaseProvider.resetDatabase(this@MainActivity)
                    val walletRepository = RepositoryProvider.provideWalletRepository(this@MainActivity)
                    walletRepository.createDefaultWalletsIfNeeded()
                    android.util.Log.d("MainActivity", "Database reset and initialization successful")
                } catch (resetException: Exception) {
                    android.util.Log.e("MainActivity", "Final database reset attempt failed", resetException)
                }
            }
        }
    }
}
