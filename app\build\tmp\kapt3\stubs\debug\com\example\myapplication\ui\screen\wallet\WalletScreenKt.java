package com.example.myapplication.ui.screen.wallet;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u00008\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0006\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\b\n\u0002\u0018\u0002\n\u0002\b\u0002\u001a8\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0018\u0010\u0004\u001a\u0014\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u00052\f\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00010\bH\u0003\u001a4\u0010\t\u001a\u00020\u00012\b\b\u0002\u0010\n\u001a\u00020\u000b2\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\f\u001a\u00020\u00062\u0006\u0010\r\u001a\u00020\u000eH\u0003\u00f8\u0001\u0000\u00a2\u0006\u0004\b\u000f\u0010\u0010\u001a\u0018\u0010\u0011\u001a\u00020\u00012\u0006\u0010\u0012\u001a\u00020\u00062\u0006\u0010\u0013\u001a\u00020\u0003H\u0003\u001a\"\u0010\u0014\u001a\u00020\u00012\u000e\b\u0002\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u00010\b2\b\b\u0002\u0010\u0016\u001a\u00020\u0017H\u0007\u001a\u0010\u0010\u0018\u001a\u00020\u00032\u0006\u0010\f\u001a\u00020\u0006H\u0002\u0082\u0002\u0007\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006\u0019"}, d2 = {"MoneyDialog", "", "title", "", "onConfirm", "Lkotlin/Function2;", "", "onDismiss", "Lkotlin/Function0;", "StatisticCard", "modifier", "Landroidx/compose/ui/Modifier;", "amount", "color", "Landroidx/compose/ui/graphics/Color;", "StatisticCard-g2O1Hgs", "(Landroidx/compose/ui/Modifier;Ljava/lang/String;DJ)V", "WalletBalanceCard", "balance", "walletName", "WalletScreen", "onNavigateToWalletManagement", "viewModel", "Lcom/example/myapplication/ui/screen/wallet/WalletViewModel;", "formatCurrency", "app_debug"})
public final class WalletScreenKt {
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void WalletScreen(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateToWalletManagement, @org.jetbrains.annotations.NotNull()
    com.example.myapplication.ui.screen.wallet.WalletViewModel viewModel) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void WalletBalanceCard(double balance, java.lang.String walletName) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void MoneyDialog(java.lang.String title, kotlin.jvm.functions.Function2<? super java.lang.Double, ? super java.lang.String, kotlin.Unit> onConfirm, kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss) {
    }
    
    private static final java.lang.String formatCurrency(double amount) {
        return null;
    }
}