package com.example.myapplication.ui.screen.settings

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.example.myapplication.utils.ValidationUtils

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SettingsScreen(
    onNavigateBack: () -> Unit = {},
    onNavigateToCategoryManagement: () -> Unit = {},
    viewModel: SettingsViewModel = viewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    
    LaunchedEffect(uiState.successMessage) {
        if (uiState.successMessage != null) {
            kotlinx.coroutines.delay(2000)
            viewModel.clearMessages()
        }
    }
    
    LaunchedEffect(uiState.errorMessage) {
        if (uiState.errorMessage != null) {
            kotlinx.coroutines.delay(3000)
            viewModel.clearMessages()
        }
    }
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { 
                    Text(
                        "Cài đặt",
                        fontWeight = FontWeight.Bold
                    ) 
                },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "Quay lại"
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = MaterialTheme.colorScheme.primaryContainer,
                    titleContentColor = MaterialTheme.colorScheme.onPrimaryContainer
                )
            )
        }
    ) { paddingValues ->
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // Success/Error messages
            item {
                uiState.successMessage?.let { message ->
                    Card(
                        modifier = Modifier.fillMaxWidth(),
                        colors = CardDefaults.cardColors(
                            containerColor = Color(0xFF4CAF50).copy(alpha = 0.1f)
                        )
                    ) {
                        Text(
                            text = message,
                            modifier = Modifier.padding(16.dp),
                            color = Color(0xFF2E7D32)
                        )
                    }
                }
                
                uiState.errorMessage?.let { error ->
                    Card(
                        modifier = Modifier.fillMaxWidth(),
                        colors = CardDefaults.cardColors(
                            containerColor = MaterialTheme.colorScheme.errorContainer
                        )
                    ) {
                        Text(
                            text = error,
                            modifier = Modifier.padding(16.dp),
                            color = MaterialTheme.colorScheme.onErrorContainer
                        )
                    }
                }
            }
            
            // Statistics Section
            item {
                StatisticsSection(
                    totalWallets = uiState.totalWallets,
                    totalTransactions = uiState.totalTransactions,
                    totalBalance = uiState.totalBalance
                )
            }
            
            // Account Preferences Section
            item {
                SettingsSection(
                    title = "Tài khoản",
                    icon = Icons.Default.AccountCircle
                ) {
                    SettingsItem(
                        title = "Loại tiền tệ mặc định",
                        subtitle = uiState.defaultCurrency,
                        icon = Icons.Default.AttachMoney,
                        onClick = { /* TODO: Show currency selector */ }
                    )

                    SettingsItem(
                        title = "Quản lý danh mục",
                        subtitle = "Tùy chỉnh danh mục thu chi",
                        icon = Icons.Default.Category,
                        onClick = onNavigateToCategoryManagement
                    )
                }
            }
            
            // App Preferences Section
            item {
                SettingsSection(
                    title = "Ứng dụng",
                    icon = Icons.Default.Settings
                ) {
                    SettingsItem(
                        title = "Chế độ tối",
                        subtitle = if (uiState.isDarkTheme) "Bật" else "Tắt",
                        icon = Icons.Default.DarkMode,
                        trailing = {
                            Switch(
                                checked = uiState.isDarkTheme,
                                onCheckedChange = viewModel::updateDarkTheme
                            )
                        }
                    )
                    
                    SettingsItem(
                        title = "Thông báo",
                        subtitle = if (uiState.isNotificationsEnabled) "Bật" else "Tắt",
                        icon = Icons.Default.Notifications,
                        trailing = {
                            Switch(
                                checked = uiState.isNotificationsEnabled,
                                onCheckedChange = viewModel::updateNotifications
                            )
                        }
                    )
                }
            }
            
            // Data Management Section
            item {
                SettingsSection(
                    title = "Quản lý dữ liệu",
                    icon = Icons.Default.Storage
                ) {
                    SettingsItem(
                        title = "Xuất dữ liệu",
                        subtitle = "Sao lưu dữ liệu của bạn",
                        icon = Icons.Default.FileDownload,
                        onClick = { viewModel.showExportDataDialog() }
                    )
                    
                    SettingsItem(
                        title = "Xóa tất cả dữ liệu",
                        subtitle = "Xóa toàn bộ dữ liệu ứng dụng",
                        icon = Icons.Default.DeleteForever,
                        iconTint = MaterialTheme.colorScheme.error,
                        onClick = { viewModel.showClearDataDialog() }
                    )
                }
            }
            
            // About Section
            item {
                SettingsSection(
                    title = "Thông tin",
                    icon = Icons.Default.Info
                ) {
                    SettingsItem(
                        title = "Phiên bản ứng dụng",
                        subtitle = uiState.appVersion,
                        icon = Icons.Default.AppRegistration
                    )
                    
                    SettingsItem(
                        title = "Chính sách bảo mật",
                        subtitle = "Xem chính sách bảo mật",
                        icon = Icons.Default.PrivacyTip,
                        onClick = { /* TODO: Open privacy policy */ }
                    )
                    
                    SettingsItem(
                        title = "Điều khoản sử dụng",
                        subtitle = "Xem điều khoản sử dụng",
                        icon = Icons.Default.Description,
                        onClick = { /* TODO: Open terms of service */ }
                    )
                }
            }
        }
    }
    
    // Clear Data Confirmation Dialog
    if (uiState.showClearDataDialog) {
        AlertDialog(
            onDismissRequest = { viewModel.hideClearDataDialog() },
            title = {
                Text(
                    text = "Xác nhận xóa dữ liệu",
                    fontWeight = FontWeight.Bold
                )
            },
            text = {
                Text("Bạn có chắc chắn muốn xóa toàn bộ dữ liệu? Hành động này không thể hoàn tác.")
            },
            confirmButton = {
                Button(
                    onClick = { viewModel.clearAllData() },
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.colorScheme.error
                    )
                ) {
                    Text("Xóa")
                }
            },
            dismissButton = {
                TextButton(onClick = { viewModel.hideClearDataDialog() }) {
                    Text("Hủy")
                }
            }
        )
    }
    
    // Export Data Dialog
    if (uiState.showExportDataDialog) {
        AlertDialog(
            onDismissRequest = { viewModel.hideExportDataDialog() },
            title = {
                Text(
                    text = "Xuất dữ liệu",
                    fontWeight = FontWeight.Bold
                )
            },
            text = {
                Text("Dữ liệu sẽ được xuất dưới dạng file JSON. Bạn có muốn tiếp tục?")
            },
            confirmButton = {
                Button(onClick = { viewModel.exportData() }) {
                    Text("Xuất")
                }
            },
            dismissButton = {
                TextButton(onClick = { viewModel.hideExportDataDialog() }) {
                    Text("Hủy")
                }
            }
        )
    }
}

@Composable
private fun StatisticsSection(
    totalWallets: Int,
    totalTransactions: Int,
    totalBalance: Double
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.primaryContainer
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "Thống kê",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.onPrimaryContainer
            )

            Spacer(modifier = Modifier.height(12.dp))

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                StatisticItem(
                    title = "Tổng số dư",
                    value = ValidationUtils.formatCurrency(totalBalance),
                    modifier = Modifier.weight(1f)
                )

                StatisticItem(
                    title = "Số ví",
                    value = totalWallets.toString(),
                    modifier = Modifier.weight(1f)
                )

                StatisticItem(
                    title = "Giao dịch",
                    value = totalTransactions.toString(),
                    modifier = Modifier.weight(1f)
                )
            }
        }
    }
}

@Composable
private fun StatisticItem(
    title: String,
    value: String,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = value,
            style = MaterialTheme.typography.titleLarge,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )
        Text(
            text = title,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.7f)
        )
    }
}

@Composable
private fun SettingsSection(
    title: String,
    icon: ImageVector,
    content: @Composable () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = icon,
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.size(20.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = title,
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
            }

            Spacer(modifier = Modifier.height(12.dp))

            content()
        }
    }
}

@Composable
private fun SettingsItem(
    title: String,
    subtitle: String? = null,
    icon: ImageVector,
    iconTint: Color = MaterialTheme.colorScheme.onSurfaceVariant,
    trailing: @Composable (() -> Unit)? = null,
    onClick: (() -> Unit)? = null
) {
    val modifier = if (onClick != null) {
        Modifier.fillMaxWidth()
    } else {
        Modifier.fillMaxWidth()
    }

    Card(
        onClick = onClick ?: {},
        modifier = modifier,
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        enabled = onClick != null
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                tint = iconTint,
                modifier = Modifier.size(24.dp)
            )

            Spacer(modifier = Modifier.width(12.dp))

            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = title,
                    style = MaterialTheme.typography.bodyLarge,
                    fontWeight = FontWeight.Medium
                )

                subtitle?.let {
                    Text(
                        text = it,
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }

            trailing?.invoke()
        }
    }
}
