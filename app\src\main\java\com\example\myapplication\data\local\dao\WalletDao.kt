package com.example.myapplication.data.local.dao

import androidx.room.*
import com.example.myapplication.data.local.entity.WalletEntity
import kotlinx.coroutines.flow.Flow

@Dao
interface WalletDao {
    @Query("SELECT * FROM wallets WHERE isActive = 1 ORDER BY name ASC")
    fun getAllActiveWallets(): Flow<List<WalletEntity>>

    @Query("SELECT * FROM wallets ORDER BY name ASC")
    fun getAllWallets(): Flow<List<WalletEntity>>

    @Query("SELECT * FROM wallets ORDER BY name ASC")
    fun getAllWalletsSync(): List<WalletEntity>

    @Query("SELECT * FROM wallets WHERE isActive = 1 AND isFrozen = 0 ORDER BY name ASC")
    fun getAvailableWallets(): Flow<List<WalletEntity>>

    @Query("SELECT * FROM wallets WHERE id = :id")
    suspend fun getWalletById(id: Int): WalletEntity?

    @Query("SELECT * FROM wallets WHERE isActive = 1 LIMIT 1")
    suspend fun getFirstActiveWallet(): WalletEntity?

    @Query("SELECT * FROM wallets WHERE isActive = 1 LIMIT 1")
    fun getFirstActiveWalletFlow(): Flow<WalletEntity?>

    @Query("SELECT * FROM wallets WHERE isActive = 1 AND isFrozen = 0 LIMIT 1")
    suspend fun getFirstAvailableWallet(): WalletEntity?

    @Insert
    suspend fun insertWallet(wallet: WalletEntity): Long

    @Update
    suspend fun updateWallet(wallet: WalletEntity)

    @Delete
    suspend fun deleteWallet(wallet: WalletEntity)

    @Query("UPDATE wallets SET balance = balance + :amount, updatedAt = :timestamp WHERE id = :walletId")
    suspend fun updateBalance(walletId: Int, amount: Double, timestamp: Long = System.currentTimeMillis())

    @Query("SELECT balance FROM wallets WHERE id = :walletId")
    suspend fun getBalance(walletId: Int): Double?

    @Query("UPDATE wallets SET isFrozen = :isFrozen, updatedAt = :timestamp WHERE id = :walletId")
    suspend fun updateFrozenStatus(walletId: Int, isFrozen: Boolean, timestamp: Long = System.currentTimeMillis())

    @Query("UPDATE wallets SET isActive = 0, updatedAt = :timestamp WHERE id = :walletId")
    suspend fun deactivateWallet(walletId: Int, timestamp: Long = System.currentTimeMillis())

    @Query("UPDATE wallets SET isActive = 1, updatedAt = :timestamp WHERE id = :walletId")
    suspend fun activateWallet(walletId: Int, timestamp: Long = System.currentTimeMillis())

    @Query("SELECT COUNT(*) FROM wallets WHERE isActive = 1")
    suspend fun getActiveWalletCount(): Int

    @Query("SELECT SUM(balance) FROM wallets WHERE isActive = 1")
    suspend fun getTotalBalance(): Double?

    @Query("SELECT * FROM wallets WHERE name LIKE '%' || :searchQuery || '%' AND isActive = 1")
    fun searchWallets(searchQuery: String): Flow<List<WalletEntity>>
}
