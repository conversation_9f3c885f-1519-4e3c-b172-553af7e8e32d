package com.example.myapplication.data.local.dao

import androidx.room.*
import com.example.myapplication.data.local.entity.WalletEntity
import kotlinx.coroutines.flow.Flow

@Dao
interface WalletDao {
    @Query("SELECT * FROM wallets")
    fun getAllWallets(): Flow<List<WalletEntity>>

    @Query("SELECT * FROM wallets WHERE id = :id")
    suspend fun getWalletById(id: Int): WalletEntity?

    @Query("SELECT * FROM wallets LIMIT 1")
    suspend fun getFirstWallet(): WalletEntity?

    @Query("SELECT * FROM wallets LIMIT 1")
    fun getFirstWalletFlow(): Flow<WalletEntity?>

    @Insert
    suspend fun insertWallet(wallet: WalletEntity): Long

    @Update
    suspend fun updateWallet(wallet: WalletEntity)

    @Delete
    suspend fun deleteWallet(wallet: WalletEntity)

    @Query("UPDATE wallets SET balance = balance + :amount WHERE id = :walletId")
    suspend fun updateBalance(walletId: Int, amount: Double)

    @Query("SELECT balance FROM wallets WHERE id = :walletId")
    suspend fun getBalance(walletId: Int): Double?
}
