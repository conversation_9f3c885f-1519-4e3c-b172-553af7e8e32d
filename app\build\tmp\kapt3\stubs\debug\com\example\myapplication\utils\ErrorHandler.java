package com.example.myapplication.utils;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0003\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0004\b\u00c7\u0002\u0018\u00002\u00020\u0001:\u0001\u000bB\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006J\"\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\u00042\u0006\u0010\n\u001a\u00020\u00042\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0006\u00a8\u0006\f"}, d2 = {"Lcom/example/myapplication/utils/ErrorHandler;", "", "()V", "getErrorMessage", "", "throwable", "", "logError", "", "tag", "message", "AppError", "app_debug"})
public final class ErrorHandler {
    @org.jetbrains.annotations.NotNull()
    public static final com.example.myapplication.utils.ErrorHandler INSTANCE = null;
    
    private ErrorHandler() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getErrorMessage(@org.jetbrains.annotations.NotNull()
    java.lang.Throwable throwable) {
        return null;
    }
    
    public final void logError(@org.jetbrains.annotations.NotNull()
    java.lang.String tag, @org.jetbrains.annotations.NotNull()
    java.lang.String message, @org.jetbrains.annotations.Nullable()
    java.lang.Throwable throwable) {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\b\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\b7\u0018\u00002\u00020\u0001:\u0005\u0007\b\t\n\u000bB\u000f\b\u0004\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006\u0082\u0001\u0005\f\r\u000e\u000f\u0010\u00a8\u0006\u0011"}, d2 = {"Lcom/example/myapplication/utils/ErrorHandler$AppError;", "", "message", "", "(Ljava/lang/String;)V", "getMessage", "()Ljava/lang/String;", "CustomError", "DatabaseError", "InsufficientFunds", "NetworkError", "ValidationError", "Lcom/example/myapplication/utils/ErrorHandler$AppError$CustomError;", "Lcom/example/myapplication/utils/ErrorHandler$AppError$DatabaseError;", "Lcom/example/myapplication/utils/ErrorHandler$AppError$InsufficientFunds;", "Lcom/example/myapplication/utils/ErrorHandler$AppError$NetworkError;", "Lcom/example/myapplication/utils/ErrorHandler$AppError$ValidationError;", "app_debug"})
    public static abstract class AppError {
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String message = null;
        
        private AppError(java.lang.String message) {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getMessage() {
            return null;
        }
        
        @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\b\u0087\b\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\t\u0010\u0007\u001a\u00020\u0003H\u00c6\u0003J\u0013\u0010\b\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\t\u001a\u00020\n2\b\u0010\u000b\u001a\u0004\u0018\u00010\fH\u00d6\u0003J\t\u0010\r\u001a\u00020\u000eH\u00d6\u0001J\t\u0010\u000f\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006\u00a8\u0006\u0010"}, d2 = {"Lcom/example/myapplication/utils/ErrorHandler$AppError$CustomError;", "Lcom/example/myapplication/utils/ErrorHandler$AppError;", "customMessage", "", "(Ljava/lang/String;)V", "getCustomMessage", "()Ljava/lang/String;", "component1", "copy", "equals", "", "other", "", "hashCode", "", "toString", "app_debug"})
        public static final class CustomError extends com.example.myapplication.utils.ErrorHandler.AppError {
            @org.jetbrains.annotations.NotNull()
            private final java.lang.String customMessage = null;
            
            public CustomError(@org.jetbrains.annotations.NotNull()
            java.lang.String customMessage) {
            }
            
            @org.jetbrains.annotations.NotNull()
            public final java.lang.String getCustomMessage() {
                return null;
            }
            
            @org.jetbrains.annotations.NotNull()
            public final java.lang.String component1() {
                return null;
            }
            
            @org.jetbrains.annotations.NotNull()
            public final com.example.myapplication.utils.ErrorHandler.AppError.CustomError copy(@org.jetbrains.annotations.NotNull()
            java.lang.String customMessage) {
                return null;
            }
            
            @java.lang.Override()
            public boolean equals(@org.jetbrains.annotations.Nullable()
            java.lang.Object other) {
                return false;
            }
            
            @java.lang.Override()
            public int hashCode() {
                return 0;
            }
            
            @java.lang.Override()
            @org.jetbrains.annotations.NotNull()
            public java.lang.String toString() {
                return null;
            }
        }
        
        @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/example/myapplication/utils/ErrorHandler$AppError$DatabaseError;", "Lcom/example/myapplication/utils/ErrorHandler$AppError;", "()V", "app_debug"})
        public static final class DatabaseError extends com.example.myapplication.utils.ErrorHandler.AppError {
            @org.jetbrains.annotations.NotNull()
            public static final com.example.myapplication.utils.ErrorHandler.AppError.DatabaseError INSTANCE = null;
            
            private DatabaseError() {
            }
        }
        
        @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/example/myapplication/utils/ErrorHandler$AppError$InsufficientFunds;", "Lcom/example/myapplication/utils/ErrorHandler$AppError;", "()V", "app_debug"})
        public static final class InsufficientFunds extends com.example.myapplication.utils.ErrorHandler.AppError {
            @org.jetbrains.annotations.NotNull()
            public static final com.example.myapplication.utils.ErrorHandler.AppError.InsufficientFunds INSTANCE = null;
            
            private InsufficientFunds() {
            }
        }
        
        @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/example/myapplication/utils/ErrorHandler$AppError$NetworkError;", "Lcom/example/myapplication/utils/ErrorHandler$AppError;", "()V", "app_debug"})
        public static final class NetworkError extends com.example.myapplication.utils.ErrorHandler.AppError {
            @org.jetbrains.annotations.NotNull()
            public static final com.example.myapplication.utils.ErrorHandler.AppError.NetworkError INSTANCE = null;
            
            private NetworkError() {
            }
        }
        
        @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/example/myapplication/utils/ErrorHandler$AppError$ValidationError;", "Lcom/example/myapplication/utils/ErrorHandler$AppError;", "()V", "app_debug"})
        public static final class ValidationError extends com.example.myapplication.utils.ErrorHandler.AppError {
            @org.jetbrains.annotations.NotNull()
            public static final com.example.myapplication.utils.ErrorHandler.AppError.ValidationError INSTANCE = null;
            
            private ValidationError() {
            }
        }
    }
}