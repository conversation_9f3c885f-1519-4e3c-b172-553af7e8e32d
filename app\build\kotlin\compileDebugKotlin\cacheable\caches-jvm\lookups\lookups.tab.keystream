  Activity android.app  
HomeScreen android.app.Activity  
HomeViewModel android.app.Activity  
MaterialTheme android.app.Activity  collectAsState android.app.Activity  onCreate android.app.Activity  
setContent android.app.Activity  	viewModel android.app.Activity  Context android.content  
HomeScreen android.content.Context  
HomeViewModel android.content.Context  
MaterialTheme android.content.Context  collectAsState android.content.Context  
setContent android.content.Context  	viewModel android.content.Context  
HomeScreen android.content.ContextWrapper  
HomeViewModel android.content.ContextWrapper  
MaterialTheme android.content.ContextWrapper  collectAsState android.content.ContextWrapper  
setContent android.content.ContextWrapper  	viewModel android.content.ContextWrapper  Build 
android.os  Bundle 
android.os  SDK_INT android.os.Build.VERSION  S android.os.Build.VERSION_CODES  
HomeScreen  android.view.ContextThemeWrapper  
HomeViewModel  android.view.ContextThemeWrapper  
MaterialTheme  android.view.ContextThemeWrapper  collectAsState  android.view.ContextThemeWrapper  
setContent  android.view.ContextThemeWrapper  	viewModel  android.view.ContextThemeWrapper  ComponentActivity androidx.activity  
HomeScreen #androidx.activity.ComponentActivity  
HomeViewModel #androidx.activity.ComponentActivity  
MaterialTheme #androidx.activity.ComponentActivity  collectAsState #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  
setContent #androidx.activity.ComponentActivity  	viewModel #androidx.activity.ComponentActivity  
setContent androidx.activity.compose  isSystemInDarkTheme androidx.compose.foundation  
Composable "androidx.compose.foundation.layout  ExperimentalMaterial3Api "androidx.compose.foundation.layout  HorizontalDivider "androidx.compose.foundation.layout  List "androidx.compose.foundation.layout  ListItem "androidx.compose.foundation.layout  Modifier "androidx.compose.foundation.layout  OptIn "androidx.compose.foundation.layout  
PaddingValues "androidx.compose.foundation.layout  Scaffold "androidx.compose.foundation.layout  Text "androidx.compose.foundation.layout  	TopAppBar "androidx.compose.foundation.layout  TransactionEntity "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  
LazyColumn  androidx.compose.foundation.lazy  
LazyItemScope  androidx.compose.foundation.lazy  
LazyListScope  androidx.compose.foundation.lazy  items  androidx.compose.foundation.lazy  HorizontalDivider .androidx.compose.foundation.lazy.LazyItemScope  ListItem .androidx.compose.foundation.lazy.LazyItemScope  Text .androidx.compose.foundation.lazy.LazyItemScope  HorizontalDivider .androidx.compose.foundation.lazy.LazyListScope  ListItem .androidx.compose.foundation.lazy.LazyListScope  Text .androidx.compose.foundation.lazy.LazyListScope  items .androidx.compose.foundation.lazy.LazyListScope  ColorScheme androidx.compose.material3  
Composable androidx.compose.material3  ExperimentalMaterial3Api androidx.compose.material3  HorizontalDivider androidx.compose.material3  List androidx.compose.material3  ListItem androidx.compose.material3  
MaterialTheme androidx.compose.material3  Modifier androidx.compose.material3  OptIn androidx.compose.material3  Scaffold androidx.compose.material3  Text androidx.compose.material3  	TopAppBar androidx.compose.material3  TransactionEntity androidx.compose.material3  
Typography androidx.compose.material3  darkColorScheme androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  fillMaxSize androidx.compose.material3  lightColorScheme androidx.compose.material3  padding androidx.compose.material3  
Composable androidx.compose.runtime  ProvidableCompositionLocal androidx.compose.runtime  State androidx.compose.runtime  collectAsState androidx.compose.runtime  
getCurrent )androidx.compose.runtime.CompositionLocal  current 3androidx.compose.runtime.ProvidableCompositionLocal  value androidx.compose.runtime.State  ComposableFunction0 !androidx.compose.runtime.internal  ComposableFunction1 !androidx.compose.runtime.internal  ComposableFunction2 !androidx.compose.runtime.internal  Modifier androidx.compose.ui  	Companion androidx.compose.ui.Modifier  fillMaxSize androidx.compose.ui.Modifier  padding androidx.compose.ui.Modifier  padding &androidx.compose.ui.Modifier.Companion  Color androidx.compose.ui.graphics  LocalContext androidx.compose.ui.platform  	TextStyle androidx.compose.ui.text  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  	Companion (androidx.compose.ui.text.font.FontFamily  Default (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  	Companion (androidx.compose.ui.text.font.FontWeight  Normal (androidx.compose.ui.text.font.FontWeight  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  sp androidx.compose.ui.unit  Bundle #androidx.core.app.ComponentActivity  
HomeScreen #androidx.core.app.ComponentActivity  
HomeViewModel #androidx.core.app.ComponentActivity  
MaterialTheme #androidx.core.app.ComponentActivity  collectAsState #androidx.core.app.ComponentActivity  
setContent #androidx.core.app.ComponentActivity  	viewModel #androidx.core.app.ComponentActivity  	ViewModel androidx.lifecycle  viewModelScope androidx.lifecycle  	viewModel $androidx.lifecycle.viewmodel.compose  Dao 
androidx.room  Database 
androidx.room  Delete 
androidx.room  Entity 
androidx.room  Flow 
androidx.room  
ForeignKey 
androidx.room  Insert 
androidx.room  List 
androidx.room  
PrimaryKey 
androidx.room  Query 
androidx.room  RoomDatabase 
androidx.room  TransactionEntity 
androidx.room  Update 
androidx.room  WalletEntity 
androidx.room  CASCADE androidx.room.ForeignKey  	Companion androidx.room.ForeignKey  CASCADE "androidx.room.ForeignKey.Companion  TransactionDao androidx.room.RoomDatabase  	WalletDao androidx.room.RoomDatabase  Bundle com.example.myapplication  ComponentActivity com.example.myapplication  
HomeScreen com.example.myapplication  
HomeViewModel com.example.myapplication  MainActivity com.example.myapplication  
MaterialTheme com.example.myapplication  collectAsState com.example.myapplication  	viewModel com.example.myapplication  
HomeScreen &com.example.myapplication.MainActivity  
MaterialTheme &com.example.myapplication.MainActivity  collectAsState &com.example.myapplication.MainActivity  
setContent &com.example.myapplication.MainActivity  	viewModel &com.example.myapplication.MainActivity  AppDatabase $com.example.myapplication.data.local  Database $com.example.myapplication.data.local  RoomDatabase $com.example.myapplication.data.local  TransactionDao $com.example.myapplication.data.local  TransactionEntity $com.example.myapplication.data.local  	WalletDao $com.example.myapplication.data.local  WalletEntity $com.example.myapplication.data.local  Dao (com.example.myapplication.data.local.dao  Delete (com.example.myapplication.data.local.dao  Flow (com.example.myapplication.data.local.dao  Insert (com.example.myapplication.data.local.dao  List (com.example.myapplication.data.local.dao  Query (com.example.myapplication.data.local.dao  TransactionDao (com.example.myapplication.data.local.dao  TransactionEntity (com.example.myapplication.data.local.dao  Update (com.example.myapplication.data.local.dao  	WalletDao (com.example.myapplication.data.local.dao  WalletEntity (com.example.myapplication.data.local.dao  getAllTransactions 7com.example.myapplication.data.local.dao.TransactionDao  insertTransaction 7com.example.myapplication.data.local.dao.TransactionDao  
getAllWallets 2com.example.myapplication.data.local.dao.WalletDao  insertWallet 2com.example.myapplication.data.local.dao.WalletDao  Double +com.example.myapplication.data.local.entity  Entity +com.example.myapplication.data.local.entity  
ForeignKey +com.example.myapplication.data.local.entity  Int +com.example.myapplication.data.local.entity  Long +com.example.myapplication.data.local.entity  
PrimaryKey +com.example.myapplication.data.local.entity  String +com.example.myapplication.data.local.entity  System +com.example.myapplication.data.local.entity  TransactionEntity +com.example.myapplication.data.local.entity  WalletEntity +com.example.myapplication.data.local.entity  amount =com.example.myapplication.data.local.entity.TransactionEntity  category =com.example.myapplication.data.local.entity.TransactionEntity  TransactionDao )com.example.myapplication.data.repository  TransactionEntity )com.example.myapplication.data.repository  TransactionRepository )com.example.myapplication.data.repository  	WalletDao )com.example.myapplication.data.repository  WalletEntity )com.example.myapplication.data.repository  WalletRepository )com.example.myapplication.data.repository  transactionDao ?com.example.myapplication.data.repository.TransactionRepository  	walletDao :com.example.myapplication.data.repository.WalletRepository  Transaction &com.example.myapplication.domain.model  Wallet &com.example.myapplication.domain.model  AddTransactionUseCase (com.example.myapplication.domain.usecase  GetTransactionsUseCase (com.example.myapplication.domain.usecase  UpdateWalletBalanceUseCase (com.example.myapplication.domain.usecase  
AppNavHost 'com.example.myapplication.ui.navigation  AddTransactionScreen 3com.example.myapplication.ui.screen.add_transaction  AddTransactionViewModel 3com.example.myapplication.ui.screen.add_transaction  
Composable (com.example.myapplication.ui.screen.home  ExperimentalMaterial3Api (com.example.myapplication.ui.screen.home  
HomeScreen (com.example.myapplication.ui.screen.home  
HomeViewModel (com.example.myapplication.ui.screen.home  HorizontalDivider (com.example.myapplication.ui.screen.home  List (com.example.myapplication.ui.screen.home  ListItem (com.example.myapplication.ui.screen.home  Modifier (com.example.myapplication.ui.screen.home  MutableStateFlow (com.example.myapplication.ui.screen.home  OptIn (com.example.myapplication.ui.screen.home  Scaffold (com.example.myapplication.ui.screen.home  	StateFlow (com.example.myapplication.ui.screen.home  Text (com.example.myapplication.ui.screen.home  	TopAppBar (com.example.myapplication.ui.screen.home  TransactionEntity (com.example.myapplication.ui.screen.home  	ViewModel (com.example.myapplication.ui.screen.home  asStateFlow (com.example.myapplication.ui.screen.home  fillMaxSize (com.example.myapplication.ui.screen.home  listOf (com.example.myapplication.ui.screen.home  padding (com.example.myapplication.ui.screen.home  MutableStateFlow 6com.example.myapplication.ui.screen.home.HomeViewModel  TransactionEntity 6com.example.myapplication.ui.screen.home.HomeViewModel  
_transactions 6com.example.myapplication.ui.screen.home.HomeViewModel  asStateFlow 6com.example.myapplication.ui.screen.home.HomeViewModel  listOf 6com.example.myapplication.ui.screen.home.HomeViewModel  transactions 6com.example.myapplication.ui.screen.home.HomeViewModel  WalletScreen *com.example.myapplication.ui.screen.wallet  WalletViewModel *com.example.myapplication.ui.screen.wallet  Boolean "com.example.myapplication.ui.theme  Build "com.example.myapplication.ui.theme  
Composable "com.example.myapplication.ui.theme  DarkColorScheme "com.example.myapplication.ui.theme  
FontFamily "com.example.myapplication.ui.theme  
FontWeight "com.example.myapplication.ui.theme  LightColorScheme "com.example.myapplication.ui.theme  MyApplicationTheme "com.example.myapplication.ui.theme  Pink40 "com.example.myapplication.ui.theme  Pink80 "com.example.myapplication.ui.theme  Purple40 "com.example.myapplication.ui.theme  Purple80 "com.example.myapplication.ui.theme  PurpleGrey40 "com.example.myapplication.ui.theme  PurpleGrey80 "com.example.myapplication.ui.theme  
Typography "com.example.myapplication.ui.theme  Unit "com.example.myapplication.ui.theme  currentTimeMillis java.lang.System  Array kotlin  	Function1 kotlin  OptIn kotlin  sp 
kotlin.Double  	compareTo 
kotlin.Int  List kotlin.collections  listOf kotlin.collections  KClass kotlin.reflect  Flow kotlinx.coroutines.flow  MutableStateFlow kotlinx.coroutines.flow  	StateFlow kotlinx.coroutines.flow  asStateFlow kotlinx.coroutines.flow  asStateFlow (kotlinx.coroutines.flow.MutableStateFlow  collectAsState !kotlinx.coroutines.flow.StateFlow  Application android.app  
AppNavHost android.app.Activity  Modifier android.app.Activity  MyApplicationTheme android.app.Activity  Surface android.app.Activity  enableEdgeToEdge android.app.Activity  fillMaxSize android.app.Activity  rememberNavController android.app.Activity  
AppNavHost android.content.Context  Modifier android.content.Context  MyApplicationTheme android.content.Context  Surface android.content.Context  applicationContext android.content.Context  enableEdgeToEdge android.content.Context  fillMaxSize android.content.Context  rememberNavController android.content.Context  
AppNavHost android.content.ContextWrapper  Modifier android.content.ContextWrapper  MyApplicationTheme android.content.ContextWrapper  Surface android.content.ContextWrapper  enableEdgeToEdge android.content.ContextWrapper  fillMaxSize android.content.ContextWrapper  rememberNavController android.content.ContextWrapper  SQLiteException android.database.sqlite  message 'android.database.sqlite.SQLiteException  e android.util.Log  
AppNavHost  android.view.ContextThemeWrapper  Modifier  android.view.ContextThemeWrapper  MyApplicationTheme  android.view.ContextThemeWrapper  Surface  android.view.ContextThemeWrapper  enableEdgeToEdge  android.view.ContextThemeWrapper  fillMaxSize  android.view.ContextThemeWrapper  rememberNavController  android.view.ContextThemeWrapper  enableEdgeToEdge androidx.activity  
AppNavHost #androidx.activity.ComponentActivity  Modifier #androidx.activity.ComponentActivity  MyApplicationTheme #androidx.activity.ComponentActivity  Surface #androidx.activity.ComponentActivity  enableEdgeToEdge #androidx.activity.ComponentActivity  fillMaxSize #androidx.activity.ComponentActivity  rememberNavController #androidx.activity.ComponentActivity  AnimatedContentScope androidx.compose.animation  AddTransactionScreen /androidx.compose.animation.AnimatedContentScope  
HomeScreen /androidx.compose.animation.AnimatedContentScope  Screen /androidx.compose.animation.AnimatedContentScope  WalletScreen /androidx.compose.animation.AnimatedContentScope  ScrollState androidx.compose.foundation  rememberScrollState androidx.compose.foundation  verticalScroll androidx.compose.foundation  AddTransactionViewModel "androidx.compose.foundation.layout  AlertDialog "androidx.compose.foundation.layout  	Alignment "androidx.compose.foundation.layout  Arrangement "androidx.compose.foundation.layout  BalanceCard "androidx.compose.foundation.layout  Box "androidx.compose.foundation.layout  BoxScope "androidx.compose.foundation.layout  Button "androidx.compose.foundation.layout  ButtonDefaults "androidx.compose.foundation.layout  Card "androidx.compose.foundation.layout  CardDefaults "androidx.compose.foundation.layout  CategorySelector "androidx.compose.foundation.layout  CircularProgressIndicator "androidx.compose.foundation.layout  Color "androidx.compose.foundation.layout  Column "androidx.compose.foundation.layout  ColumnScope "androidx.compose.foundation.layout  Double "androidx.compose.foundation.layout  EmptyTransactionsCard "androidx.compose.foundation.layout  
FilterChip "androidx.compose.foundation.layout  FloatingActionButton "androidx.compose.foundation.layout  
FontWeight "androidx.compose.foundation.layout  
HomeViewModel "androidx.compose.foundation.layout  Icon "androidx.compose.foundation.layout  
IconButton "androidx.compose.foundation.layout  Icons "androidx.compose.foundation.layout  LaunchedEffect "androidx.compose.foundation.layout  LazyRow "androidx.compose.foundation.layout  Long "androidx.compose.foundation.layout  
MaterialTheme "androidx.compose.foundation.layout  OutlinedButton "androidx.compose.foundation.layout  OutlinedTextField "androidx.compose.foundation.layout  Row "androidx.compose.foundation.layout  RowScope "androidx.compose.foundation.layout  Spacer "androidx.compose.foundation.layout  
StatisticCard "androidx.compose.foundation.layout  String "androidx.compose.foundation.layout  	TextAlign "androidx.compose.foundation.layout  
TextButton "androidx.compose.foundation.layout  TopAppBarDefaults "androidx.compose.foundation.layout  TransactionItem "androidx.compose.foundation.layout  TransactionType "androidx.compose.foundation.layout  TransactionTypeSelector "androidx.compose.foundation.layout  Unit "androidx.compose.foundation.layout  ValidationUtils "androidx.compose.foundation.layout  WalletBalanceCard "androidx.compose.foundation.layout  WalletViewModel "androidx.compose.foundation.layout  buttonColors "androidx.compose.foundation.layout  
cardColors "androidx.compose.foundation.layout  
cardElevation "androidx.compose.foundation.layout  collectAsState "androidx.compose.foundation.layout  fillMaxWidth "androidx.compose.foundation.layout  forEach "androidx.compose.foundation.layout  formatCurrency "androidx.compose.foundation.layout  
formatDate "androidx.compose.foundation.layout  getValue "androidx.compose.foundation.layout  height "androidx.compose.foundation.layout  ifBlank "androidx.compose.foundation.layout  
isNotBlank "androidx.compose.foundation.layout  kotlinx "androidx.compose.foundation.layout  let "androidx.compose.foundation.layout  mutableStateOf "androidx.compose.foundation.layout  provideDelegate "androidx.compose.foundation.layout  remember "androidx.compose.foundation.layout  setValue "androidx.compose.foundation.layout  size "androidx.compose.foundation.layout  spacedBy "androidx.compose.foundation.layout  take "androidx.compose.foundation.layout  toDoubleOrNull "androidx.compose.foundation.layout  topAppBarColors "androidx.compose.foundation.layout  updateAmount "androidx.compose.foundation.layout  
updateNote "androidx.compose.foundation.layout  weight "androidx.compose.foundation.layout  width "androidx.compose.foundation.layout  
Horizontal .androidx.compose.foundation.layout.Arrangement  HorizontalOrVertical .androidx.compose.foundation.layout.Arrangement  SpaceBetween .androidx.compose.foundation.layout.Arrangement  Vertical .androidx.compose.foundation.layout.Arrangement  spacedBy .androidx.compose.foundation.layout.Arrangement  CircularProgressIndicator +androidx.compose.foundation.layout.BoxScope  Add .androidx.compose.foundation.layout.ColumnScope  	Alignment .androidx.compose.foundation.layout.ColumnScope  Arrangement .androidx.compose.foundation.layout.ColumnScope  
ArrowDownward .androidx.compose.foundation.layout.ColumnScope  ArrowUpward .androidx.compose.foundation.layout.ColumnScope  BalanceCard .androidx.compose.foundation.layout.ColumnScope  Box .androidx.compose.foundation.layout.ColumnScope  Button .androidx.compose.foundation.layout.ColumnScope  ButtonDefaults .androidx.compose.foundation.layout.ColumnScope  Card .androidx.compose.foundation.layout.ColumnScope  CardDefaults .androidx.compose.foundation.layout.ColumnScope  CategorySelector .androidx.compose.foundation.layout.ColumnScope  CircularProgressIndicator .androidx.compose.foundation.layout.ColumnScope  Color .androidx.compose.foundation.layout.ColumnScope  Column .androidx.compose.foundation.layout.ColumnScope  
FilterChip .androidx.compose.foundation.layout.ColumnScope  
FontWeight .androidx.compose.foundation.layout.ColumnScope  Icon .androidx.compose.foundation.layout.ColumnScope  Icons .androidx.compose.foundation.layout.ColumnScope  LazyRow .androidx.compose.foundation.layout.ColumnScope  
MaterialTheme .androidx.compose.foundation.layout.ColumnScope  Modifier .androidx.compose.foundation.layout.ColumnScope  OutlinedButton .androidx.compose.foundation.layout.ColumnScope  OutlinedTextField .androidx.compose.foundation.layout.ColumnScope  Row .androidx.compose.foundation.layout.ColumnScope  Spacer .androidx.compose.foundation.layout.ColumnScope  
StatisticCard .androidx.compose.foundation.layout.ColumnScope  Text .androidx.compose.foundation.layout.ColumnScope  	TextAlign .androidx.compose.foundation.layout.ColumnScope  TransactionType .androidx.compose.foundation.layout.ColumnScope  TransactionTypeSelector .androidx.compose.foundation.layout.ColumnScope  Wallet .androidx.compose.foundation.layout.ColumnScope  WalletBalanceCard .androidx.compose.foundation.layout.ColumnScope  buttonColors .androidx.compose.foundation.layout.ColumnScope  
cardColors .androidx.compose.foundation.layout.ColumnScope  dp .androidx.compose.foundation.layout.ColumnScope  fillMaxWidth .androidx.compose.foundation.layout.ColumnScope  forEach .androidx.compose.foundation.layout.ColumnScope  formatCurrency .androidx.compose.foundation.layout.ColumnScope  
formatDate .androidx.compose.foundation.layout.ColumnScope  height .androidx.compose.foundation.layout.ColumnScope  
isNotBlank .androidx.compose.foundation.layout.ColumnScope  items .androidx.compose.foundation.layout.ColumnScope  let .androidx.compose.foundation.layout.ColumnScope  padding .androidx.compose.foundation.layout.ColumnScope  size .androidx.compose.foundation.layout.ColumnScope  sp .androidx.compose.foundation.layout.ColumnScope  spacedBy .androidx.compose.foundation.layout.ColumnScope  updateAmount .androidx.compose.foundation.layout.ColumnScope  
updateNote .androidx.compose.foundation.layout.ColumnScope  weight .androidx.compose.foundation.layout.ColumnScope  width .androidx.compose.foundation.layout.ColumnScope  Add +androidx.compose.foundation.layout.RowScope  
ArrowDownward +androidx.compose.foundation.layout.RowScope  ArrowUpward +androidx.compose.foundation.layout.RowScope  Button +androidx.compose.foundation.layout.RowScope  ButtonDefaults +androidx.compose.foundation.layout.RowScope  CircularProgressIndicator +androidx.compose.foundation.layout.RowScope  Column +androidx.compose.foundation.layout.RowScope  
FilterChip +androidx.compose.foundation.layout.RowScope  
FontWeight +androidx.compose.foundation.layout.RowScope  Icon +androidx.compose.foundation.layout.RowScope  Icons +androidx.compose.foundation.layout.RowScope  
MaterialTheme +androidx.compose.foundation.layout.RowScope  Modifier +androidx.compose.foundation.layout.RowScope  NavigationBarItem +androidx.compose.foundation.layout.RowScope  OutlinedButton +androidx.compose.foundation.layout.RowScope  Spacer +androidx.compose.foundation.layout.RowScope  
StatisticCard +androidx.compose.foundation.layout.RowScope  Text +androidx.compose.foundation.layout.RowScope  	TextAlign +androidx.compose.foundation.layout.RowScope  
TextButton +androidx.compose.foundation.layout.RowScope  TransactionType +androidx.compose.foundation.layout.RowScope  Wallet +androidx.compose.foundation.layout.RowScope  any +androidx.compose.foundation.layout.RowScope  buttonColors +androidx.compose.foundation.layout.RowScope  dp +androidx.compose.foundation.layout.RowScope  forEach +androidx.compose.foundation.layout.RowScope  formatCurrency +androidx.compose.foundation.layout.RowScope  
formatDate +androidx.compose.foundation.layout.RowScope  	hierarchy +androidx.compose.foundation.layout.RowScope  
isNotBlank +androidx.compose.foundation.layout.RowScope  let +androidx.compose.foundation.layout.RowScope  size +androidx.compose.foundation.layout.RowScope  weight +androidx.compose.foundation.layout.RowScope  width +androidx.compose.foundation.layout.RowScope  LazyRow  androidx.compose.foundation.lazy  	Alignment .androidx.compose.foundation.lazy.LazyItemScope  Arrangement .androidx.compose.foundation.lazy.LazyItemScope  EmptyTransactionsCard .androidx.compose.foundation.lazy.LazyItemScope  
FilterChip .androidx.compose.foundation.lazy.LazyItemScope  
FontWeight .androidx.compose.foundation.lazy.LazyItemScope  
MaterialTheme .androidx.compose.foundation.lazy.LazyItemScope  Modifier .androidx.compose.foundation.lazy.LazyItemScope  Row .androidx.compose.foundation.lazy.LazyItemScope  Spacer .androidx.compose.foundation.lazy.LazyItemScope  
TextButton .androidx.compose.foundation.lazy.LazyItemScope  TransactionItem .androidx.compose.foundation.lazy.LazyItemScope  WalletBalanceCard .androidx.compose.foundation.lazy.LazyItemScope  dp .androidx.compose.foundation.lazy.LazyItemScope  fillMaxWidth .androidx.compose.foundation.lazy.LazyItemScope  height .androidx.compose.foundation.lazy.LazyItemScope  	Alignment .androidx.compose.foundation.lazy.LazyListScope  Arrangement .androidx.compose.foundation.lazy.LazyListScope  EmptyTransactionsCard .androidx.compose.foundation.lazy.LazyListScope  
FilterChip .androidx.compose.foundation.lazy.LazyListScope  
FontWeight .androidx.compose.foundation.lazy.LazyListScope  
MaterialTheme .androidx.compose.foundation.lazy.LazyListScope  Modifier .androidx.compose.foundation.lazy.LazyListScope  Row .androidx.compose.foundation.lazy.LazyListScope  Spacer .androidx.compose.foundation.lazy.LazyListScope  
TextButton .androidx.compose.foundation.lazy.LazyListScope  TransactionItem .androidx.compose.foundation.lazy.LazyListScope  WalletBalanceCard .androidx.compose.foundation.lazy.LazyListScope  dp .androidx.compose.foundation.lazy.LazyListScope  fillMaxWidth .androidx.compose.foundation.lazy.LazyListScope  height .androidx.compose.foundation.lazy.LazyListScope  item .androidx.compose.foundation.lazy.LazyListScope  take .androidx.compose.foundation.lazy.LazyListScope  RoundedCornerShape !androidx.compose.foundation.shape  Icons androidx.compose.material.icons  Default %androidx.compose.material.icons.Icons  Filled %androidx.compose.material.icons.Icons  Add ,androidx.compose.material.icons.Icons.Filled  	ArrowBack ,androidx.compose.material.icons.Icons.Filled  
ArrowDownward ,androidx.compose.material.icons.Icons.Filled  ArrowUpward ,androidx.compose.material.icons.Icons.Filled  Home ,androidx.compose.material.icons.Icons.Filled  Wallet ,androidx.compose.material.icons.Icons.Filled  Add &androidx.compose.material.icons.filled  	ArrowBack &androidx.compose.material.icons.filled  
ArrowDownward &androidx.compose.material.icons.filled  ArrowUpward &androidx.compose.material.icons.filled  Home &androidx.compose.material.icons.filled  Wallet &androidx.compose.material.icons.filled  AddTransactionScreen androidx.compose.material3  AddTransactionViewModel androidx.compose.material3  AlertDialog androidx.compose.material3  	Alignment androidx.compose.material3  Arrangement androidx.compose.material3  BalanceCard androidx.compose.material3  Box androidx.compose.material3  Button androidx.compose.material3  ButtonColors androidx.compose.material3  ButtonDefaults androidx.compose.material3  Card androidx.compose.material3  
CardColors androidx.compose.material3  CardDefaults androidx.compose.material3  
CardElevation androidx.compose.material3  CategorySelector androidx.compose.material3  CircularProgressIndicator androidx.compose.material3  Color androidx.compose.material3  Column androidx.compose.material3  Double androidx.compose.material3  EmptyTransactionsCard androidx.compose.material3  
FilterChip androidx.compose.material3  FloatingActionButton androidx.compose.material3  
FontWeight androidx.compose.material3  
HomeScreen androidx.compose.material3  
HomeViewModel androidx.compose.material3  Icon androidx.compose.material3  
IconButton androidx.compose.material3  Icons androidx.compose.material3  ImageVector androidx.compose.material3  LaunchedEffect androidx.compose.material3  LazyRow androidx.compose.material3  Long androidx.compose.material3  NavHostController androidx.compose.material3  
NavigationBar androidx.compose.material3  NavigationBarItem androidx.compose.material3  OutlinedButton androidx.compose.material3  OutlinedTextField androidx.compose.material3  Row androidx.compose.material3  Screen androidx.compose.material3  Spacer androidx.compose.material3  
StatisticCard androidx.compose.material3  String androidx.compose.material3  Surface androidx.compose.material3  	TextAlign androidx.compose.material3  
TextButton androidx.compose.material3  TopAppBarColors androidx.compose.material3  TopAppBarDefaults androidx.compose.material3  TransactionItem androidx.compose.material3  TransactionType androidx.compose.material3  TransactionTypeSelector androidx.compose.material3  Unit androidx.compose.material3  ValidationUtils androidx.compose.material3  WalletBalanceCard androidx.compose.material3  WalletScreen androidx.compose.material3  WalletViewModel androidx.compose.material3  any androidx.compose.material3  buttonColors androidx.compose.material3  
cardColors androidx.compose.material3  
cardElevation androidx.compose.material3  collectAsState androidx.compose.material3  fillMaxWidth androidx.compose.material3  forEach androidx.compose.material3  formatCurrency androidx.compose.material3  
formatDate androidx.compose.material3  getValue androidx.compose.material3  height androidx.compose.material3  ifBlank androidx.compose.material3  
isNotBlank androidx.compose.material3  kotlinx androidx.compose.material3  let androidx.compose.material3  listOf androidx.compose.material3  mutableStateOf androidx.compose.material3  provideDelegate androidx.compose.material3  remember androidx.compose.material3  setValue androidx.compose.material3  size androidx.compose.material3  spacedBy androidx.compose.material3  take androidx.compose.material3  toDoubleOrNull androidx.compose.material3  topAppBarColors androidx.compose.material3  updateAmount androidx.compose.material3  
updateNote androidx.compose.material3  weight androidx.compose.material3  width androidx.compose.material3  buttonColors )androidx.compose.material3.ButtonDefaults  
cardColors 'androidx.compose.material3.CardDefaults  
cardElevation 'androidx.compose.material3.CardDefaults  
background &androidx.compose.material3.ColorScheme  error &androidx.compose.material3.ColorScheme  errorContainer &androidx.compose.material3.ColorScheme  onErrorContainer &androidx.compose.material3.ColorScheme  	onPrimary &androidx.compose.material3.ColorScheme  onPrimaryContainer &androidx.compose.material3.ColorScheme  	onSurface &androidx.compose.material3.ColorScheme  onSurfaceVariant &androidx.compose.material3.ColorScheme  primary &androidx.compose.material3.ColorScheme  primaryContainer &androidx.compose.material3.ColorScheme  surface &androidx.compose.material3.ColorScheme  surfaceVariant &androidx.compose.material3.ColorScheme  colorScheme (androidx.compose.material3.MaterialTheme  
typography (androidx.compose.material3.MaterialTheme  topAppBarColors ,androidx.compose.material3.TopAppBarDefaults  
bodyMedium %androidx.compose.material3.Typography  	bodySmall %androidx.compose.material3.Typography  
headlineLarge %androidx.compose.material3.Typography  
headlineSmall %androidx.compose.material3.Typography  
titleLarge %androidx.compose.material3.Typography  titleMedium %androidx.compose.material3.Typography  AddTransactionScreen androidx.compose.runtime  AddTransactionViewModel androidx.compose.runtime  AlertDialog androidx.compose.runtime  	Alignment androidx.compose.runtime  Arrangement androidx.compose.runtime  BalanceCard androidx.compose.runtime  Box androidx.compose.runtime  Button androidx.compose.runtime  ButtonDefaults androidx.compose.runtime  Card androidx.compose.runtime  CardDefaults androidx.compose.runtime  CategorySelector androidx.compose.runtime  CircularProgressIndicator androidx.compose.runtime  Color androidx.compose.runtime  Column androidx.compose.runtime  Double androidx.compose.runtime  EmptyTransactionsCard androidx.compose.runtime  ExperimentalMaterial3Api androidx.compose.runtime  
FilterChip androidx.compose.runtime  FloatingActionButton androidx.compose.runtime  
FontWeight androidx.compose.runtime  
HomeScreen androidx.compose.runtime  
HomeViewModel androidx.compose.runtime  Icon androidx.compose.runtime  
IconButton androidx.compose.runtime  Icons androidx.compose.runtime  ImageVector androidx.compose.runtime  LaunchedEffect androidx.compose.runtime  LazyRow androidx.compose.runtime  List androidx.compose.runtime  Long androidx.compose.runtime  
MaterialTheme androidx.compose.runtime  Modifier androidx.compose.runtime  MutableState androidx.compose.runtime  NavHostController androidx.compose.runtime  
NavigationBar androidx.compose.runtime  NavigationBarItem androidx.compose.runtime  OptIn androidx.compose.runtime  OutlinedButton androidx.compose.runtime  OutlinedTextField androidx.compose.runtime  Row androidx.compose.runtime  Scaffold androidx.compose.runtime  Screen androidx.compose.runtime  Spacer androidx.compose.runtime  
StatisticCard androidx.compose.runtime  String androidx.compose.runtime  Text androidx.compose.runtime  	TextAlign androidx.compose.runtime  
TextButton androidx.compose.runtime  	TopAppBar androidx.compose.runtime  TopAppBarDefaults androidx.compose.runtime  TransactionEntity androidx.compose.runtime  TransactionItem androidx.compose.runtime  TransactionType androidx.compose.runtime  TransactionTypeSelector androidx.compose.runtime  Unit androidx.compose.runtime  ValidationUtils androidx.compose.runtime  WalletBalanceCard androidx.compose.runtime  WalletScreen androidx.compose.runtime  WalletViewModel androidx.compose.runtime  any androidx.compose.runtime  buttonColors androidx.compose.runtime  
cardColors androidx.compose.runtime  
cardElevation androidx.compose.runtime  fillMaxSize androidx.compose.runtime  fillMaxWidth androidx.compose.runtime  forEach androidx.compose.runtime  formatCurrency androidx.compose.runtime  
formatDate androidx.compose.runtime  getValue androidx.compose.runtime  height androidx.compose.runtime  ifBlank androidx.compose.runtime  
isNotBlank androidx.compose.runtime  kotlinx androidx.compose.runtime  let androidx.compose.runtime  listOf androidx.compose.runtime  mutableStateOf androidx.compose.runtime  padding androidx.compose.runtime  provideDelegate androidx.compose.runtime  remember androidx.compose.runtime  setValue androidx.compose.runtime  size androidx.compose.runtime  spacedBy androidx.compose.runtime  take androidx.compose.runtime  toDoubleOrNull androidx.compose.runtime  topAppBarColors androidx.compose.runtime  updateAmount androidx.compose.runtime  
updateNote androidx.compose.runtime  weight androidx.compose.runtime  width androidx.compose.runtime  setValue %androidx.compose.runtime.MutableState  getValue androidx.compose.runtime.State  provideDelegate androidx.compose.runtime.State  	Alignment androidx.compose.ui  Center androidx.compose.ui.Alignment  CenterHorizontally androidx.compose.ui.Alignment  CenterVertically androidx.compose.ui.Alignment  	Companion androidx.compose.ui.Alignment  
Horizontal androidx.compose.ui.Alignment  Vertical androidx.compose.ui.Alignment  Center 'androidx.compose.ui.Alignment.Companion  CenterHorizontally 'androidx.compose.ui.Alignment.Companion  CenterVertically 'androidx.compose.ui.Alignment.Companion  fillMaxWidth androidx.compose.ui.Modifier  height androidx.compose.ui.Modifier  size androidx.compose.ui.Modifier  verticalScroll androidx.compose.ui.Modifier  weight androidx.compose.ui.Modifier  width androidx.compose.ui.Modifier  fillMaxSize &androidx.compose.ui.Modifier.Companion  fillMaxWidth &androidx.compose.ui.Modifier.Companion  height &androidx.compose.ui.Modifier.Companion  size &androidx.compose.ui.Modifier.Companion  weight &androidx.compose.ui.Modifier.Companion  width &androidx.compose.ui.Modifier.Companion  copy "androidx.compose.ui.graphics.Color  ImageVector #androidx.compose.ui.graphics.vector  copy "androidx.compose.ui.text.TextStyle  Bold (androidx.compose.ui.text.font.FontWeight  Medium (androidx.compose.ui.text.font.FontWeight  SemiBold (androidx.compose.ui.text.font.FontWeight  Bold 2androidx.compose.ui.text.font.FontWeight.Companion  Medium 2androidx.compose.ui.text.font.FontWeight.Companion  SemiBold 2androidx.compose.ui.text.font.FontWeight.Companion  	TextAlign androidx.compose.ui.text.style  Center (androidx.compose.ui.text.style.TextAlign  	Companion (androidx.compose.ui.text.style.TextAlign  End (androidx.compose.ui.text.style.TextAlign  Center 2androidx.compose.ui.text.style.TextAlign.Companion  End 2androidx.compose.ui.text.style.TextAlign.Companion  Dp androidx.compose.ui.unit  getDp androidx.compose.ui.unit  
AppNavHost #androidx.core.app.ComponentActivity  Modifier #androidx.core.app.ComponentActivity  MyApplicationTheme #androidx.core.app.ComponentActivity  Surface #androidx.core.app.ComponentActivity  enableEdgeToEdge #androidx.core.app.ComponentActivity  fillMaxSize #androidx.core.app.ComponentActivity  rememberNavController #androidx.core.app.ComponentActivity  AndroidViewModel androidx.lifecycle  NavBackStackEntry androidx.navigation  NavDestination androidx.navigation  NavGraph androidx.navigation  NavGraphBuilder androidx.navigation  NavHostController androidx.navigation  NavOptionsBuilder androidx.navigation  PopUpToBuilder androidx.navigation  destination %androidx.navigation.NavBackStackEntry  graph !androidx.navigation.NavController  navigate !androidx.navigation.NavController  	hierarchy "androidx.navigation.NavDestination  route "androidx.navigation.NavDestination  	hierarchy ,androidx.navigation.NavDestination.Companion  startDestinationId androidx.navigation.NavGraph  AddTransactionScreen #androidx.navigation.NavGraphBuilder  
HomeScreen #androidx.navigation.NavGraphBuilder  Screen #androidx.navigation.NavGraphBuilder  WalletScreen #androidx.navigation.NavGraphBuilder  
composable #androidx.navigation.NavGraphBuilder  currentBackStackEntryAsState %androidx.navigation.NavHostController  graph %androidx.navigation.NavHostController  navigate %androidx.navigation.NavHostController  Screen %androidx.navigation.NavOptionsBuilder  launchSingleTop %androidx.navigation.NavOptionsBuilder  popUpTo %androidx.navigation.NavOptionsBuilder  restoreState %androidx.navigation.NavOptionsBuilder  	inclusive "androidx.navigation.PopUpToBuilder  	saveState "androidx.navigation.PopUpToBuilder  NavHost androidx.navigation.compose  
composable androidx.navigation.compose  currentBackStackEntryAsState androidx.navigation.compose  rememberNavController androidx.navigation.compose  Double 
androidx.room  Int 
androidx.room  Long 
androidx.room  Room 
androidx.room  String 
androidx.room  databaseBuilder androidx.room.Room  Builder androidx.room.RoomDatabase  build "androidx.room.RoomDatabase.Builder  fallbackToDestructiveMigration "androidx.room.RoomDatabase.Builder  
AppNavHost com.example.myapplication  Modifier com.example.myapplication  MyApplicationTheme com.example.myapplication  Surface com.example.myapplication  fillMaxSize com.example.myapplication  rememberNavController com.example.myapplication  
AppNavHost &com.example.myapplication.MainActivity  Modifier &com.example.myapplication.MainActivity  MyApplicationTheme &com.example.myapplication.MainActivity  Surface &com.example.myapplication.MainActivity  enableEdgeToEdge &com.example.myapplication.MainActivity  fillMaxSize &com.example.myapplication.MainActivity  rememberNavController &com.example.myapplication.MainActivity  Context $com.example.myapplication.data.local  DatabaseProvider $com.example.myapplication.data.local  Room $com.example.myapplication.data.local  Volatile $com.example.myapplication.data.local  databaseBuilder $com.example.myapplication.data.local  java $com.example.myapplication.data.local  synchronized $com.example.myapplication.data.local  transactionDao 0com.example.myapplication.data.local.AppDatabase  	walletDao 0com.example.myapplication.data.local.AppDatabase  AppDatabase 5com.example.myapplication.data.local.DatabaseProvider  INSTANCE 5com.example.myapplication.data.local.DatabaseProvider  Room 5com.example.myapplication.data.local.DatabaseProvider  databaseBuilder 5com.example.myapplication.data.local.DatabaseProvider  getDatabase 5com.example.myapplication.data.local.DatabaseProvider  java 5com.example.myapplication.data.local.DatabaseProvider  synchronized 5com.example.myapplication.data.local.DatabaseProvider  Double (com.example.myapplication.data.local.dao  Int (com.example.myapplication.data.local.dao  Long (com.example.myapplication.data.local.dao  String (com.example.myapplication.data.local.dao  deleteTransaction 7com.example.myapplication.data.local.dao.TransactionDao  getTotalExpenses 7com.example.myapplication.data.local.dao.TransactionDao  getTotalIncome 7com.example.myapplication.data.local.dao.TransactionDao  getTransactionsByType 7com.example.myapplication.data.local.dao.TransactionDao  getTransactionsByWallet 7com.example.myapplication.data.local.dao.TransactionDao  updateTransaction 7com.example.myapplication.data.local.dao.TransactionDao  deleteWallet 2com.example.myapplication.data.local.dao.WalletDao  
getBalance 2com.example.myapplication.data.local.dao.WalletDao  getFirstWallet 2com.example.myapplication.data.local.dao.WalletDao  getFirstWalletFlow 2com.example.myapplication.data.local.dao.WalletDao  
getWalletById 2com.example.myapplication.data.local.dao.WalletDao  
updateBalance 2com.example.myapplication.data.local.dao.WalletDao  updateWallet 2com.example.myapplication.data.local.dao.WalletDao  date =com.example.myapplication.data.local.entity.TransactionEntity  note =com.example.myapplication.data.local.entity.TransactionEntity  type =com.example.myapplication.data.local.entity.TransactionEntity  balance 8com.example.myapplication.data.local.entity.WalletEntity  id 8com.example.myapplication.data.local.entity.WalletEntity  name 8com.example.myapplication.data.local.entity.WalletEntity  Boolean )com.example.myapplication.data.repository  Context )com.example.myapplication.data.repository  DatabaseProvider )com.example.myapplication.data.repository  Double )com.example.myapplication.data.repository  Flow )com.example.myapplication.data.repository  Int )com.example.myapplication.data.repository  List )com.example.myapplication.data.repository  Long )com.example.myapplication.data.repository  RepositoryProvider )com.example.myapplication.data.repository  String )com.example.myapplication.data.repository  System )com.example.myapplication.data.repository  getDatabase )com.example.myapplication.data.repository  DatabaseProvider <com.example.myapplication.data.repository.RepositoryProvider  TransactionRepository <com.example.myapplication.data.repository.RepositoryProvider  WalletRepository <com.example.myapplication.data.repository.RepositoryProvider  getDatabase <com.example.myapplication.data.repository.RepositoryProvider  provideTransactionRepository <com.example.myapplication.data.repository.RepositoryProvider  provideWalletRepository <com.example.myapplication.data.repository.RepositoryProvider  System ?com.example.myapplication.data.repository.TransactionRepository  TransactionEntity ?com.example.myapplication.data.repository.TransactionRepository  
addExpense ?com.example.myapplication.data.repository.TransactionRepository  	addIncome ?com.example.myapplication.data.repository.TransactionRepository  getAllTransactions ?com.example.myapplication.data.repository.TransactionRepository  getTotalExpenses ?com.example.myapplication.data.repository.TransactionRepository  getTotalIncome ?com.example.myapplication.data.repository.TransactionRepository  insertTransaction ?com.example.myapplication.data.repository.TransactionRepository  WalletEntity :com.example.myapplication.data.repository.WalletRepository  addMoney :com.example.myapplication.data.repository.WalletRepository  createDefaultWalletIfNeeded :com.example.myapplication.data.repository.WalletRepository  getFirstWallet :com.example.myapplication.data.repository.WalletRepository  
getWalletById :com.example.myapplication.data.repository.WalletRepository  removeMoney :com.example.myapplication.data.repository.WalletRepository  AddTransactionScreen 'com.example.myapplication.ui.navigation  
Composable 'com.example.myapplication.ui.navigation  
HomeScreen 'com.example.myapplication.ui.navigation  Icon 'com.example.myapplication.ui.navigation  Icons 'com.example.myapplication.ui.navigation  ImageVector 'com.example.myapplication.ui.navigation  Modifier 'com.example.myapplication.ui.navigation  NavHostController 'com.example.myapplication.ui.navigation  
NavigationBar 'com.example.myapplication.ui.navigation  NavigationBarItem 'com.example.myapplication.ui.navigation  Scaffold 'com.example.myapplication.ui.navigation  Screen 'com.example.myapplication.ui.navigation  String 'com.example.myapplication.ui.navigation  Text 'com.example.myapplication.ui.navigation  WalletScreen 'com.example.myapplication.ui.navigation  any 'com.example.myapplication.ui.navigation  forEach 'com.example.myapplication.ui.navigation  getValue 'com.example.myapplication.ui.navigation  listOf 'com.example.myapplication.ui.navigation  padding 'com.example.myapplication.ui.navigation  provideDelegate 'com.example.myapplication.ui.navigation  Add .com.example.myapplication.ui.navigation.Screen  AddTransaction .com.example.myapplication.ui.navigation.Screen  Home .com.example.myapplication.ui.navigation.Screen  Icons .com.example.myapplication.ui.navigation.Screen  ImageVector .com.example.myapplication.ui.navigation.Screen  Screen .com.example.myapplication.ui.navigation.Screen  String .com.example.myapplication.ui.navigation.Screen  Wallet .com.example.myapplication.ui.navigation.Screen  icon .com.example.myapplication.ui.navigation.Screen  route .com.example.myapplication.ui.navigation.Screen  title .com.example.myapplication.ui.navigation.Screen  route =com.example.myapplication.ui.navigation.Screen.AddTransaction  route 3com.example.myapplication.ui.navigation.Screen.Home  route 5com.example.myapplication.ui.navigation.Screen.Wallet  AddTransactionUiState 3com.example.myapplication.ui.screen.add_transaction  	Alignment 3com.example.myapplication.ui.screen.add_transaction  AndroidViewModel 3com.example.myapplication.ui.screen.add_transaction  Application 3com.example.myapplication.ui.screen.add_transaction  Arrangement 3com.example.myapplication.ui.screen.add_transaction  BalanceCard 3com.example.myapplication.ui.screen.add_transaction  Boolean 3com.example.myapplication.ui.screen.add_transaction  Button 3com.example.myapplication.ui.screen.add_transaction  ButtonDefaults 3com.example.myapplication.ui.screen.add_transaction  Card 3com.example.myapplication.ui.screen.add_transaction  CardDefaults 3com.example.myapplication.ui.screen.add_transaction  CategorySelector 3com.example.myapplication.ui.screen.add_transaction  CircularProgressIndicator 3com.example.myapplication.ui.screen.add_transaction  Color 3com.example.myapplication.ui.screen.add_transaction  Column 3com.example.myapplication.ui.screen.add_transaction  
Composable 3com.example.myapplication.ui.screen.add_transaction  Double 3com.example.myapplication.ui.screen.add_transaction  ErrorHandler 3com.example.myapplication.ui.screen.add_transaction  	Exception 3com.example.myapplication.ui.screen.add_transaction  ExperimentalMaterial3Api 3com.example.myapplication.ui.screen.add_transaction  
FilterChip 3com.example.myapplication.ui.screen.add_transaction  
FontWeight 3com.example.myapplication.ui.screen.add_transaction  Icon 3com.example.myapplication.ui.screen.add_transaction  
IconButton 3com.example.myapplication.ui.screen.add_transaction  Icons 3com.example.myapplication.ui.screen.add_transaction  LaunchedEffect 3com.example.myapplication.ui.screen.add_transaction  LazyRow 3com.example.myapplication.ui.screen.add_transaction  List 3com.example.myapplication.ui.screen.add_transaction  
MaterialTheme 3com.example.myapplication.ui.screen.add_transaction  Modifier 3com.example.myapplication.ui.screen.add_transaction  MutableStateFlow 3com.example.myapplication.ui.screen.add_transaction  OptIn 3com.example.myapplication.ui.screen.add_transaction  OutlinedTextField 3com.example.myapplication.ui.screen.add_transaction  RepositoryProvider 3com.example.myapplication.ui.screen.add_transaction  Row 3com.example.myapplication.ui.screen.add_transaction  Scaffold 3com.example.myapplication.ui.screen.add_transaction  Spacer 3com.example.myapplication.ui.screen.add_transaction  	StateFlow 3com.example.myapplication.ui.screen.add_transaction  String 3com.example.myapplication.ui.screen.add_transaction  Text 3com.example.myapplication.ui.screen.add_transaction  	TopAppBar 3com.example.myapplication.ui.screen.add_transaction  TopAppBarDefaults 3com.example.myapplication.ui.screen.add_transaction  TransactionRepository 3com.example.myapplication.ui.screen.add_transaction  TransactionType 3com.example.myapplication.ui.screen.add_transaction  TransactionTypeSelector 3com.example.myapplication.ui.screen.add_transaction  Unit 3com.example.myapplication.ui.screen.add_transaction  ValidationUtils 3com.example.myapplication.ui.screen.add_transaction  WalletRepository 3com.example.myapplication.ui.screen.add_transaction  _uiState 3com.example.myapplication.ui.screen.add_transaction  asStateFlow 3com.example.myapplication.ui.screen.add_transaction  buttonColors 3com.example.myapplication.ui.screen.add_transaction  
cardColors 3com.example.myapplication.ui.screen.add_transaction  collectAsState 3com.example.myapplication.ui.screen.add_transaction  fillMaxSize 3com.example.myapplication.ui.screen.add_transaction  fillMaxWidth 3com.example.myapplication.ui.screen.add_transaction  forEach 3com.example.myapplication.ui.screen.add_transaction  formatCurrency 3com.example.myapplication.ui.screen.add_transaction  getErrorMessage 3com.example.myapplication.ui.screen.add_transaction  getValue 3com.example.myapplication.ui.screen.add_transaction  height 3com.example.myapplication.ui.screen.add_transaction  ifBlank 3com.example.myapplication.ui.screen.add_transaction  
isNotBlank 3com.example.myapplication.ui.screen.add_transaction  kotlinx 3com.example.myapplication.ui.screen.add_transaction  launch 3com.example.myapplication.ui.screen.add_transaction  let 3com.example.myapplication.ui.screen.add_transaction  listOf 3com.example.myapplication.ui.screen.add_transaction  loadWalletBalance 3com.example.myapplication.ui.screen.add_transaction  logError 3com.example.myapplication.ui.screen.add_transaction  padding 3com.example.myapplication.ui.screen.add_transaction  provideDelegate 3com.example.myapplication.ui.screen.add_transaction  provideTransactionRepository 3com.example.myapplication.ui.screen.add_transaction  provideWalletRepository 3com.example.myapplication.ui.screen.add_transaction  
sanitizeInput 3com.example.myapplication.ui.screen.add_transaction  size 3com.example.myapplication.ui.screen.add_transaction  spacedBy 3com.example.myapplication.ui.screen.add_transaction  toDouble 3com.example.myapplication.ui.screen.add_transaction  topAppBarColors 3com.example.myapplication.ui.screen.add_transaction  transactionRepository 3com.example.myapplication.ui.screen.add_transaction  updateAmount 3com.example.myapplication.ui.screen.add_transaction  
updateNote 3com.example.myapplication.ui.screen.add_transaction  validateAmount 3com.example.myapplication.ui.screen.add_transaction  validateCategory 3com.example.myapplication.ui.screen.add_transaction  validateExpenseAgainstBalance 3com.example.myapplication.ui.screen.add_transaction  validateNote 3com.example.myapplication.ui.screen.add_transaction  walletRepository 3com.example.myapplication.ui.screen.add_transaction  weight 3com.example.myapplication.ui.screen.add_transaction  width 3com.example.myapplication.ui.screen.add_transaction  amount Icom.example.myapplication.ui.screen.add_transaction.AddTransactionUiState  availableBalance Icom.example.myapplication.ui.screen.add_transaction.AddTransactionUiState  category Icom.example.myapplication.ui.screen.add_transaction.AddTransactionUiState  copy Icom.example.myapplication.ui.screen.add_transaction.AddTransactionUiState  errorMessage Icom.example.myapplication.ui.screen.add_transaction.AddTransactionUiState  	isLoading Icom.example.myapplication.ui.screen.add_transaction.AddTransactionUiState  note Icom.example.myapplication.ui.screen.add_transaction.AddTransactionUiState  selectedType Icom.example.myapplication.ui.screen.add_transaction.AddTransactionUiState  successMessage Icom.example.myapplication.ui.screen.add_transaction.AddTransactionUiState  AddTransactionUiState Kcom.example.myapplication.ui.screen.add_transaction.AddTransactionViewModel  ErrorHandler Kcom.example.myapplication.ui.screen.add_transaction.AddTransactionViewModel  	Exception Kcom.example.myapplication.ui.screen.add_transaction.AddTransactionViewModel  MutableStateFlow Kcom.example.myapplication.ui.screen.add_transaction.AddTransactionViewModel  RepositoryProvider Kcom.example.myapplication.ui.screen.add_transaction.AddTransactionViewModel  TransactionType Kcom.example.myapplication.ui.screen.add_transaction.AddTransactionViewModel  ValidationUtils Kcom.example.myapplication.ui.screen.add_transaction.AddTransactionViewModel  _uiState Kcom.example.myapplication.ui.screen.add_transaction.AddTransactionViewModel  addTransaction Kcom.example.myapplication.ui.screen.add_transaction.AddTransactionViewModel  asStateFlow Kcom.example.myapplication.ui.screen.add_transaction.AddTransactionViewModel  
clearMessages Kcom.example.myapplication.ui.screen.add_transaction.AddTransactionViewModel  expenseCategories Kcom.example.myapplication.ui.screen.add_transaction.AddTransactionViewModel  getErrorMessage Kcom.example.myapplication.ui.screen.add_transaction.AddTransactionViewModel  ifBlank Kcom.example.myapplication.ui.screen.add_transaction.AddTransactionViewModel  incomeCategories Kcom.example.myapplication.ui.screen.add_transaction.AddTransactionViewModel  invoke Kcom.example.myapplication.ui.screen.add_transaction.AddTransactionViewModel  launch Kcom.example.myapplication.ui.screen.add_transaction.AddTransactionViewModel  listOf Kcom.example.myapplication.ui.screen.add_transaction.AddTransactionViewModel  loadWalletBalance Kcom.example.myapplication.ui.screen.add_transaction.AddTransactionViewModel  logError Kcom.example.myapplication.ui.screen.add_transaction.AddTransactionViewModel  provideTransactionRepository Kcom.example.myapplication.ui.screen.add_transaction.AddTransactionViewModel  provideWalletRepository Kcom.example.myapplication.ui.screen.add_transaction.AddTransactionViewModel  
sanitizeInput Kcom.example.myapplication.ui.screen.add_transaction.AddTransactionViewModel  toDouble Kcom.example.myapplication.ui.screen.add_transaction.AddTransactionViewModel  transactionRepository Kcom.example.myapplication.ui.screen.add_transaction.AddTransactionViewModel  uiState Kcom.example.myapplication.ui.screen.add_transaction.AddTransactionViewModel  updateAmount Kcom.example.myapplication.ui.screen.add_transaction.AddTransactionViewModel  updateCategory Kcom.example.myapplication.ui.screen.add_transaction.AddTransactionViewModel  
updateNote Kcom.example.myapplication.ui.screen.add_transaction.AddTransactionViewModel  updateTransactionType Kcom.example.myapplication.ui.screen.add_transaction.AddTransactionViewModel  validateAmount Kcom.example.myapplication.ui.screen.add_transaction.AddTransactionViewModel  validateCategory Kcom.example.myapplication.ui.screen.add_transaction.AddTransactionViewModel  validateExpenseAgainstBalance Kcom.example.myapplication.ui.screen.add_transaction.AddTransactionViewModel  validateNote Kcom.example.myapplication.ui.screen.add_transaction.AddTransactionViewModel  viewModelScope Kcom.example.myapplication.ui.screen.add_transaction.AddTransactionViewModel  walletRepository Kcom.example.myapplication.ui.screen.add_transaction.AddTransactionViewModel  EXPENSE Ccom.example.myapplication.ui.screen.add_transaction.TransactionType  INCOME Ccom.example.myapplication.ui.screen.add_transaction.TransactionType  displayName Ccom.example.myapplication.ui.screen.add_transaction.TransactionType  values Ccom.example.myapplication.ui.screen.add_transaction.TransactionType  	Alignment (com.example.myapplication.ui.screen.home  AndroidViewModel (com.example.myapplication.ui.screen.home  Application (com.example.myapplication.ui.screen.home  Arrangement (com.example.myapplication.ui.screen.home  Boolean (com.example.myapplication.ui.screen.home  Box (com.example.myapplication.ui.screen.home  Button (com.example.myapplication.ui.screen.home  Card (com.example.myapplication.ui.screen.home  CardDefaults (com.example.myapplication.ui.screen.home  CircularProgressIndicator (com.example.myapplication.ui.screen.home  Column (com.example.myapplication.ui.screen.home  Double (com.example.myapplication.ui.screen.home  EmptyTransactionsCard (com.example.myapplication.ui.screen.home  	Exception (com.example.myapplication.ui.screen.home  FloatingActionButton (com.example.myapplication.ui.screen.home  
FontWeight (com.example.myapplication.ui.screen.home  HomeUiState (com.example.myapplication.ui.screen.home  Icon (com.example.myapplication.ui.screen.home  Icons (com.example.myapplication.ui.screen.home  LaunchedEffect (com.example.myapplication.ui.screen.home  Long (com.example.myapplication.ui.screen.home  
MaterialTheme (com.example.myapplication.ui.screen.home  RepositoryProvider (com.example.myapplication.ui.screen.home  Row (com.example.myapplication.ui.screen.home  Spacer (com.example.myapplication.ui.screen.home  String (com.example.myapplication.ui.screen.home  	TextAlign (com.example.myapplication.ui.screen.home  
TextButton (com.example.myapplication.ui.screen.home  TopAppBarDefaults (com.example.myapplication.ui.screen.home  TransactionItem (com.example.myapplication.ui.screen.home  TransactionRepository (com.example.myapplication.ui.screen.home  Unit (com.example.myapplication.ui.screen.home  ValidationUtils (com.example.myapplication.ui.screen.home  WalletBalanceCard (com.example.myapplication.ui.screen.home  WalletEntity (com.example.myapplication.ui.screen.home  WalletRepository (com.example.myapplication.ui.screen.home  _uiState (com.example.myapplication.ui.screen.home  
cardColors (com.example.myapplication.ui.screen.home  
cardElevation (com.example.myapplication.ui.screen.home  collectAsState (com.example.myapplication.ui.screen.home  combine (com.example.myapplication.ui.screen.home  	emptyList (com.example.myapplication.ui.screen.home  fillMaxWidth (com.example.myapplication.ui.screen.home  formatCurrency (com.example.myapplication.ui.screen.home  
formatDate (com.example.myapplication.ui.screen.home  getValue (com.example.myapplication.ui.screen.home  height (com.example.myapplication.ui.screen.home  
isNotBlank (com.example.myapplication.ui.screen.home  kotlinx (com.example.myapplication.ui.screen.home  launch (com.example.myapplication.ui.screen.home  let (com.example.myapplication.ui.screen.home  provideDelegate (com.example.myapplication.ui.screen.home  provideTransactionRepository (com.example.myapplication.ui.screen.home  provideWalletRepository (com.example.myapplication.ui.screen.home  size (com.example.myapplication.ui.screen.home  spacedBy (com.example.myapplication.ui.screen.home  take (com.example.myapplication.ui.screen.home  topAppBarColors (com.example.myapplication.ui.screen.home  transactionRepository (com.example.myapplication.ui.screen.home  walletRepository (com.example.myapplication.ui.screen.home  weight (com.example.myapplication.ui.screen.home  width (com.example.myapplication.ui.screen.home  copy 4com.example.myapplication.ui.screen.home.HomeUiState  errorMessage 4com.example.myapplication.ui.screen.home.HomeUiState  	isLoading 4com.example.myapplication.ui.screen.home.HomeUiState  transactions 4com.example.myapplication.ui.screen.home.HomeUiState  wallet 4com.example.myapplication.ui.screen.home.HomeUiState  HomeUiState 6com.example.myapplication.ui.screen.home.HomeViewModel  RepositoryProvider 6com.example.myapplication.ui.screen.home.HomeViewModel  _uiState 6com.example.myapplication.ui.screen.home.HomeViewModel  
clearError 6com.example.myapplication.ui.screen.home.HomeViewModel  combine 6com.example.myapplication.ui.screen.home.HomeViewModel  initializeData 6com.example.myapplication.ui.screen.home.HomeViewModel  invoke 6com.example.myapplication.ui.screen.home.HomeViewModel  launch 6com.example.myapplication.ui.screen.home.HomeViewModel  observeData 6com.example.myapplication.ui.screen.home.HomeViewModel  provideTransactionRepository 6com.example.myapplication.ui.screen.home.HomeViewModel  provideWalletRepository 6com.example.myapplication.ui.screen.home.HomeViewModel  transactionRepository 6com.example.myapplication.ui.screen.home.HomeViewModel  uiState 6com.example.myapplication.ui.screen.home.HomeViewModel  viewModelScope 6com.example.myapplication.ui.screen.home.HomeViewModel  walletRepository 6com.example.myapplication.ui.screen.home.HomeViewModel  AlertDialog *com.example.myapplication.ui.screen.wallet  	Alignment *com.example.myapplication.ui.screen.wallet  AndroidViewModel *com.example.myapplication.ui.screen.wallet  Application *com.example.myapplication.ui.screen.wallet  Arrangement *com.example.myapplication.ui.screen.wallet  Boolean *com.example.myapplication.ui.screen.wallet  Box *com.example.myapplication.ui.screen.wallet  Button *com.example.myapplication.ui.screen.wallet  ButtonDefaults *com.example.myapplication.ui.screen.wallet  Card *com.example.myapplication.ui.screen.wallet  CardDefaults *com.example.myapplication.ui.screen.wallet  CircularProgressIndicator *com.example.myapplication.ui.screen.wallet  Color *com.example.myapplication.ui.screen.wallet  Column *com.example.myapplication.ui.screen.wallet  
Composable *com.example.myapplication.ui.screen.wallet  Double *com.example.myapplication.ui.screen.wallet  ErrorHandler *com.example.myapplication.ui.screen.wallet  	Exception *com.example.myapplication.ui.screen.wallet  ExperimentalMaterial3Api *com.example.myapplication.ui.screen.wallet  
FontWeight *com.example.myapplication.ui.screen.wallet  Icon *com.example.myapplication.ui.screen.wallet  Icons *com.example.myapplication.ui.screen.wallet  IllegalStateException *com.example.myapplication.ui.screen.wallet  LaunchedEffect *com.example.myapplication.ui.screen.wallet  
MaterialTheme *com.example.myapplication.ui.screen.wallet  Modifier *com.example.myapplication.ui.screen.wallet  MoneyDialog *com.example.myapplication.ui.screen.wallet  MutableStateFlow *com.example.myapplication.ui.screen.wallet  OptIn *com.example.myapplication.ui.screen.wallet  OutlinedButton *com.example.myapplication.ui.screen.wallet  OutlinedTextField *com.example.myapplication.ui.screen.wallet  RepositoryProvider *com.example.myapplication.ui.screen.wallet  Row *com.example.myapplication.ui.screen.wallet  Scaffold *com.example.myapplication.ui.screen.wallet  Spacer *com.example.myapplication.ui.screen.wallet  	StateFlow *com.example.myapplication.ui.screen.wallet  
StatisticCard *com.example.myapplication.ui.screen.wallet  String *com.example.myapplication.ui.screen.wallet  Text *com.example.myapplication.ui.screen.wallet  	TextAlign *com.example.myapplication.ui.screen.wallet  
TextButton *com.example.myapplication.ui.screen.wallet  	TopAppBar *com.example.myapplication.ui.screen.wallet  TopAppBarDefaults *com.example.myapplication.ui.screen.wallet  TransactionRepository *com.example.myapplication.ui.screen.wallet  Unit *com.example.myapplication.ui.screen.wallet  ValidationUtils *com.example.myapplication.ui.screen.wallet  WalletBalanceCard *com.example.myapplication.ui.screen.wallet  WalletEntity *com.example.myapplication.ui.screen.wallet  WalletRepository *com.example.myapplication.ui.screen.wallet  
WalletUiState *com.example.myapplication.ui.screen.wallet  _uiState *com.example.myapplication.ui.screen.wallet  asStateFlow *com.example.myapplication.ui.screen.wallet  buttonColors *com.example.myapplication.ui.screen.wallet  
cardColors *com.example.myapplication.ui.screen.wallet  
cardElevation *com.example.myapplication.ui.screen.wallet  collectAsState *com.example.myapplication.ui.screen.wallet  combine *com.example.myapplication.ui.screen.wallet  fillMaxSize *com.example.myapplication.ui.screen.wallet  fillMaxWidth *com.example.myapplication.ui.screen.wallet  formatCurrency *com.example.myapplication.ui.screen.wallet  getErrorMessage *com.example.myapplication.ui.screen.wallet  getValue *com.example.myapplication.ui.screen.wallet  height *com.example.myapplication.ui.screen.wallet  hideAddMoneyDialog *com.example.myapplication.ui.screen.wallet  hideRemoveMoneyDialog *com.example.myapplication.ui.screen.wallet  ifBlank *com.example.myapplication.ui.screen.wallet  kotlinx *com.example.myapplication.ui.screen.wallet  launch *com.example.myapplication.ui.screen.wallet  let *com.example.myapplication.ui.screen.wallet  logError *com.example.myapplication.ui.screen.wallet  mutableStateOf *com.example.myapplication.ui.screen.wallet  padding *com.example.myapplication.ui.screen.wallet  provideDelegate *com.example.myapplication.ui.screen.wallet  provideTransactionRepository *com.example.myapplication.ui.screen.wallet  provideWalletRepository *com.example.myapplication.ui.screen.wallet  remember *com.example.myapplication.ui.screen.wallet  
sanitizeInput *com.example.myapplication.ui.screen.wallet  setValue *com.example.myapplication.ui.screen.wallet  size *com.example.myapplication.ui.screen.wallet  spacedBy *com.example.myapplication.ui.screen.wallet  toDoubleOrNull *com.example.myapplication.ui.screen.wallet  topAppBarColors *com.example.myapplication.ui.screen.wallet  transactionRepository *com.example.myapplication.ui.screen.wallet  validateAmount *com.example.myapplication.ui.screen.wallet  validateExpenseAgainstBalance *com.example.myapplication.ui.screen.wallet  validateNote *com.example.myapplication.ui.screen.wallet  walletRepository *com.example.myapplication.ui.screen.wallet  weight *com.example.myapplication.ui.screen.wallet  width *com.example.myapplication.ui.screen.wallet  copy 8com.example.myapplication.ui.screen.wallet.WalletUiState  errorMessage 8com.example.myapplication.ui.screen.wallet.WalletUiState  	isLoading 8com.example.myapplication.ui.screen.wallet.WalletUiState  showAddMoneyDialog 8com.example.myapplication.ui.screen.wallet.WalletUiState  showRemoveMoneyDialog 8com.example.myapplication.ui.screen.wallet.WalletUiState  
totalExpenses 8com.example.myapplication.ui.screen.wallet.WalletUiState  totalIncome 8com.example.myapplication.ui.screen.wallet.WalletUiState  wallet 8com.example.myapplication.ui.screen.wallet.WalletUiState  ErrorHandler :com.example.myapplication.ui.screen.wallet.WalletViewModel  IllegalStateException :com.example.myapplication.ui.screen.wallet.WalletViewModel  MutableStateFlow :com.example.myapplication.ui.screen.wallet.WalletViewModel  RepositoryProvider :com.example.myapplication.ui.screen.wallet.WalletViewModel  ValidationUtils :com.example.myapplication.ui.screen.wallet.WalletViewModel  
WalletUiState :com.example.myapplication.ui.screen.wallet.WalletViewModel  _uiState :com.example.myapplication.ui.screen.wallet.WalletViewModel  addMoney :com.example.myapplication.ui.screen.wallet.WalletViewModel  asStateFlow :com.example.myapplication.ui.screen.wallet.WalletViewModel  
clearError :com.example.myapplication.ui.screen.wallet.WalletViewModel  combine :com.example.myapplication.ui.screen.wallet.WalletViewModel  getErrorMessage :com.example.myapplication.ui.screen.wallet.WalletViewModel  hideAddMoneyDialog :com.example.myapplication.ui.screen.wallet.WalletViewModel  hideRemoveMoneyDialog :com.example.myapplication.ui.screen.wallet.WalletViewModel  ifBlank :com.example.myapplication.ui.screen.wallet.WalletViewModel  initializeWallet :com.example.myapplication.ui.screen.wallet.WalletViewModel  invoke :com.example.myapplication.ui.screen.wallet.WalletViewModel  launch :com.example.myapplication.ui.screen.wallet.WalletViewModel  logError :com.example.myapplication.ui.screen.wallet.WalletViewModel  observeWalletData :com.example.myapplication.ui.screen.wallet.WalletViewModel  provideTransactionRepository :com.example.myapplication.ui.screen.wallet.WalletViewModel  provideWalletRepository :com.example.myapplication.ui.screen.wallet.WalletViewModel  removeMoney :com.example.myapplication.ui.screen.wallet.WalletViewModel  
sanitizeInput :com.example.myapplication.ui.screen.wallet.WalletViewModel  showAddMoneyDialog :com.example.myapplication.ui.screen.wallet.WalletViewModel  showRemoveMoneyDialog :com.example.myapplication.ui.screen.wallet.WalletViewModel  transactionRepository :com.example.myapplication.ui.screen.wallet.WalletViewModel  uiState :com.example.myapplication.ui.screen.wallet.WalletViewModel  validateAmount :com.example.myapplication.ui.screen.wallet.WalletViewModel  validateExpenseAgainstBalance :com.example.myapplication.ui.screen.wallet.WalletViewModel  validateNote :com.example.myapplication.ui.screen.wallet.WalletViewModel  viewModelScope :com.example.myapplication.ui.screen.wallet.WalletViewModel  walletRepository :com.example.myapplication.ui.screen.wallet.WalletViewModel  
Background "com.example.myapplication.ui.theme  Error "com.example.myapplication.ui.theme  Info "com.example.myapplication.ui.theme  OnBackground "com.example.myapplication.ui.theme  	OnPrimary "com.example.myapplication.ui.theme  OnSecondary "com.example.myapplication.ui.theme  	OnSurface "com.example.myapplication.ui.theme  OnSurfaceVariant "com.example.myapplication.ui.theme  Primary "com.example.myapplication.ui.theme  PrimaryVariant "com.example.myapplication.ui.theme  	Secondary "com.example.myapplication.ui.theme  SecondaryVariant "com.example.myapplication.ui.theme  Success "com.example.myapplication.ui.theme  Surface "com.example.myapplication.ui.theme  SurfaceVariant "com.example.myapplication.ui.theme  Warning "com.example.myapplication.ui.theme  AppError com.example.myapplication.utils  Boolean com.example.myapplication.utils  Double com.example.myapplication.utils  ErrorHandler com.example.myapplication.utils  	Exception com.example.myapplication.utils  IOException com.example.myapplication.utils  IllegalArgumentException com.example.myapplication.utils  IllegalStateException com.example.myapplication.utils  Long com.example.myapplication.utils  NumberFormatException com.example.myapplication.utils  Regex com.example.myapplication.utils  SQLiteException com.example.myapplication.utils  SocketTimeoutException com.example.myapplication.utils  String com.example.myapplication.utils  	Throwable com.example.myapplication.utils  UnknownHostException com.example.myapplication.utils  ValidationResult com.example.myapplication.utils  ValidationUtils com.example.myapplication.utils  android com.example.myapplication.utils  format com.example.myapplication.utils  isBlank com.example.myapplication.utils  java com.example.myapplication.utils  replace com.example.myapplication.utils  toDoubleOrNull com.example.myapplication.utils  trim com.example.myapplication.utils  AppError ,com.example.myapplication.utils.ErrorHandler  IOException ,com.example.myapplication.utils.ErrorHandler  IllegalArgumentException ,com.example.myapplication.utils.ErrorHandler  IllegalStateException ,com.example.myapplication.utils.ErrorHandler  NumberFormatException ,com.example.myapplication.utils.ErrorHandler  SQLiteException ,com.example.myapplication.utils.ErrorHandler  SocketTimeoutException ,com.example.myapplication.utils.ErrorHandler  String ,com.example.myapplication.utils.ErrorHandler  	Throwable ,com.example.myapplication.utils.ErrorHandler  UnknownHostException ,com.example.myapplication.utils.ErrorHandler  android ,com.example.myapplication.utils.ErrorHandler  getErrorMessage ,com.example.myapplication.utils.ErrorHandler  logError ,com.example.myapplication.utils.ErrorHandler  AppError 5com.example.myapplication.utils.ErrorHandler.AppError  String 5com.example.myapplication.utils.ErrorHandler.AppError  Boolean /com.example.myapplication.utils.ValidationUtils  Double /com.example.myapplication.utils.ValidationUtils  	Exception /com.example.myapplication.utils.ValidationUtils  Long /com.example.myapplication.utils.ValidationUtils  Regex /com.example.myapplication.utils.ValidationUtils  String /com.example.myapplication.utils.ValidationUtils  ValidationResult /com.example.myapplication.utils.ValidationUtils  format /com.example.myapplication.utils.ValidationUtils  formatCurrency /com.example.myapplication.utils.ValidationUtils  
formatDate /com.example.myapplication.utils.ValidationUtils  isBlank /com.example.myapplication.utils.ValidationUtils  java /com.example.myapplication.utils.ValidationUtils  replace /com.example.myapplication.utils.ValidationUtils  
sanitizeInput /com.example.myapplication.utils.ValidationUtils  toDoubleOrNull /com.example.myapplication.utils.ValidationUtils  trim /com.example.myapplication.utils.ValidationUtils  validateAmount /com.example.myapplication.utils.ValidationUtils  validateCategory /com.example.myapplication.utils.ValidationUtils  validateExpenseAgainstBalance /com.example.myapplication.utils.ValidationUtils  validateNote /com.example.myapplication.utils.ValidationUtils  errorMessage @com.example.myapplication.utils.ValidationUtils.ValidationResult  isValid @com.example.myapplication.utils.ValidationUtils.ValidationResult  IOException java.io  Class 	java.lang  	Exception 	java.lang  IllegalArgumentException 	java.lang  IllegalStateException 	java.lang  NumberFormatException 	java.lang  message java.lang.Exception  message "java.lang.IllegalArgumentException  message java.lang.IllegalStateException  SocketTimeoutException java.net  UnknownHostException java.net  SimpleDateFormat 	java.text  format java.text.DateFormat  format java.text.Format  format java.text.NumberFormat  getCurrencyInstance java.text.NumberFormat  format java.text.SimpleDateFormat  Date 	java.util  Locale 	java.util  CharSequence kotlin  	Function0 kotlin  	Function2 kotlin  Nothing kotlin  String kotlin  	Throwable kotlin  let kotlin  synchronized kotlin  forEach kotlin.Array  not kotlin.Boolean  	compareTo 
kotlin.Double  let 
kotlin.Double  toString 
kotlin.Double  
unaryMinus 
kotlin.Double  String kotlin.Enum  invoke kotlin.Function1  invoke kotlin.Function2  	Companion 
kotlin.String  format 
kotlin.String  ifBlank 
kotlin.String  isBlank 
kotlin.String  
isNotBlank 
kotlin.String  length 
kotlin.String  let 
kotlin.String  replace 
kotlin.String  toDouble 
kotlin.String  toDoubleOrNull 
kotlin.String  trim 
kotlin.String  format kotlin.String.Companion  message kotlin.Throwable  any kotlin.collections  	emptyList kotlin.collections  forEach kotlin.collections  take kotlin.collections  contains kotlin.collections.List  isEmpty kotlin.collections.List  take kotlin.collections.List  SuspendFunction1 kotlin.coroutines  SuspendFunction2 kotlin.coroutines  Volatile 
kotlin.jvm  java 
kotlin.jvm  
KFunction0 kotlin.reflect  
KFunction1 kotlin.reflect  KMutableProperty0 kotlin.reflect  
KProperty0 kotlin.reflect  java kotlin.reflect.KClass  Sequence kotlin.sequences  any kotlin.sequences  forEach kotlin.sequences  take kotlin.sequences  any kotlin.sequences.Sequence  Regex kotlin.text  any kotlin.text  forEach kotlin.text  format kotlin.text  ifBlank kotlin.text  isBlank kotlin.text  
isNotBlank kotlin.text  replace kotlin.text  take kotlin.text  toDouble kotlin.text  toDoubleOrNull kotlin.text  trim kotlin.text  CoroutineScope kotlinx.coroutines  Job kotlinx.coroutines  delay kotlinx.coroutines  launch kotlinx.coroutines  ErrorHandler !kotlinx.coroutines.CoroutineScope  	Exception !kotlinx.coroutines.CoroutineScope  IllegalStateException !kotlinx.coroutines.CoroutineScope  TransactionType !kotlinx.coroutines.CoroutineScope  ValidationUtils !kotlinx.coroutines.CoroutineScope  _uiState !kotlinx.coroutines.CoroutineScope  combine !kotlinx.coroutines.CoroutineScope  getErrorMessage !kotlinx.coroutines.CoroutineScope  hideAddMoneyDialog !kotlinx.coroutines.CoroutineScope  hideRemoveMoneyDialog !kotlinx.coroutines.CoroutineScope  ifBlank !kotlinx.coroutines.CoroutineScope  kotlinx !kotlinx.coroutines.CoroutineScope  launch !kotlinx.coroutines.CoroutineScope  loadWalletBalance !kotlinx.coroutines.CoroutineScope  logError !kotlinx.coroutines.CoroutineScope  
sanitizeInput !kotlinx.coroutines.CoroutineScope  toDouble !kotlinx.coroutines.CoroutineScope  transactionRepository !kotlinx.coroutines.CoroutineScope  validateAmount !kotlinx.coroutines.CoroutineScope  validateCategory !kotlinx.coroutines.CoroutineScope  validateExpenseAgainstBalance !kotlinx.coroutines.CoroutineScope  validateNote !kotlinx.coroutines.CoroutineScope  walletRepository !kotlinx.coroutines.CoroutineScope  
FlowCollector kotlinx.coroutines.flow  combine kotlinx.coroutines.flow  collect kotlinx.coroutines.flow.Flow  <SAM-CONSTRUCTOR> %kotlinx.coroutines.flow.FlowCollector  value (kotlinx.coroutines.flow.MutableStateFlow  getSharedPreferences android.app.Application  MODE_PRIVATE android.content.Context  getSharedPreferences android.content.ContextWrapper  edit !android.content.SharedPreferences  
getBoolean !android.content.SharedPreferences  	getString !android.content.SharedPreferences  apply (android.content.SharedPreferences.Editor  
putBoolean (android.content.SharedPreferences.Editor  	putString (android.content.SharedPreferences.Editor  
parseColor android.graphics.Color  CategoryManagementScreen /androidx.compose.animation.AnimatedContentScope  SettingsScreen /androidx.compose.animation.AnimatedContentScope  WalletManagementScreen /androidx.compose.animation.AnimatedContentScope  	Alignment androidx.compose.animation.core  Box androidx.compose.animation.core  Brush androidx.compose.animation.core  CardDefaults androidx.compose.animation.core  Color androidx.compose.animation.core  Column androidx.compose.animation.core  
Composable androidx.compose.animation.core  InfiniteRepeatableSpec androidx.compose.animation.core  InfiniteTransition androidx.compose.animation.core  
MaterialTheme androidx.compose.animation.core  Modifier androidx.compose.animation.core  Offset androidx.compose.animation.core  Row androidx.compose.animation.core  Spacer androidx.compose.animation.core  	TweenSpec androidx.compose.animation.core  androidx androidx.compose.animation.core  animateFloat androidx.compose.animation.core  
cardColors androidx.compose.animation.core  
cardElevation androidx.compose.animation.core  fillMaxWidth androidx.compose.animation.core  getValue androidx.compose.animation.core  height androidx.compose.animation.core  infiniteRepeatable androidx.compose.animation.core  linearGradient androidx.compose.animation.core  listOf androidx.compose.animation.core  mutableStateOf androidx.compose.animation.core  onGloballyPositioned androidx.compose.animation.core  padding androidx.compose.animation.core  provideDelegate androidx.compose.animation.core  remember androidx.compose.animation.core  rememberInfiniteTransition androidx.compose.animation.core  setValue androidx.compose.animation.core  
shimmerEffect androidx.compose.animation.core  size androidx.compose.animation.core  tween androidx.compose.animation.core  weight androidx.compose.animation.core  width androidx.compose.animation.core  animateFloat 2androidx.compose.animation.core.InfiniteTransition  ExperimentalFoundationApi androidx.compose.foundation  
background androidx.compose.foundation  	clickable androidx.compose.foundation  combinedClickable androidx.compose.foundation  
AccountCircle "androidx.compose.foundation.layout  Add "androidx.compose.foundation.layout  AppRegistration "androidx.compose.foundation.layout  	ArrowBack "androidx.compose.foundation.layout  AttachMoney "androidx.compose.foundation.layout  Boolean "androidx.compose.foundation.layout  Brush "androidx.compose.foundation.layout  Category "androidx.compose.foundation.layout  CategoryEntity "androidx.compose.foundation.layout  CategoryItem "androidx.compose.foundation.layout  CategoryManagementViewModel "androidx.compose.foundation.layout  CategorySelectionCard "androidx.compose.foundation.layout  CategoryType "androidx.compose.foundation.layout  Check "androidx.compose.foundation.layout  Checkbox "androidx.compose.foundation.layout  CircleShape "androidx.compose.foundation.layout  CreateCategoryFormState "androidx.compose.foundation.layout  CreateWalletFormState "androidx.compose.foundation.layout  DarkMode "androidx.compose.foundation.layout  Delete "androidx.compose.foundation.layout  
DeleteForever "androidx.compose.foundation.layout  Description "androidx.compose.foundation.layout  Edit "androidx.compose.foundation.layout  ExperimentalFoundationApi "androidx.compose.foundation.layout  FileDownload "androidx.compose.foundation.layout  HapticFeedbackType "androidx.compose.foundation.layout  ImageVector "androidx.compose.foundation.layout  Info "androidx.compose.foundation.layout  Int "androidx.compose.foundation.layout  
LazyColumn "androidx.compose.foundation.layout  Lock "androidx.compose.foundation.layout  LockOpen "androidx.compose.foundation.layout  
Notifications "androidx.compose.foundation.layout  Offset "androidx.compose.foundation.layout  
PrivacyTip "androidx.compose.foundation.layout  RoundedCornerShape "androidx.compose.foundation.layout  Settings "androidx.compose.foundation.layout  SettingsItem "androidx.compose.foundation.layout  SettingsSection "androidx.compose.foundation.layout  SettingsViewModel "androidx.compose.foundation.layout  ShimmerTransactionItem "androidx.compose.foundation.layout  ShimmerWalletCard "androidx.compose.foundation.layout  SnackbarDuration "androidx.compose.foundation.layout  SnackbarHost "androidx.compose.foundation.layout  SnackbarHostState "androidx.compose.foundation.layout  
StatisticItem "androidx.compose.foundation.layout  StatisticsSection "androidx.compose.foundation.layout  Storage "androidx.compose.foundation.layout  SwipeToDeleteBox "androidx.compose.foundation.layout  Switch "androidx.compose.foundation.layout  Tab "androidx.compose.foundation.layout  TabRow "androidx.compose.foundation.layout  Wallet "androidx.compose.foundation.layout  WalletEntity "androidx.compose.foundation.layout  
WalletItem "androidx.compose.foundation.layout  WalletManagementViewModel "androidx.compose.foundation.layout  WalletSelectionCard "androidx.compose.foundation.layout  android "androidx.compose.foundation.layout  androidx "androidx.compose.foundation.layout  animateFloat "androidx.compose.foundation.layout  
background "androidx.compose.foundation.layout  	clickable "androidx.compose.foundation.layout  clip "androidx.compose.foundation.layout  com "androidx.compose.foundation.layout  forEachIndexed "androidx.compose.foundation.layout  infiniteRepeatable "androidx.compose.foundation.layout  
isNotEmpty "androidx.compose.foundation.layout  linearGradient "androidx.compose.foundation.layout  listOf "androidx.compose.foundation.layout  onGloballyPositioned "androidx.compose.foundation.layout  rememberInfiniteTransition "androidx.compose.foundation.layout  
shimmerEffect "androidx.compose.foundation.layout  tween "androidx.compose.foundation.layout  SpaceEvenly .androidx.compose.foundation.layout.Arrangement  Category +androidx.compose.foundation.layout.BoxScope  Check +androidx.compose.foundation.layout.BoxScope  Color +androidx.compose.foundation.layout.BoxScope  Icon +androidx.compose.foundation.layout.BoxScope  Icons +androidx.compose.foundation.layout.BoxScope  Modifier +androidx.compose.foundation.layout.BoxScope  Wallet +androidx.compose.foundation.layout.BoxScope  dp +androidx.compose.foundation.layout.BoxScope  size +androidx.compose.foundation.layout.BoxScope  Category .androidx.compose.foundation.layout.ColumnScope  CategoryItem .androidx.compose.foundation.layout.ColumnScope  CategorySelectionCard .androidx.compose.foundation.layout.ColumnScope  CategoryType .androidx.compose.foundation.layout.ColumnScope  Check .androidx.compose.foundation.layout.ColumnScope  Checkbox .androidx.compose.foundation.layout.ColumnScope  CircleShape .androidx.compose.foundation.layout.ColumnScope  Delete .androidx.compose.foundation.layout.ColumnScope  Edit .androidx.compose.foundation.layout.ColumnScope  
IconButton .androidx.compose.foundation.layout.ColumnScope  KeyboardArrowDown .androidx.compose.foundation.layout.ColumnScope  
LazyColumn .androidx.compose.foundation.layout.ColumnScope  Lock .androidx.compose.foundation.layout.ColumnScope  LockOpen .androidx.compose.foundation.layout.ColumnScope  RoundedCornerShape .androidx.compose.foundation.layout.ColumnScope  
StatisticItem .androidx.compose.foundation.layout.ColumnScope  StatisticsSection .androidx.compose.foundation.layout.ColumnScope  Tab .androidx.compose.foundation.layout.ColumnScope  TabRow .androidx.compose.foundation.layout.ColumnScope  ValidationUtils .androidx.compose.foundation.layout.ColumnScope  
WalletItem .androidx.compose.foundation.layout.ColumnScope  WalletSelectionCard .androidx.compose.foundation.layout.ColumnScope  android .androidx.compose.foundation.layout.ColumnScope  
background .androidx.compose.foundation.layout.ColumnScope  	clickable .androidx.compose.foundation.layout.ColumnScope  clip .androidx.compose.foundation.layout.ColumnScope  forEachIndexed .androidx.compose.foundation.layout.ColumnScope  
isNotEmpty .androidx.compose.foundation.layout.ColumnScope  
shimmerEffect .androidx.compose.foundation.layout.ColumnScope  	Alignment +androidx.compose.foundation.layout.RowScope  Arrangement +androidx.compose.foundation.layout.RowScope  Box +androidx.compose.foundation.layout.RowScope  Card +androidx.compose.foundation.layout.RowScope  CardDefaults +androidx.compose.foundation.layout.RowScope  Category +androidx.compose.foundation.layout.RowScope  Check +androidx.compose.foundation.layout.RowScope  Checkbox +androidx.compose.foundation.layout.RowScope  CircleShape +androidx.compose.foundation.layout.RowScope  Color +androidx.compose.foundation.layout.RowScope  Delete +androidx.compose.foundation.layout.RowScope  Edit +androidx.compose.foundation.layout.RowScope  
IconButton +androidx.compose.foundation.layout.RowScope  KeyboardArrowDown +androidx.compose.foundation.layout.RowScope  Lock +androidx.compose.foundation.layout.RowScope  LockOpen +androidx.compose.foundation.layout.RowScope  Refresh +androidx.compose.foundation.layout.RowScope  RoundedCornerShape +androidx.compose.foundation.layout.RowScope  Row +androidx.compose.foundation.layout.RowScope  Settings +androidx.compose.foundation.layout.RowScope  
StatisticItem +androidx.compose.foundation.layout.RowScope  ValidationUtils +androidx.compose.foundation.layout.RowScope  android +androidx.compose.foundation.layout.RowScope  
background +androidx.compose.foundation.layout.RowScope  
cardColors +androidx.compose.foundation.layout.RowScope  clip +androidx.compose.foundation.layout.RowScope  fillMaxWidth +androidx.compose.foundation.layout.RowScope  height +androidx.compose.foundation.layout.RowScope  padding +androidx.compose.foundation.layout.RowScope  
shimmerEffect +androidx.compose.foundation.layout.RowScope  spacedBy +androidx.compose.foundation.layout.RowScope  example &androidx.compose.foundation.layout.com  
myapplication .androidx.compose.foundation.layout.com.example  data <androidx.compose.foundation.layout.com.example.myapplication  local Aandroidx.compose.foundation.layout.com.example.myapplication.data  entity Gandroidx.compose.foundation.layout.com.example.myapplication.data.local  CategoryEntity Nandroidx.compose.foundation.layout.com.example.myapplication.data.local.entity  WalletEntity Nandroidx.compose.foundation.layout.com.example.myapplication.data.local.entity  
AccountCircle .androidx.compose.foundation.lazy.LazyItemScope  AppRegistration .androidx.compose.foundation.lazy.LazyItemScope  AttachMoney .androidx.compose.foundation.lazy.LazyItemScope  Box .androidx.compose.foundation.lazy.LazyItemScope  Card .androidx.compose.foundation.lazy.LazyItemScope  CardDefaults .androidx.compose.foundation.lazy.LazyItemScope  Category .androidx.compose.foundation.lazy.LazyItemScope  CategoryItem .androidx.compose.foundation.lazy.LazyItemScope  Check .androidx.compose.foundation.lazy.LazyItemScope  CircleShape .androidx.compose.foundation.lazy.LazyItemScope  Color .androidx.compose.foundation.lazy.LazyItemScope  Column .androidx.compose.foundation.lazy.LazyItemScope  DarkMode .androidx.compose.foundation.lazy.LazyItemScope  
DeleteForever .androidx.compose.foundation.lazy.LazyItemScope  Description .androidx.compose.foundation.lazy.LazyItemScope  FileDownload .androidx.compose.foundation.lazy.LazyItemScope  Icon .androidx.compose.foundation.lazy.LazyItemScope  Icons .androidx.compose.foundation.lazy.LazyItemScope  Info .androidx.compose.foundation.lazy.LazyItemScope  
Notifications .androidx.compose.foundation.lazy.LazyItemScope  
PrivacyTip .androidx.compose.foundation.lazy.LazyItemScope  Settings .androidx.compose.foundation.lazy.LazyItemScope  SettingsItem .androidx.compose.foundation.lazy.LazyItemScope  SettingsSection .androidx.compose.foundation.lazy.LazyItemScope  ShimmerTransactionItem .androidx.compose.foundation.lazy.LazyItemScope  ShimmerWalletCard .androidx.compose.foundation.lazy.LazyItemScope  StatisticsSection .androidx.compose.foundation.lazy.LazyItemScope  Storage .androidx.compose.foundation.lazy.LazyItemScope  SwipeToDeleteBox .androidx.compose.foundation.lazy.LazyItemScope  Switch .androidx.compose.foundation.lazy.LazyItemScope  Wallet .androidx.compose.foundation.lazy.LazyItemScope  
WalletItem .androidx.compose.foundation.lazy.LazyItemScope  android .androidx.compose.foundation.lazy.LazyItemScope  
background .androidx.compose.foundation.lazy.LazyItemScope  
cardColors .androidx.compose.foundation.lazy.LazyItemScope  	clickable .androidx.compose.foundation.lazy.LazyItemScope  clip .androidx.compose.foundation.lazy.LazyItemScope  formatCurrency .androidx.compose.foundation.lazy.LazyItemScope  
isNotEmpty .androidx.compose.foundation.lazy.LazyItemScope  let .androidx.compose.foundation.lazy.LazyItemScope  padding .androidx.compose.foundation.lazy.LazyItemScope  size .androidx.compose.foundation.lazy.LazyItemScope  weight .androidx.compose.foundation.lazy.LazyItemScope  width .androidx.compose.foundation.lazy.LazyItemScope  
AccountCircle .androidx.compose.foundation.lazy.LazyListScope  AppRegistration .androidx.compose.foundation.lazy.LazyListScope  AttachMoney .androidx.compose.foundation.lazy.LazyListScope  Box .androidx.compose.foundation.lazy.LazyListScope  Card .androidx.compose.foundation.lazy.LazyListScope  CardDefaults .androidx.compose.foundation.lazy.LazyListScope  Category .androidx.compose.foundation.lazy.LazyListScope  CategoryItem .androidx.compose.foundation.lazy.LazyListScope  Check .androidx.compose.foundation.lazy.LazyListScope  CircleShape .androidx.compose.foundation.lazy.LazyListScope  Color .androidx.compose.foundation.lazy.LazyListScope  Column .androidx.compose.foundation.lazy.LazyListScope  DarkMode .androidx.compose.foundation.lazy.LazyListScope  
DeleteForever .androidx.compose.foundation.lazy.LazyListScope  Description .androidx.compose.foundation.lazy.LazyListScope  FileDownload .androidx.compose.foundation.lazy.LazyListScope  Icon .androidx.compose.foundation.lazy.LazyListScope  Icons .androidx.compose.foundation.lazy.LazyListScope  Info .androidx.compose.foundation.lazy.LazyListScope  
Notifications .androidx.compose.foundation.lazy.LazyListScope  
PrivacyTip .androidx.compose.foundation.lazy.LazyListScope  Settings .androidx.compose.foundation.lazy.LazyListScope  SettingsItem .androidx.compose.foundation.lazy.LazyListScope  SettingsSection .androidx.compose.foundation.lazy.LazyListScope  ShimmerTransactionItem .androidx.compose.foundation.lazy.LazyListScope  ShimmerWalletCard .androidx.compose.foundation.lazy.LazyListScope  StatisticsSection .androidx.compose.foundation.lazy.LazyListScope  Storage .androidx.compose.foundation.lazy.LazyListScope  SwipeToDeleteBox .androidx.compose.foundation.lazy.LazyListScope  Switch .androidx.compose.foundation.lazy.LazyListScope  Wallet .androidx.compose.foundation.lazy.LazyListScope  
WalletItem .androidx.compose.foundation.lazy.LazyListScope  android .androidx.compose.foundation.lazy.LazyListScope  
background .androidx.compose.foundation.lazy.LazyListScope  
cardColors .androidx.compose.foundation.lazy.LazyListScope  	clickable .androidx.compose.foundation.lazy.LazyListScope  clip .androidx.compose.foundation.lazy.LazyListScope  formatCurrency .androidx.compose.foundation.lazy.LazyListScope  
isNotEmpty .androidx.compose.foundation.lazy.LazyListScope  let .androidx.compose.foundation.lazy.LazyListScope  padding .androidx.compose.foundation.lazy.LazyListScope  size .androidx.compose.foundation.lazy.LazyListScope  weight .androidx.compose.foundation.lazy.LazyListScope  width .androidx.compose.foundation.lazy.LazyListScope  CircleShape !androidx.compose.foundation.shape  
AccountCircle ,androidx.compose.material.icons.Icons.Filled  AppRegistration ,androidx.compose.material.icons.Icons.Filled  AttachMoney ,androidx.compose.material.icons.Icons.Filled  Category ,androidx.compose.material.icons.Icons.Filled  Check ,androidx.compose.material.icons.Icons.Filled  DarkMode ,androidx.compose.material.icons.Icons.Filled  Delete ,androidx.compose.material.icons.Icons.Filled  
DeleteForever ,androidx.compose.material.icons.Icons.Filled  Description ,androidx.compose.material.icons.Icons.Filled  Edit ,androidx.compose.material.icons.Icons.Filled  FileDownload ,androidx.compose.material.icons.Icons.Filled  Info ,androidx.compose.material.icons.Icons.Filled  KeyboardArrowDown ,androidx.compose.material.icons.Icons.Filled  Lock ,androidx.compose.material.icons.Icons.Filled  LockOpen ,androidx.compose.material.icons.Icons.Filled  
Notifications ,androidx.compose.material.icons.Icons.Filled  
PrivacyTip ,androidx.compose.material.icons.Icons.Filled  Refresh ,androidx.compose.material.icons.Icons.Filled  Settings ,androidx.compose.material.icons.Icons.Filled  Storage ,androidx.compose.material.icons.Icons.Filled  
AccountCircle &androidx.compose.material.icons.filled  AlertDialog &androidx.compose.material.icons.filled  	Alignment &androidx.compose.material.icons.filled  AppRegistration &androidx.compose.material.icons.filled  Arrangement &androidx.compose.material.icons.filled  AttachMoney &androidx.compose.material.icons.filled  Boolean &androidx.compose.material.icons.filled  Box &androidx.compose.material.icons.filled  Button &androidx.compose.material.icons.filled  ButtonDefaults &androidx.compose.material.icons.filled  Card &androidx.compose.material.icons.filled  CardDefaults &androidx.compose.material.icons.filled  Category &androidx.compose.material.icons.filled  CategoryEntity &androidx.compose.material.icons.filled  CategoryItem &androidx.compose.material.icons.filled  CategoryManagementViewModel &androidx.compose.material.icons.filled  CategoryType &androidx.compose.material.icons.filled  Check &androidx.compose.material.icons.filled  Checkbox &androidx.compose.material.icons.filled  CircleShape &androidx.compose.material.icons.filled  CircularProgressIndicator &androidx.compose.material.icons.filled  Color &androidx.compose.material.icons.filled  Column &androidx.compose.material.icons.filled  
Composable &androidx.compose.material.icons.filled  CreateCategoryFormState &androidx.compose.material.icons.filled  CreateWalletFormState &androidx.compose.material.icons.filled  DarkMode &androidx.compose.material.icons.filled  Delete &androidx.compose.material.icons.filled  
DeleteForever &androidx.compose.material.icons.filled  Description &androidx.compose.material.icons.filled  Double &androidx.compose.material.icons.filled  Edit &androidx.compose.material.icons.filled  ExperimentalMaterial3Api &androidx.compose.material.icons.filled  FileDownload &androidx.compose.material.icons.filled  FloatingActionButton &androidx.compose.material.icons.filled  
FontWeight &androidx.compose.material.icons.filled  Icon &androidx.compose.material.icons.filled  
IconButton &androidx.compose.material.icons.filled  Icons &androidx.compose.material.icons.filled  ImageVector &androidx.compose.material.icons.filled  Info &androidx.compose.material.icons.filled  Int &androidx.compose.material.icons.filled  KeyboardArrowDown &androidx.compose.material.icons.filled  LaunchedEffect &androidx.compose.material.icons.filled  
LazyColumn &androidx.compose.material.icons.filled  LazyRow &androidx.compose.material.icons.filled  List &androidx.compose.material.icons.filled  Lock &androidx.compose.material.icons.filled  LockOpen &androidx.compose.material.icons.filled  
MaterialTheme &androidx.compose.material.icons.filled  Modifier &androidx.compose.material.icons.filled  
Notifications &androidx.compose.material.icons.filled  OptIn &androidx.compose.material.icons.filled  OutlinedTextField &androidx.compose.material.icons.filled  
PrivacyTip &androidx.compose.material.icons.filled  Refresh &androidx.compose.material.icons.filled  RoundedCornerShape &androidx.compose.material.icons.filled  Row &androidx.compose.material.icons.filled  Scaffold &androidx.compose.material.icons.filled  Settings &androidx.compose.material.icons.filled  SettingsItem &androidx.compose.material.icons.filled  SettingsSection &androidx.compose.material.icons.filled  SettingsViewModel &androidx.compose.material.icons.filled  Spacer &androidx.compose.material.icons.filled  
StatisticItem &androidx.compose.material.icons.filled  StatisticsSection &androidx.compose.material.icons.filled  Storage &androidx.compose.material.icons.filled  String &androidx.compose.material.icons.filled  Switch &androidx.compose.material.icons.filled  Tab &androidx.compose.material.icons.filled  TabRow &androidx.compose.material.icons.filled  Text &androidx.compose.material.icons.filled  
TextButton &androidx.compose.material.icons.filled  	TopAppBar &androidx.compose.material.icons.filled  TopAppBarDefaults &androidx.compose.material.icons.filled  Unit &androidx.compose.material.icons.filled  ValidationUtils &androidx.compose.material.icons.filled  WalletEntity &androidx.compose.material.icons.filled  
WalletItem &androidx.compose.material.icons.filled  WalletManagementViewModel &androidx.compose.material.icons.filled  android &androidx.compose.material.icons.filled  
background &androidx.compose.material.icons.filled  buttonColors &androidx.compose.material.icons.filled  
cardColors &androidx.compose.material.icons.filled  	clickable &androidx.compose.material.icons.filled  clip &androidx.compose.material.icons.filled  collectAsState &androidx.compose.material.icons.filled  fillMaxSize &androidx.compose.material.icons.filled  fillMaxWidth &androidx.compose.material.icons.filled  forEachIndexed &androidx.compose.material.icons.filled  formatCurrency &androidx.compose.material.icons.filled  getValue &androidx.compose.material.icons.filled  height &androidx.compose.material.icons.filled  
isNotBlank &androidx.compose.material.icons.filled  kotlinx &androidx.compose.material.icons.filled  let &androidx.compose.material.icons.filled  padding &androidx.compose.material.icons.filled  provideDelegate &androidx.compose.material.icons.filled  size &androidx.compose.material.icons.filled  spacedBy &androidx.compose.material.icons.filled  topAppBarColors &androidx.compose.material.icons.filled  weight &androidx.compose.material.icons.filled  width &androidx.compose.material.icons.filled  
AccountCircle androidx.compose.material3  Add androidx.compose.material3  AppRegistration androidx.compose.material3  	ArrowBack androidx.compose.material3  AttachMoney androidx.compose.material3  Boolean androidx.compose.material3  Category androidx.compose.material3  CategoryEntity androidx.compose.material3  CategoryItem androidx.compose.material3  CategoryManagementScreen androidx.compose.material3  CategoryManagementViewModel androidx.compose.material3  CategorySelectionCard androidx.compose.material3  CategoryType androidx.compose.material3  Check androidx.compose.material3  Checkbox androidx.compose.material3  CircleShape androidx.compose.material3  CreateCategoryFormState androidx.compose.material3  CreateWalletFormState androidx.compose.material3  DarkMode androidx.compose.material3  Delete androidx.compose.material3  
DeleteForever androidx.compose.material3  Description androidx.compose.material3  Edit androidx.compose.material3  ExperimentalFoundationApi androidx.compose.material3  FileDownload androidx.compose.material3  HapticFeedbackType androidx.compose.material3  Info androidx.compose.material3  Int androidx.compose.material3  
LazyColumn androidx.compose.material3  Lock androidx.compose.material3  LockOpen androidx.compose.material3  
Notifications androidx.compose.material3  
PrivacyTip androidx.compose.material3  RoundedCornerShape androidx.compose.material3  Settings androidx.compose.material3  SettingsItem androidx.compose.material3  SettingsScreen androidx.compose.material3  SettingsSection androidx.compose.material3  SettingsViewModel androidx.compose.material3  ShimmerTransactionItem androidx.compose.material3  ShimmerWalletCard androidx.compose.material3  SnackbarDuration androidx.compose.material3  SnackbarHost androidx.compose.material3  SnackbarHostState androidx.compose.material3  SnackbarResult androidx.compose.material3  
StatisticItem androidx.compose.material3  StatisticsSection androidx.compose.material3  Storage androidx.compose.material3  SwipeToDeleteBox androidx.compose.material3  Switch androidx.compose.material3  Tab androidx.compose.material3  TabRow androidx.compose.material3  Wallet androidx.compose.material3  WalletEntity androidx.compose.material3  
WalletItem androidx.compose.material3  WalletManagementScreen androidx.compose.material3  WalletManagementViewModel androidx.compose.material3  WalletSelectionCard androidx.compose.material3  android androidx.compose.material3  
background androidx.compose.material3  	clickable androidx.compose.material3  clip androidx.compose.material3  com androidx.compose.material3  forEachIndexed androidx.compose.material3  
isNotEmpty androidx.compose.material3  onSecondaryContainer &androidx.compose.material3.ColorScheme  secondaryContainer &androidx.compose.material3.ColorScheme  Short +androidx.compose.material3.SnackbarDuration  showSnackbar ,androidx.compose.material3.SnackbarHostState  	bodyLarge %androidx.compose.material3.Typography  
titleSmall %androidx.compose.material3.Typography  example androidx.compose.material3.com  
myapplication &androidx.compose.material3.com.example  data 4androidx.compose.material3.com.example.myapplication  local 9androidx.compose.material3.com.example.myapplication.data  entity ?androidx.compose.material3.com.example.myapplication.data.local  CategoryEntity Fandroidx.compose.material3.com.example.myapplication.data.local.entity  WalletEntity Fandroidx.compose.material3.com.example.myapplication.data.local.entity  
AccountCircle androidx.compose.runtime  Add androidx.compose.runtime  AppRegistration androidx.compose.runtime  	ArrowBack androidx.compose.runtime  AttachMoney androidx.compose.runtime  Boolean androidx.compose.runtime  Brush androidx.compose.runtime  Category androidx.compose.runtime  CategoryEntity androidx.compose.runtime  CategoryItem androidx.compose.runtime  CategoryManagementScreen androidx.compose.runtime  CategoryManagementViewModel androidx.compose.runtime  CategorySelectionCard androidx.compose.runtime  CategoryType androidx.compose.runtime  Check androidx.compose.runtime  Checkbox androidx.compose.runtime  CircleShape androidx.compose.runtime  CreateCategoryFormState androidx.compose.runtime  CreateWalletFormState androidx.compose.runtime  DarkMode androidx.compose.runtime  Delete androidx.compose.runtime  
DeleteForever androidx.compose.runtime  Description androidx.compose.runtime  Edit androidx.compose.runtime  ExperimentalFoundationApi androidx.compose.runtime  FileDownload androidx.compose.runtime  HapticFeedbackType androidx.compose.runtime  Info androidx.compose.runtime  Int androidx.compose.runtime  
LazyColumn androidx.compose.runtime  Lock androidx.compose.runtime  LockOpen androidx.compose.runtime  
Notifications androidx.compose.runtime  Offset androidx.compose.runtime  
PrivacyTip androidx.compose.runtime  RoundedCornerShape androidx.compose.runtime  Settings androidx.compose.runtime  SettingsItem androidx.compose.runtime  SettingsScreen androidx.compose.runtime  SettingsSection androidx.compose.runtime  SettingsViewModel androidx.compose.runtime  ShimmerTransactionItem androidx.compose.runtime  ShimmerWalletCard androidx.compose.runtime  SnackbarDuration androidx.compose.runtime  SnackbarHost androidx.compose.runtime  SnackbarHostState androidx.compose.runtime  
StatisticItem androidx.compose.runtime  StatisticsSection androidx.compose.runtime  Storage androidx.compose.runtime  SwipeToDeleteBox androidx.compose.runtime  Switch androidx.compose.runtime  Tab androidx.compose.runtime  TabRow androidx.compose.runtime  Wallet androidx.compose.runtime  WalletEntity androidx.compose.runtime  
WalletItem androidx.compose.runtime  WalletManagementScreen androidx.compose.runtime  WalletManagementViewModel androidx.compose.runtime  WalletSelectionCard androidx.compose.runtime  android androidx.compose.runtime  androidx androidx.compose.runtime  animateFloat androidx.compose.runtime  
background androidx.compose.runtime  	clickable androidx.compose.runtime  clip androidx.compose.runtime  com androidx.compose.runtime  forEachIndexed androidx.compose.runtime  infiniteRepeatable androidx.compose.runtime  
isNotEmpty androidx.compose.runtime  linearGradient androidx.compose.runtime  onGloballyPositioned androidx.compose.runtime  rememberInfiniteTransition androidx.compose.runtime  
shimmerEffect androidx.compose.runtime  tween androidx.compose.runtime  example androidx.compose.runtime.com  
myapplication $androidx.compose.runtime.com.example  data 2androidx.compose.runtime.com.example.myapplication  local 7androidx.compose.runtime.com.example.myapplication.data  entity =androidx.compose.runtime.com.example.myapplication.data.local  CategoryEntity Dandroidx.compose.runtime.com.example.myapplication.data.local.entity  WalletEntity Dandroidx.compose.runtime.com.example.myapplication.data.local.entity  invoke 5androidx.compose.runtime.internal.ComposableFunction0  composed androidx.compose.ui  Brush androidx.compose.ui.Modifier  Color androidx.compose.ui.Modifier  Offset androidx.compose.ui.Modifier  androidx androidx.compose.ui.Modifier  animateFloat androidx.compose.ui.Modifier  
background androidx.compose.ui.Modifier  	clickable androidx.compose.ui.Modifier  clip androidx.compose.ui.Modifier  combinedClickable androidx.compose.ui.Modifier  composed androidx.compose.ui.Modifier  getValue androidx.compose.ui.Modifier  infiniteRepeatable androidx.compose.ui.Modifier  linearGradient androidx.compose.ui.Modifier  listOf androidx.compose.ui.Modifier  mutableStateOf androidx.compose.ui.Modifier  onGloballyPositioned androidx.compose.ui.Modifier  provideDelegate androidx.compose.ui.Modifier  remember androidx.compose.ui.Modifier  rememberInfiniteTransition androidx.compose.ui.Modifier  	semantics androidx.compose.ui.Modifier  setValue androidx.compose.ui.Modifier  
shimmerEffect androidx.compose.ui.Modifier  then androidx.compose.ui.Modifier  tween androidx.compose.ui.Modifier  
background &androidx.compose.ui.Modifier.Companion  clip androidx.compose.ui.draw  Offset androidx.compose.ui.geometry  Size androidx.compose.ui.geometry  	Companion !androidx.compose.ui.geometry.Size  Zero !androidx.compose.ui.geometry.Size  height !androidx.compose.ui.geometry.Size  width !androidx.compose.ui.geometry.Size  Zero +androidx.compose.ui.geometry.Size.Companion  Brush androidx.compose.ui.graphics  	Companion "androidx.compose.ui.graphics.Brush  linearGradient "androidx.compose.ui.graphics.Brush  linearGradient ,androidx.compose.ui.graphics.Brush.Companion  	Companion "androidx.compose.ui.graphics.Color  White "androidx.compose.ui.graphics.Color  White ,androidx.compose.ui.graphics.Color.Companion  HapticFeedback "androidx.compose.ui.hapticfeedback  HapticFeedbackType "androidx.compose.ui.hapticfeedback  performHapticFeedback 1androidx.compose.ui.hapticfeedback.HapticFeedback  	Companion 5androidx.compose.ui.hapticfeedback.HapticFeedbackType  	LongPress 5androidx.compose.ui.hapticfeedback.HapticFeedbackType  	LongPress ?androidx.compose.ui.hapticfeedback.HapticFeedbackType.Companion  LayoutCoordinates androidx.compose.ui.layout  onGloballyPositioned androidx.compose.ui.layout  size ,androidx.compose.ui.layout.LayoutCoordinates  LocalHapticFeedback androidx.compose.ui.platform  SemanticsPropertyReceiver androidx.compose.ui.semantics  contentDescription androidx.compose.ui.semantics  	semantics androidx.compose.ui.semantics  contentDescription 7androidx.compose.ui.semantics.SemanticsPropertyReceiver  formatCurrency 7androidx.compose.ui.semantics.SemanticsPropertyReceiver  
formatDate 7androidx.compose.ui.semantics.SemanticsPropertyReceiver  IntSize androidx.compose.ui.unit  height  androidx.compose.ui.unit.IntSize  width  androidx.compose.ui.unit.IntSize  popBackStack !androidx.navigation.NavController  CategoryManagementScreen #androidx.navigation.NavGraphBuilder  SettingsScreen #androidx.navigation.NavGraphBuilder  WalletManagementScreen #androidx.navigation.NavGraphBuilder  popBackStack %androidx.navigation.NavHostController  Boolean 
androidx.room  CategoryEntity 
androidx.room  System 
androidx.room  SET_NULL androidx.room.ForeignKey  SET_NULL "androidx.room.ForeignKey.Companion  CategoryDao androidx.room.RoomDatabase  
addMigrations "androidx.room.RoomDatabase.Builder  	Migration androidx.room.migration  SupportSQLiteDatabase androidx.sqlite.db  execSQL (androidx.sqlite.db.SupportSQLiteDatabase  CategoryDao $com.example.myapplication.data.local  CategoryEntity $com.example.myapplication.data.local  	Migration $com.example.myapplication.data.local  SupportSQLiteDatabase $com.example.myapplication.data.local  System $com.example.myapplication.data.local  forEach $com.example.myapplication.data.local  insertDefaultCategories $com.example.myapplication.data.local  listOf $com.example.myapplication.data.local  to $com.example.myapplication.data.local  
trimIndent $com.example.myapplication.data.local  categoryDao 0com.example.myapplication.data.local.AppDatabase  
MIGRATION_1_2 5com.example.myapplication.data.local.DatabaseProvider  System 5com.example.myapplication.data.local.DatabaseProvider  insertDefaultCategories 5com.example.myapplication.data.local.DatabaseProvider  listOf 5com.example.myapplication.data.local.DatabaseProvider  to 5com.example.myapplication.data.local.DatabaseProvider  
trimIndent 5com.example.myapplication.data.local.DatabaseProvider  Boolean (com.example.myapplication.data.local.dao  CategoryDao (com.example.myapplication.data.local.dao  CategoryEntity (com.example.myapplication.data.local.dao  System (com.example.myapplication.data.local.dao  activateCategory 4com.example.myapplication.data.local.dao.CategoryDao  deactivateCategory 4com.example.myapplication.data.local.dao.CategoryDao  deleteCategory 4com.example.myapplication.data.local.dao.CategoryDao  getAllActiveCategories 4com.example.myapplication.data.local.dao.CategoryDao  getCategoriesByType 4com.example.myapplication.data.local.dao.CategoryDao  getCategoryById 4com.example.myapplication.data.local.dao.CategoryDao  getCategoryCountByType 4com.example.myapplication.data.local.dao.CategoryDao  getDefaultCategories 4com.example.myapplication.data.local.dao.CategoryDao  getDefaultCategoriesByType 4com.example.myapplication.data.local.dao.CategoryDao  insertCategories 4com.example.myapplication.data.local.dao.CategoryDao  insertCategory 4com.example.myapplication.data.local.dao.CategoryDao  searchCategories 4com.example.myapplication.data.local.dao.CategoryDao  updateCategory 4com.example.myapplication.data.local.dao.CategoryDao  System 2com.example.myapplication.data.local.dao.WalletDao  activateWallet 2com.example.myapplication.data.local.dao.WalletDao  deactivateWallet 2com.example.myapplication.data.local.dao.WalletDao  getActiveWalletCount 2com.example.myapplication.data.local.dao.WalletDao  getAllActiveWallets 2com.example.myapplication.data.local.dao.WalletDao  getAvailableWallets 2com.example.myapplication.data.local.dao.WalletDao  getFirstActiveWallet 2com.example.myapplication.data.local.dao.WalletDao  getFirstActiveWalletFlow 2com.example.myapplication.data.local.dao.WalletDao  getFirstAvailableWallet 2com.example.myapplication.data.local.dao.WalletDao  getTotalBalance 2com.example.myapplication.data.local.dao.WalletDao  
searchWallets 2com.example.myapplication.data.local.dao.WalletDao  updateFrozenStatus 2com.example.myapplication.data.local.dao.WalletDao  Boolean +com.example.myapplication.data.local.entity  CategoryEntity +com.example.myapplication.data.local.entity  budgetLimit :com.example.myapplication.data.local.entity.CategoryEntity  color :com.example.myapplication.data.local.entity.CategoryEntity  copy :com.example.myapplication.data.local.entity.CategoryEntity  icon :com.example.myapplication.data.local.entity.CategoryEntity  id :com.example.myapplication.data.local.entity.CategoryEntity  isActive :com.example.myapplication.data.local.entity.CategoryEntity  	isDefault :com.example.myapplication.data.local.entity.CategoryEntity  let :com.example.myapplication.data.local.entity.CategoryEntity  name :com.example.myapplication.data.local.entity.CategoryEntity  walletId =com.example.myapplication.data.local.entity.TransactionEntity  color 8com.example.myapplication.data.local.entity.WalletEntity  copy 8com.example.myapplication.data.local.entity.WalletEntity  currency 8com.example.myapplication.data.local.entity.WalletEntity  description 8com.example.myapplication.data.local.entity.WalletEntity  icon 8com.example.myapplication.data.local.entity.WalletEntity  isActive 8com.example.myapplication.data.local.entity.WalletEntity  isFrozen 8com.example.myapplication.data.local.entity.WalletEntity  CategoryDao )com.example.myapplication.data.repository  CategoryEntity )com.example.myapplication.data.repository  CategoryRepository )com.example.myapplication.data.repository  forEach )com.example.myapplication.data.repository  let )com.example.myapplication.data.repository  listOf )com.example.myapplication.data.repository  CategoryEntity <com.example.myapplication.data.repository.CategoryRepository  System <com.example.myapplication.data.repository.CategoryRepository  categoryDao <com.example.myapplication.data.repository.CategoryRepository  createCustomCategory <com.example.myapplication.data.repository.CategoryRepository  deactivateCategory <com.example.myapplication.data.repository.CategoryRepository  getCategoriesByType <com.example.myapplication.data.repository.CategoryRepository  let <com.example.myapplication.data.repository.CategoryRepository  updateCategory <com.example.myapplication.data.repository.CategoryRepository  CategoryRepository <com.example.myapplication.data.repository.RepositoryProvider  provideCategoryRepository <com.example.myapplication.data.repository.RepositoryProvider  deleteTransaction ?com.example.myapplication.data.repository.TransactionRepository  System :com.example.myapplication.data.repository.WalletRepository  createDefaultWalletsIfNeeded :com.example.myapplication.data.repository.WalletRepository  createWallet :com.example.myapplication.data.repository.WalletRepository  deactivateWallet :com.example.myapplication.data.repository.WalletRepository  freezeWallet :com.example.myapplication.data.repository.WalletRepository  getActiveWalletCount :com.example.myapplication.data.repository.WalletRepository  
getAllWallets :com.example.myapplication.data.repository.WalletRepository  getAvailableWallets :com.example.myapplication.data.repository.WalletRepository  getFirstActiveWallet :com.example.myapplication.data.repository.WalletRepository  getTotalBalance :com.example.myapplication.data.repository.WalletRepository  isWalletAvailableForTransaction :com.example.myapplication.data.repository.WalletRepository  listOf :com.example.myapplication.data.repository.WalletRepository  unfreezeWallet :com.example.myapplication.data.repository.WalletRepository  updateWallet :com.example.myapplication.data.repository.WalletRepository  AlertDialog 'com.example.myapplication.ui.components  	Alignment 'com.example.myapplication.ui.components  Arrangement 'com.example.myapplication.ui.components  Box 'com.example.myapplication.ui.components  Brush 'com.example.myapplication.ui.components  Button 'com.example.myapplication.ui.components  ButtonDefaults 'com.example.myapplication.ui.components  Card 'com.example.myapplication.ui.components  CardDefaults 'com.example.myapplication.ui.components  Color 'com.example.myapplication.ui.components  Column 'com.example.myapplication.ui.components  
Composable 'com.example.myapplication.ui.components  ExperimentalFoundationApi 'com.example.myapplication.ui.components  HapticFeedbackType 'com.example.myapplication.ui.components  Icon 'com.example.myapplication.ui.components  Icons 'com.example.myapplication.ui.components  
MaterialTheme 'com.example.myapplication.ui.components  Modifier 'com.example.myapplication.ui.components  Offset 'com.example.myapplication.ui.components  OptIn 'com.example.myapplication.ui.components  Row 'com.example.myapplication.ui.components  ShimmerTransactionItem 'com.example.myapplication.ui.components  ShimmerWalletCard 'com.example.myapplication.ui.components  Spacer 'com.example.myapplication.ui.components  SwipeToDeleteBox 'com.example.myapplication.ui.components  Text 'com.example.myapplication.ui.components  
TextButton 'com.example.myapplication.ui.components  Unit 'com.example.myapplication.ui.components  androidx 'com.example.myapplication.ui.components  animateFloat 'com.example.myapplication.ui.components  buttonColors 'com.example.myapplication.ui.components  
cardColors 'com.example.myapplication.ui.components  
cardElevation 'com.example.myapplication.ui.components  fillMaxWidth 'com.example.myapplication.ui.components  getValue 'com.example.myapplication.ui.components  height 'com.example.myapplication.ui.components  infiniteRepeatable 'com.example.myapplication.ui.components  linearGradient 'com.example.myapplication.ui.components  listOf 'com.example.myapplication.ui.components  mutableStateOf 'com.example.myapplication.ui.components  onGloballyPositioned 'com.example.myapplication.ui.components  padding 'com.example.myapplication.ui.components  provideDelegate 'com.example.myapplication.ui.components  remember 'com.example.myapplication.ui.components  rememberInfiniteTransition 'com.example.myapplication.ui.components  setValue 'com.example.myapplication.ui.components  
shimmerEffect 'com.example.myapplication.ui.components  size 'com.example.myapplication.ui.components  spacedBy 'com.example.myapplication.ui.components  tween 'com.example.myapplication.ui.components  weight 'com.example.myapplication.ui.components  width 'com.example.myapplication.ui.components  CategoryManagementScreen 'com.example.myapplication.ui.navigation  SettingsScreen 'com.example.myapplication.ui.navigation  WalletManagementScreen 'com.example.myapplication.ui.navigation  Category .com.example.myapplication.ui.navigation.Screen  CategoryManagement .com.example.myapplication.ui.navigation.Screen  Settings .com.example.myapplication.ui.navigation.Screen  WalletManagement .com.example.myapplication.ui.navigation.Screen  route Acom.example.myapplication.ui.navigation.Screen.CategoryManagement  route 7com.example.myapplication.ui.navigation.Screen.Settings  route ?com.example.myapplication.ui.navigation.Screen.WalletManagement  AlertDialog 3com.example.myapplication.ui.screen.add_transaction  Box 3com.example.myapplication.ui.screen.add_transaction  CategoryEntity 3com.example.myapplication.ui.screen.add_transaction  CategoryRepository 3com.example.myapplication.ui.screen.add_transaction  CategorySelectionCard 3com.example.myapplication.ui.screen.add_transaction  CategorySelectorDialog 3com.example.myapplication.ui.screen.add_transaction  CircleShape 3com.example.myapplication.ui.screen.add_transaction  
TextButton 3com.example.myapplication.ui.screen.add_transaction  WalletEntity 3com.example.myapplication.ui.screen.add_transaction  WalletSelectionCard 3com.example.myapplication.ui.screen.add_transaction  WalletSelectorDialog 3com.example.myapplication.ui.screen.add_transaction  android 3com.example.myapplication.ui.screen.add_transaction  
background 3com.example.myapplication.ui.screen.add_transaction  categoryRepository 3com.example.myapplication.ui.screen.add_transaction  clip 3com.example.myapplication.ui.screen.add_transaction  com 3com.example.myapplication.ui.screen.add_transaction  	emptyList 3com.example.myapplication.ui.screen.add_transaction  firstOrNull 3com.example.myapplication.ui.screen.add_transaction  provideCategoryRepository 3com.example.myapplication.ui.screen.add_transaction  availableCategories Icom.example.myapplication.ui.screen.add_transaction.AddTransactionUiState  availableWallets Icom.example.myapplication.ui.screen.add_transaction.AddTransactionUiState  selectedCategory Icom.example.myapplication.ui.screen.add_transaction.AddTransactionUiState  selectedWallet Icom.example.myapplication.ui.screen.add_transaction.AddTransactionUiState  showCategorySelector Icom.example.myapplication.ui.screen.add_transaction.AddTransactionUiState  showWalletSelector Icom.example.myapplication.ui.screen.add_transaction.AddTransactionUiState  categoryRepository Kcom.example.myapplication.ui.screen.add_transaction.AddTransactionViewModel  firstOrNull Kcom.example.myapplication.ui.screen.add_transaction.AddTransactionViewModel  hideCategorySelector Kcom.example.myapplication.ui.screen.add_transaction.AddTransactionViewModel  hideWalletSelector Kcom.example.myapplication.ui.screen.add_transaction.AddTransactionViewModel  loadAvailableCategories Kcom.example.myapplication.ui.screen.add_transaction.AddTransactionViewModel  loadAvailableWallets Kcom.example.myapplication.ui.screen.add_transaction.AddTransactionViewModel  loadCategoriesForType Kcom.example.myapplication.ui.screen.add_transaction.AddTransactionViewModel  provideCategoryRepository Kcom.example.myapplication.ui.screen.add_transaction.AddTransactionViewModel  selectCategory Kcom.example.myapplication.ui.screen.add_transaction.AddTransactionViewModel  selectWallet Kcom.example.myapplication.ui.screen.add_transaction.AddTransactionViewModel  showCategorySelector Kcom.example.myapplication.ui.screen.add_transaction.AddTransactionViewModel  showWalletSelector Kcom.example.myapplication.ui.screen.add_transaction.AddTransactionViewModel  value Ccom.example.myapplication.ui.screen.add_transaction.TransactionType  example 7com.example.myapplication.ui.screen.add_transaction.com  
myapplication ?com.example.myapplication.ui.screen.add_transaction.com.example  data Mcom.example.myapplication.ui.screen.add_transaction.com.example.myapplication  local Rcom.example.myapplication.ui.screen.add_transaction.com.example.myapplication.data  entity Xcom.example.myapplication.ui.screen.add_transaction.com.example.myapplication.data.local  CategoryEntity _com.example.myapplication.ui.screen.add_transaction.com.example.myapplication.data.local.entity  WalletEntity _com.example.myapplication.ui.screen.add_transaction.com.example.myapplication.data.local.entity  Add 7com.example.myapplication.ui.screen.category_management  AlertDialog 7com.example.myapplication.ui.screen.category_management  	Alignment 7com.example.myapplication.ui.screen.category_management  AndroidViewModel 7com.example.myapplication.ui.screen.category_management  Application 7com.example.myapplication.ui.screen.category_management  Arrangement 7com.example.myapplication.ui.screen.category_management  	ArrowBack 7com.example.myapplication.ui.screen.category_management  Boolean 7com.example.myapplication.ui.screen.category_management  Box 7com.example.myapplication.ui.screen.category_management  Button 7com.example.myapplication.ui.screen.category_management  ButtonDefaults 7com.example.myapplication.ui.screen.category_management  Card 7com.example.myapplication.ui.screen.category_management  CardDefaults 7com.example.myapplication.ui.screen.category_management  Category 7com.example.myapplication.ui.screen.category_management  CategoryEntity 7com.example.myapplication.ui.screen.category_management  CategoryItem 7com.example.myapplication.ui.screen.category_management  CategoryManagementScreen 7com.example.myapplication.ui.screen.category_management  CategoryManagementUiState 7com.example.myapplication.ui.screen.category_management  CategoryManagementViewModel 7com.example.myapplication.ui.screen.category_management  CategoryRepository 7com.example.myapplication.ui.screen.category_management  CategoryType 7com.example.myapplication.ui.screen.category_management  Check 7com.example.myapplication.ui.screen.category_management  Checkbox 7com.example.myapplication.ui.screen.category_management  CircleShape 7com.example.myapplication.ui.screen.category_management  CircularProgressIndicator 7com.example.myapplication.ui.screen.category_management  Color 7com.example.myapplication.ui.screen.category_management  Column 7com.example.myapplication.ui.screen.category_management  
Composable 7com.example.myapplication.ui.screen.category_management  CreateCategoryFormState 7com.example.myapplication.ui.screen.category_management  CreateEditCategoryDialog 7com.example.myapplication.ui.screen.category_management  Delete 7com.example.myapplication.ui.screen.category_management  Edit 7com.example.myapplication.ui.screen.category_management  ErrorHandler 7com.example.myapplication.ui.screen.category_management  	Exception 7com.example.myapplication.ui.screen.category_management  ExperimentalMaterial3Api 7com.example.myapplication.ui.screen.category_management  FloatingActionButton 7com.example.myapplication.ui.screen.category_management  
FontWeight 7com.example.myapplication.ui.screen.category_management  Icon 7com.example.myapplication.ui.screen.category_management  
IconButton 7com.example.myapplication.ui.screen.category_management  Icons 7com.example.myapplication.ui.screen.category_management  LaunchedEffect 7com.example.myapplication.ui.screen.category_management  
LazyColumn 7com.example.myapplication.ui.screen.category_management  LazyRow 7com.example.myapplication.ui.screen.category_management  List 7com.example.myapplication.ui.screen.category_management  
MaterialTheme 7com.example.myapplication.ui.screen.category_management  Modifier 7com.example.myapplication.ui.screen.category_management  MutableStateFlow 7com.example.myapplication.ui.screen.category_management  OptIn 7com.example.myapplication.ui.screen.category_management  OutlinedTextField 7com.example.myapplication.ui.screen.category_management  RepositoryProvider 7com.example.myapplication.ui.screen.category_management  RoundedCornerShape 7com.example.myapplication.ui.screen.category_management  Row 7com.example.myapplication.ui.screen.category_management  Scaffold 7com.example.myapplication.ui.screen.category_management  Spacer 7com.example.myapplication.ui.screen.category_management  	StateFlow 7com.example.myapplication.ui.screen.category_management  String 7com.example.myapplication.ui.screen.category_management  System 7com.example.myapplication.ui.screen.category_management  Tab 7com.example.myapplication.ui.screen.category_management  TabRow 7com.example.myapplication.ui.screen.category_management  Text 7com.example.myapplication.ui.screen.category_management  
TextButton 7com.example.myapplication.ui.screen.category_management  	TopAppBar 7com.example.myapplication.ui.screen.category_management  TopAppBarDefaults 7com.example.myapplication.ui.screen.category_management  Unit 7com.example.myapplication.ui.screen.category_management  ValidationUtils 7com.example.myapplication.ui.screen.category_management  _createFormState 7com.example.myapplication.ui.screen.category_management  _uiState 7com.example.myapplication.ui.screen.category_management  android 7com.example.myapplication.ui.screen.category_management  asStateFlow 7com.example.myapplication.ui.screen.category_management  
background 7com.example.myapplication.ui.screen.category_management  buttonColors 7com.example.myapplication.ui.screen.category_management  
cardColors 7com.example.myapplication.ui.screen.category_management  categoryRepository 7com.example.myapplication.ui.screen.category_management  	clickable 7com.example.myapplication.ui.screen.category_management  clip 7com.example.myapplication.ui.screen.category_management  collectAsState 7com.example.myapplication.ui.screen.category_management  	emptyList 7com.example.myapplication.ui.screen.category_management  fillMaxSize 7com.example.myapplication.ui.screen.category_management  fillMaxWidth 7com.example.myapplication.ui.screen.category_management  forEachIndexed 7com.example.myapplication.ui.screen.category_management  formatCurrency 7com.example.myapplication.ui.screen.category_management  getErrorMessage 7com.example.myapplication.ui.screen.category_management  getValue 7com.example.myapplication.ui.screen.category_management  height 7com.example.myapplication.ui.screen.category_management  
isNotBlank 7com.example.myapplication.ui.screen.category_management  kotlinx 7com.example.myapplication.ui.screen.category_management  launch 7com.example.myapplication.ui.screen.category_management  let 7com.example.myapplication.ui.screen.category_management  listOf 7com.example.myapplication.ui.screen.category_management  logError 7com.example.myapplication.ui.screen.category_management  padding 7com.example.myapplication.ui.screen.category_management  provideCategoryRepository 7com.example.myapplication.ui.screen.category_management  provideDelegate 7com.example.myapplication.ui.screen.category_management  
sanitizeInput 7com.example.myapplication.ui.screen.category_management  size 7com.example.myapplication.ui.screen.category_management  spacedBy 7com.example.myapplication.ui.screen.category_management  toDouble 7com.example.myapplication.ui.screen.category_management  topAppBarColors 7com.example.myapplication.ui.screen.category_management  validateAmount 7com.example.myapplication.ui.screen.category_management  validateCategory 7com.example.myapplication.ui.screen.category_management  weight 7com.example.myapplication.ui.screen.category_management  width 7com.example.myapplication.ui.screen.category_management  copy Qcom.example.myapplication.ui.screen.category_management.CategoryManagementUiState  errorMessage Qcom.example.myapplication.ui.screen.category_management.CategoryManagementUiState  expenseCategories Qcom.example.myapplication.ui.screen.category_management.CategoryManagementUiState  incomeCategories Qcom.example.myapplication.ui.screen.category_management.CategoryManagementUiState  	isLoading Qcom.example.myapplication.ui.screen.category_management.CategoryManagementUiState  selectedCategory Qcom.example.myapplication.ui.screen.category_management.CategoryManagementUiState  selectedTab Qcom.example.myapplication.ui.screen.category_management.CategoryManagementUiState  showCreateCategoryDialog Qcom.example.myapplication.ui.screen.category_management.CategoryManagementUiState  showDeleteConfirmDialog Qcom.example.myapplication.ui.screen.category_management.CategoryManagementUiState  showEditCategoryDialog Qcom.example.myapplication.ui.screen.category_management.CategoryManagementUiState  successMessage Qcom.example.myapplication.ui.screen.category_management.CategoryManagementUiState  CategoryManagementUiState Scom.example.myapplication.ui.screen.category_management.CategoryManagementViewModel  CreateCategoryFormState Scom.example.myapplication.ui.screen.category_management.CategoryManagementViewModel  ErrorHandler Scom.example.myapplication.ui.screen.category_management.CategoryManagementViewModel  MutableStateFlow Scom.example.myapplication.ui.screen.category_management.CategoryManagementViewModel  RepositoryProvider Scom.example.myapplication.ui.screen.category_management.CategoryManagementViewModel  System Scom.example.myapplication.ui.screen.category_management.CategoryManagementViewModel  ValidationUtils Scom.example.myapplication.ui.screen.category_management.CategoryManagementViewModel  _createFormState Scom.example.myapplication.ui.screen.category_management.CategoryManagementViewModel  _uiState Scom.example.myapplication.ui.screen.category_management.CategoryManagementViewModel  asStateFlow Scom.example.myapplication.ui.screen.category_management.CategoryManagementViewModel  availableColors Scom.example.myapplication.ui.screen.category_management.CategoryManagementViewModel  availableIcons Scom.example.myapplication.ui.screen.category_management.CategoryManagementViewModel  categoryRepository Scom.example.myapplication.ui.screen.category_management.CategoryManagementViewModel  
clearMessages Scom.example.myapplication.ui.screen.category_management.CategoryManagementViewModel  createCategory Scom.example.myapplication.ui.screen.category_management.CategoryManagementViewModel  createFormState Scom.example.myapplication.ui.screen.category_management.CategoryManagementViewModel  deleteCategory Scom.example.myapplication.ui.screen.category_management.CategoryManagementViewModel  getErrorMessage Scom.example.myapplication.ui.screen.category_management.CategoryManagementViewModel  hideCreateCategoryDialog Scom.example.myapplication.ui.screen.category_management.CategoryManagementViewModel  hideDeleteConfirmDialog Scom.example.myapplication.ui.screen.category_management.CategoryManagementViewModel  hideEditCategoryDialog Scom.example.myapplication.ui.screen.category_management.CategoryManagementViewModel  invoke Scom.example.myapplication.ui.screen.category_management.CategoryManagementViewModel  
isNotBlank Scom.example.myapplication.ui.screen.category_management.CategoryManagementViewModel  launch Scom.example.myapplication.ui.screen.category_management.CategoryManagementViewModel  listOf Scom.example.myapplication.ui.screen.category_management.CategoryManagementViewModel  loadCategories Scom.example.myapplication.ui.screen.category_management.CategoryManagementViewModel  logError Scom.example.myapplication.ui.screen.category_management.CategoryManagementViewModel  provideCategoryRepository Scom.example.myapplication.ui.screen.category_management.CategoryManagementViewModel  
sanitizeInput Scom.example.myapplication.ui.screen.category_management.CategoryManagementViewModel  	selectTab Scom.example.myapplication.ui.screen.category_management.CategoryManagementViewModel  showCreateCategoryDialog Scom.example.myapplication.ui.screen.category_management.CategoryManagementViewModel  showDeleteConfirmDialog Scom.example.myapplication.ui.screen.category_management.CategoryManagementViewModel  showEditCategoryDialog Scom.example.myapplication.ui.screen.category_management.CategoryManagementViewModel  toDouble Scom.example.myapplication.ui.screen.category_management.CategoryManagementViewModel  uiState Scom.example.myapplication.ui.screen.category_management.CategoryManagementViewModel  updateCategory Scom.example.myapplication.ui.screen.category_management.CategoryManagementViewModel  updateFormBudgetLimit Scom.example.myapplication.ui.screen.category_management.CategoryManagementViewModel  updateFormColor Scom.example.myapplication.ui.screen.category_management.CategoryManagementViewModel  updateFormHasBudgetLimit Scom.example.myapplication.ui.screen.category_management.CategoryManagementViewModel  updateFormIcon Scom.example.myapplication.ui.screen.category_management.CategoryManagementViewModel  updateFormName Scom.example.myapplication.ui.screen.category_management.CategoryManagementViewModel  validateAmount Scom.example.myapplication.ui.screen.category_management.CategoryManagementViewModel  validateCategory Scom.example.myapplication.ui.screen.category_management.CategoryManagementViewModel  viewModelScope Scom.example.myapplication.ui.screen.category_management.CategoryManagementViewModel  EXPENSE Dcom.example.myapplication.ui.screen.category_management.CategoryType  displayName Dcom.example.myapplication.ui.screen.category_management.CategoryType  value Dcom.example.myapplication.ui.screen.category_management.CategoryType  values Dcom.example.myapplication.ui.screen.category_management.CategoryType  budgetLimit Ocom.example.myapplication.ui.screen.category_management.CreateCategoryFormState  copy Ocom.example.myapplication.ui.screen.category_management.CreateCategoryFormState  hasBudgetLimit Ocom.example.myapplication.ui.screen.category_management.CreateCategoryFormState  name Ocom.example.myapplication.ui.screen.category_management.CreateCategoryFormState  
selectedColor Ocom.example.myapplication.ui.screen.category_management.CreateCategoryFormState  selectedIcon Ocom.example.myapplication.ui.screen.category_management.CreateCategoryFormState  
IconButton (com.example.myapplication.ui.screen.home  ShimmerTransactionItem (com.example.myapplication.ui.screen.home  ShimmerWalletCard (com.example.myapplication.ui.screen.home  SnackbarDuration (com.example.myapplication.ui.screen.home  SnackbarHost (com.example.myapplication.ui.screen.home  SnackbarHostState (com.example.myapplication.ui.screen.home  SwipeToDeleteBox (com.example.myapplication.ui.screen.home  
isNotEmpty (com.example.myapplication.ui.screen.home  remember (com.example.myapplication.ui.screen.home  deleteTransaction 6com.example.myapplication.ui.screen.home.HomeViewModel  refreshData 6com.example.myapplication.ui.screen.home.HomeViewModel  
AccountCircle ,com.example.myapplication.ui.screen.settings  AlertDialog ,com.example.myapplication.ui.screen.settings  	Alignment ,com.example.myapplication.ui.screen.settings  AndroidViewModel ,com.example.myapplication.ui.screen.settings  AppRegistration ,com.example.myapplication.ui.screen.settings  Application ,com.example.myapplication.ui.screen.settings  Arrangement ,com.example.myapplication.ui.screen.settings  	ArrowBack ,com.example.myapplication.ui.screen.settings  AttachMoney ,com.example.myapplication.ui.screen.settings  Boolean ,com.example.myapplication.ui.screen.settings  Button ,com.example.myapplication.ui.screen.settings  ButtonDefaults ,com.example.myapplication.ui.screen.settings  Card ,com.example.myapplication.ui.screen.settings  CardDefaults ,com.example.myapplication.ui.screen.settings  Category ,com.example.myapplication.ui.screen.settings  Color ,com.example.myapplication.ui.screen.settings  Column ,com.example.myapplication.ui.screen.settings  
Composable ,com.example.myapplication.ui.screen.settings  Context ,com.example.myapplication.ui.screen.settings  DarkMode ,com.example.myapplication.ui.screen.settings  
DeleteForever ,com.example.myapplication.ui.screen.settings  Description ,com.example.myapplication.ui.screen.settings  Double ,com.example.myapplication.ui.screen.settings  ErrorHandler ,com.example.myapplication.ui.screen.settings  	Exception ,com.example.myapplication.ui.screen.settings  ExperimentalMaterial3Api ,com.example.myapplication.ui.screen.settings  FileDownload ,com.example.myapplication.ui.screen.settings  
FontWeight ,com.example.myapplication.ui.screen.settings  Icon ,com.example.myapplication.ui.screen.settings  
IconButton ,com.example.myapplication.ui.screen.settings  Icons ,com.example.myapplication.ui.screen.settings  ImageVector ,com.example.myapplication.ui.screen.settings  Info ,com.example.myapplication.ui.screen.settings  Int ,com.example.myapplication.ui.screen.settings  LaunchedEffect ,com.example.myapplication.ui.screen.settings  
MaterialTheme ,com.example.myapplication.ui.screen.settings  Modifier ,com.example.myapplication.ui.screen.settings  MutableStateFlow ,com.example.myapplication.ui.screen.settings  
Notifications ,com.example.myapplication.ui.screen.settings  OptIn ,com.example.myapplication.ui.screen.settings  
PrivacyTip ,com.example.myapplication.ui.screen.settings  RepositoryProvider ,com.example.myapplication.ui.screen.settings  Row ,com.example.myapplication.ui.screen.settings  Scaffold ,com.example.myapplication.ui.screen.settings  Settings ,com.example.myapplication.ui.screen.settings  SettingsItem ,com.example.myapplication.ui.screen.settings  SettingsScreen ,com.example.myapplication.ui.screen.settings  SettingsSection ,com.example.myapplication.ui.screen.settings  SettingsUiState ,com.example.myapplication.ui.screen.settings  SettingsViewModel ,com.example.myapplication.ui.screen.settings  Spacer ,com.example.myapplication.ui.screen.settings  	StateFlow ,com.example.myapplication.ui.screen.settings  
StatisticItem ,com.example.myapplication.ui.screen.settings  StatisticsSection ,com.example.myapplication.ui.screen.settings  Storage ,com.example.myapplication.ui.screen.settings  String ,com.example.myapplication.ui.screen.settings  Switch ,com.example.myapplication.ui.screen.settings  Text ,com.example.myapplication.ui.screen.settings  
TextButton ,com.example.myapplication.ui.screen.settings  	TopAppBar ,com.example.myapplication.ui.screen.settings  TopAppBarDefaults ,com.example.myapplication.ui.screen.settings  TransactionRepository ,com.example.myapplication.ui.screen.settings  Unit ,com.example.myapplication.ui.screen.settings  ValidationUtils ,com.example.myapplication.ui.screen.settings  WalletRepository ,com.example.myapplication.ui.screen.settings  _uiState ,com.example.myapplication.ui.screen.settings  asStateFlow ,com.example.myapplication.ui.screen.settings  buttonColors ,com.example.myapplication.ui.screen.settings  
cardColors ,com.example.myapplication.ui.screen.settings  collectAsState ,com.example.myapplication.ui.screen.settings  fillMaxSize ,com.example.myapplication.ui.screen.settings  fillMaxWidth ,com.example.myapplication.ui.screen.settings  formatCurrency ,com.example.myapplication.ui.screen.settings  getErrorMessage ,com.example.myapplication.ui.screen.settings  getValue ,com.example.myapplication.ui.screen.settings  height ,com.example.myapplication.ui.screen.settings  kotlinx ,com.example.myapplication.ui.screen.settings  launch ,com.example.myapplication.ui.screen.settings  let ,com.example.myapplication.ui.screen.settings  loadStatistics ,com.example.myapplication.ui.screen.settings  logError ,com.example.myapplication.ui.screen.settings  padding ,com.example.myapplication.ui.screen.settings  provideDelegate ,com.example.myapplication.ui.screen.settings  provideTransactionRepository ,com.example.myapplication.ui.screen.settings  provideWalletRepository ,com.example.myapplication.ui.screen.settings  size ,com.example.myapplication.ui.screen.settings  spacedBy ,com.example.myapplication.ui.screen.settings  topAppBarColors ,com.example.myapplication.ui.screen.settings  walletRepository ,com.example.myapplication.ui.screen.settings  weight ,com.example.myapplication.ui.screen.settings  width ,com.example.myapplication.ui.screen.settings  
appVersion <com.example.myapplication.ui.screen.settings.SettingsUiState  copy <com.example.myapplication.ui.screen.settings.SettingsUiState  defaultCurrency <com.example.myapplication.ui.screen.settings.SettingsUiState  errorMessage <com.example.myapplication.ui.screen.settings.SettingsUiState  isDarkTheme <com.example.myapplication.ui.screen.settings.SettingsUiState  isNotificationsEnabled <com.example.myapplication.ui.screen.settings.SettingsUiState  showClearDataDialog <com.example.myapplication.ui.screen.settings.SettingsUiState  showExportDataDialog <com.example.myapplication.ui.screen.settings.SettingsUiState  successMessage <com.example.myapplication.ui.screen.settings.SettingsUiState  totalBalance <com.example.myapplication.ui.screen.settings.SettingsUiState  totalTransactions <com.example.myapplication.ui.screen.settings.SettingsUiState  totalWallets <com.example.myapplication.ui.screen.settings.SettingsUiState  Context >com.example.myapplication.ui.screen.settings.SettingsViewModel  ErrorHandler >com.example.myapplication.ui.screen.settings.SettingsViewModel  MutableStateFlow >com.example.myapplication.ui.screen.settings.SettingsViewModel  RepositoryProvider >com.example.myapplication.ui.screen.settings.SettingsViewModel  SettingsUiState >com.example.myapplication.ui.screen.settings.SettingsViewModel  _uiState >com.example.myapplication.ui.screen.settings.SettingsViewModel  asStateFlow >com.example.myapplication.ui.screen.settings.SettingsViewModel  clearAllData >com.example.myapplication.ui.screen.settings.SettingsViewModel  
clearMessages >com.example.myapplication.ui.screen.settings.SettingsViewModel  
exportData >com.example.myapplication.ui.screen.settings.SettingsViewModel  getErrorMessage >com.example.myapplication.ui.screen.settings.SettingsViewModel  hideClearDataDialog >com.example.myapplication.ui.screen.settings.SettingsViewModel  hideExportDataDialog >com.example.myapplication.ui.screen.settings.SettingsViewModel  invoke >com.example.myapplication.ui.screen.settings.SettingsViewModel  launch >com.example.myapplication.ui.screen.settings.SettingsViewModel  loadSettings >com.example.myapplication.ui.screen.settings.SettingsViewModel  loadStatistics >com.example.myapplication.ui.screen.settings.SettingsViewModel  logError >com.example.myapplication.ui.screen.settings.SettingsViewModel  provideTransactionRepository >com.example.myapplication.ui.screen.settings.SettingsViewModel  provideWalletRepository >com.example.myapplication.ui.screen.settings.SettingsViewModel  sharedPreferences >com.example.myapplication.ui.screen.settings.SettingsViewModel  showClearDataDialog >com.example.myapplication.ui.screen.settings.SettingsViewModel  showExportDataDialog >com.example.myapplication.ui.screen.settings.SettingsViewModel  uiState >com.example.myapplication.ui.screen.settings.SettingsViewModel  updateDarkTheme >com.example.myapplication.ui.screen.settings.SettingsViewModel  updateNotifications >com.example.myapplication.ui.screen.settings.SettingsViewModel  viewModelScope >com.example.myapplication.ui.screen.settings.SettingsViewModel  walletRepository >com.example.myapplication.ui.screen.settings.SettingsViewModel  
IconButton *com.example.myapplication.ui.screen.wallet  Add 5com.example.myapplication.ui.screen.wallet_management  AlertDialog 5com.example.myapplication.ui.screen.wallet_management  	Alignment 5com.example.myapplication.ui.screen.wallet_management  AndroidViewModel 5com.example.myapplication.ui.screen.wallet_management  Application 5com.example.myapplication.ui.screen.wallet_management  Arrangement 5com.example.myapplication.ui.screen.wallet_management  	ArrowBack 5com.example.myapplication.ui.screen.wallet_management  Boolean 5com.example.myapplication.ui.screen.wallet_management  Box 5com.example.myapplication.ui.screen.wallet_management  Button 5com.example.myapplication.ui.screen.wallet_management  ButtonDefaults 5com.example.myapplication.ui.screen.wallet_management  Card 5com.example.myapplication.ui.screen.wallet_management  CardDefaults 5com.example.myapplication.ui.screen.wallet_management  Check 5com.example.myapplication.ui.screen.wallet_management  CircleShape 5com.example.myapplication.ui.screen.wallet_management  CircularProgressIndicator 5com.example.myapplication.ui.screen.wallet_management  Color 5com.example.myapplication.ui.screen.wallet_management  Column 5com.example.myapplication.ui.screen.wallet_management  
Composable 5com.example.myapplication.ui.screen.wallet_management  CreateEditWalletDialog 5com.example.myapplication.ui.screen.wallet_management  CreateWalletFormState 5com.example.myapplication.ui.screen.wallet_management  Delete 5com.example.myapplication.ui.screen.wallet_management  Double 5com.example.myapplication.ui.screen.wallet_management  Edit 5com.example.myapplication.ui.screen.wallet_management  ErrorHandler 5com.example.myapplication.ui.screen.wallet_management  	Exception 5com.example.myapplication.ui.screen.wallet_management  ExperimentalMaterial3Api 5com.example.myapplication.ui.screen.wallet_management  FloatingActionButton 5com.example.myapplication.ui.screen.wallet_management  
FontWeight 5com.example.myapplication.ui.screen.wallet_management  Icon 5com.example.myapplication.ui.screen.wallet_management  
IconButton 5com.example.myapplication.ui.screen.wallet_management  Icons 5com.example.myapplication.ui.screen.wallet_management  Int 5com.example.myapplication.ui.screen.wallet_management  LaunchedEffect 5com.example.myapplication.ui.screen.wallet_management  
LazyColumn 5com.example.myapplication.ui.screen.wallet_management  LazyRow 5com.example.myapplication.ui.screen.wallet_management  List 5com.example.myapplication.ui.screen.wallet_management  Lock 5com.example.myapplication.ui.screen.wallet_management  LockOpen 5com.example.myapplication.ui.screen.wallet_management  
MaterialTheme 5com.example.myapplication.ui.screen.wallet_management  Modifier 5com.example.myapplication.ui.screen.wallet_management  MutableStateFlow 5com.example.myapplication.ui.screen.wallet_management  OptIn 5com.example.myapplication.ui.screen.wallet_management  OutlinedTextField 5com.example.myapplication.ui.screen.wallet_management  RepositoryProvider 5com.example.myapplication.ui.screen.wallet_management  Row 5com.example.myapplication.ui.screen.wallet_management  Scaffold 5com.example.myapplication.ui.screen.wallet_management  Spacer 5com.example.myapplication.ui.screen.wallet_management  	StateFlow 5com.example.myapplication.ui.screen.wallet_management  StatisticsSection 5com.example.myapplication.ui.screen.wallet_management  String 5com.example.myapplication.ui.screen.wallet_management  System 5com.example.myapplication.ui.screen.wallet_management  Text 5com.example.myapplication.ui.screen.wallet_management  
TextButton 5com.example.myapplication.ui.screen.wallet_management  	TopAppBar 5com.example.myapplication.ui.screen.wallet_management  TopAppBarDefaults 5com.example.myapplication.ui.screen.wallet_management  Unit 5com.example.myapplication.ui.screen.wallet_management  ValidationUtils 5com.example.myapplication.ui.screen.wallet_management  Wallet 5com.example.myapplication.ui.screen.wallet_management  WalletEntity 5com.example.myapplication.ui.screen.wallet_management  
WalletItem 5com.example.myapplication.ui.screen.wallet_management  WalletManagementScreen 5com.example.myapplication.ui.screen.wallet_management  WalletManagementUiState 5com.example.myapplication.ui.screen.wallet_management  WalletManagementViewModel 5com.example.myapplication.ui.screen.wallet_management  WalletRepository 5com.example.myapplication.ui.screen.wallet_management  _createFormState 5com.example.myapplication.ui.screen.wallet_management  _uiState 5com.example.myapplication.ui.screen.wallet_management  android 5com.example.myapplication.ui.screen.wallet_management  asStateFlow 5com.example.myapplication.ui.screen.wallet_management  
background 5com.example.myapplication.ui.screen.wallet_management  buttonColors 5com.example.myapplication.ui.screen.wallet_management  
cardColors 5com.example.myapplication.ui.screen.wallet_management  	clickable 5com.example.myapplication.ui.screen.wallet_management  clip 5com.example.myapplication.ui.screen.wallet_management  collectAsState 5com.example.myapplication.ui.screen.wallet_management  	emptyList 5com.example.myapplication.ui.screen.wallet_management  fillMaxSize 5com.example.myapplication.ui.screen.wallet_management  fillMaxWidth 5com.example.myapplication.ui.screen.wallet_management  formatCurrency 5com.example.myapplication.ui.screen.wallet_management  getErrorMessage 5com.example.myapplication.ui.screen.wallet_management  getValue 5com.example.myapplication.ui.screen.wallet_management  height 5com.example.myapplication.ui.screen.wallet_management  ifBlank 5com.example.myapplication.ui.screen.wallet_management  
isNotBlank 5com.example.myapplication.ui.screen.wallet_management  kotlinx 5com.example.myapplication.ui.screen.wallet_management  launch 5com.example.myapplication.ui.screen.wallet_management  let 5com.example.myapplication.ui.screen.wallet_management  listOf 5com.example.myapplication.ui.screen.wallet_management  loadStatistics 5com.example.myapplication.ui.screen.wallet_management  logError 5com.example.myapplication.ui.screen.wallet_management  padding 5com.example.myapplication.ui.screen.wallet_management  provideDelegate 5com.example.myapplication.ui.screen.wallet_management  provideWalletRepository 5com.example.myapplication.ui.screen.wallet_management  
sanitizeInput 5com.example.myapplication.ui.screen.wallet_management  size 5com.example.myapplication.ui.screen.wallet_management  spacedBy 5com.example.myapplication.ui.screen.wallet_management  toDouble 5com.example.myapplication.ui.screen.wallet_management  topAppBarColors 5com.example.myapplication.ui.screen.wallet_management  validateAmount 5com.example.myapplication.ui.screen.wallet_management  validateWalletName 5com.example.myapplication.ui.screen.wallet_management  walletRepository 5com.example.myapplication.ui.screen.wallet_management  weight 5com.example.myapplication.ui.screen.wallet_management  width 5com.example.myapplication.ui.screen.wallet_management  copy Kcom.example.myapplication.ui.screen.wallet_management.CreateWalletFormState  currency Kcom.example.myapplication.ui.screen.wallet_management.CreateWalletFormState  description Kcom.example.myapplication.ui.screen.wallet_management.CreateWalletFormState  initialBalance Kcom.example.myapplication.ui.screen.wallet_management.CreateWalletFormState  name Kcom.example.myapplication.ui.screen.wallet_management.CreateWalletFormState  
selectedColor Kcom.example.myapplication.ui.screen.wallet_management.CreateWalletFormState  selectedIcon Kcom.example.myapplication.ui.screen.wallet_management.CreateWalletFormState  activeWalletCount Mcom.example.myapplication.ui.screen.wallet_management.WalletManagementUiState  copy Mcom.example.myapplication.ui.screen.wallet_management.WalletManagementUiState  errorMessage Mcom.example.myapplication.ui.screen.wallet_management.WalletManagementUiState  	isLoading Mcom.example.myapplication.ui.screen.wallet_management.WalletManagementUiState  selectedWallet Mcom.example.myapplication.ui.screen.wallet_management.WalletManagementUiState  showCreateWalletDialog Mcom.example.myapplication.ui.screen.wallet_management.WalletManagementUiState  showDeleteConfirmDialog Mcom.example.myapplication.ui.screen.wallet_management.WalletManagementUiState  showEditWalletDialog Mcom.example.myapplication.ui.screen.wallet_management.WalletManagementUiState  successMessage Mcom.example.myapplication.ui.screen.wallet_management.WalletManagementUiState  totalBalance Mcom.example.myapplication.ui.screen.wallet_management.WalletManagementUiState  wallets Mcom.example.myapplication.ui.screen.wallet_management.WalletManagementUiState  CreateWalletFormState Ocom.example.myapplication.ui.screen.wallet_management.WalletManagementViewModel  ErrorHandler Ocom.example.myapplication.ui.screen.wallet_management.WalletManagementViewModel  MutableStateFlow Ocom.example.myapplication.ui.screen.wallet_management.WalletManagementViewModel  RepositoryProvider Ocom.example.myapplication.ui.screen.wallet_management.WalletManagementViewModel  System Ocom.example.myapplication.ui.screen.wallet_management.WalletManagementViewModel  ValidationUtils Ocom.example.myapplication.ui.screen.wallet_management.WalletManagementViewModel  WalletManagementUiState Ocom.example.myapplication.ui.screen.wallet_management.WalletManagementViewModel  _createFormState Ocom.example.myapplication.ui.screen.wallet_management.WalletManagementViewModel  _uiState Ocom.example.myapplication.ui.screen.wallet_management.WalletManagementViewModel  asStateFlow Ocom.example.myapplication.ui.screen.wallet_management.WalletManagementViewModel  availableColors Ocom.example.myapplication.ui.screen.wallet_management.WalletManagementViewModel  availableIcons Ocom.example.myapplication.ui.screen.wallet_management.WalletManagementViewModel  
clearMessages Ocom.example.myapplication.ui.screen.wallet_management.WalletManagementViewModel  createFormState Ocom.example.myapplication.ui.screen.wallet_management.WalletManagementViewModel  createWallet Ocom.example.myapplication.ui.screen.wallet_management.WalletManagementViewModel  deleteWallet Ocom.example.myapplication.ui.screen.wallet_management.WalletManagementViewModel  getErrorMessage Ocom.example.myapplication.ui.screen.wallet_management.WalletManagementViewModel  hideCreateWalletDialog Ocom.example.myapplication.ui.screen.wallet_management.WalletManagementViewModel  hideDeleteConfirmDialog Ocom.example.myapplication.ui.screen.wallet_management.WalletManagementViewModel  hideEditWalletDialog Ocom.example.myapplication.ui.screen.wallet_management.WalletManagementViewModel  ifBlank Ocom.example.myapplication.ui.screen.wallet_management.WalletManagementViewModel  invoke Ocom.example.myapplication.ui.screen.wallet_management.WalletManagementViewModel  launch Ocom.example.myapplication.ui.screen.wallet_management.WalletManagementViewModel  listOf Ocom.example.myapplication.ui.screen.wallet_management.WalletManagementViewModel  loadStatistics Ocom.example.myapplication.ui.screen.wallet_management.WalletManagementViewModel  loadWallets Ocom.example.myapplication.ui.screen.wallet_management.WalletManagementViewModel  logError Ocom.example.myapplication.ui.screen.wallet_management.WalletManagementViewModel  provideWalletRepository Ocom.example.myapplication.ui.screen.wallet_management.WalletManagementViewModel  
sanitizeInput Ocom.example.myapplication.ui.screen.wallet_management.WalletManagementViewModel  showCreateWalletDialog Ocom.example.myapplication.ui.screen.wallet_management.WalletManagementViewModel  showDeleteConfirmDialog Ocom.example.myapplication.ui.screen.wallet_management.WalletManagementViewModel  showEditWalletDialog Ocom.example.myapplication.ui.screen.wallet_management.WalletManagementViewModel  toDouble Ocom.example.myapplication.ui.screen.wallet_management.WalletManagementViewModel  toggleWalletFrozenStatus Ocom.example.myapplication.ui.screen.wallet_management.WalletManagementViewModel  uiState Ocom.example.myapplication.ui.screen.wallet_management.WalletManagementViewModel  updateFormBalance Ocom.example.myapplication.ui.screen.wallet_management.WalletManagementViewModel  updateFormColor Ocom.example.myapplication.ui.screen.wallet_management.WalletManagementViewModel  updateFormCurrency Ocom.example.myapplication.ui.screen.wallet_management.WalletManagementViewModel  updateFormDescription Ocom.example.myapplication.ui.screen.wallet_management.WalletManagementViewModel  updateFormIcon Ocom.example.myapplication.ui.screen.wallet_management.WalletManagementViewModel  updateFormName Ocom.example.myapplication.ui.screen.wallet_management.WalletManagementViewModel  updateWallet Ocom.example.myapplication.ui.screen.wallet_management.WalletManagementViewModel  validateAmount Ocom.example.myapplication.ui.screen.wallet_management.WalletManagementViewModel  validateWalletName Ocom.example.myapplication.ui.screen.wallet_management.WalletManagementViewModel  viewModelScope Ocom.example.myapplication.ui.screen.wallet_management.WalletManagementViewModel  walletRepository Ocom.example.myapplication.ui.screen.wallet_management.WalletManagementViewModel  validateWalletName /com.example.myapplication.utils.ValidationUtils  Pair kotlin  to kotlin  forEachIndexed kotlin.Array  plus kotlin.Float  invoke kotlin.Function0  times 
kotlin.Int  toFloat 
kotlin.Int  toString 
kotlin.Int  toInt kotlin.Long  
component1 kotlin.Pair  
component2 kotlin.Pair  to 
kotlin.String  
trimIndent 
kotlin.String  firstOrNull kotlin.collections  forEachIndexed kotlin.collections  
isNotEmpty kotlin.collections  firstOrNull kotlin.collections.List  
isNotEmpty kotlin.collections.List  size kotlin.collections.List  firstOrNull 
kotlin.ranges  firstOrNull kotlin.sequences  forEachIndexed kotlin.sequences  firstOrNull kotlin.text  forEachIndexed kotlin.text  
isNotEmpty kotlin.text  
trimIndent kotlin.text  SnackbarDuration !kotlinx.coroutines.CoroutineScope  System !kotlinx.coroutines.CoroutineScope  _createFormState !kotlinx.coroutines.CoroutineScope  categoryRepository !kotlinx.coroutines.CoroutineScope  firstOrNull !kotlinx.coroutines.CoroutineScope  
isNotBlank !kotlinx.coroutines.CoroutineScope  let !kotlinx.coroutines.CoroutineScope  loadStatistics !kotlinx.coroutines.CoroutineScope  validateWalletName !kotlinx.coroutines.CoroutineScope                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  